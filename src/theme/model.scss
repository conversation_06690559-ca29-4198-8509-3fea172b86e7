.model-container {
	.btn {
		@apply px-2 py-1 rounded-lg cursor-pointer hover:bg-[#F8FCFF] flex items-center justify-center;
	}

	.btn-active {
		@apply bg-[#F8FCFF];
	}

	.icon {
		@apply text-center min-w-[56px];
	}

	.hover-shadow {
		@apply hover:shadow-[0px_0px_5px_2px_rgba(175,217,255,0.63)];
	}

	.btn2-active {
		@apply shadow-[0px_0px_5px_2px_rgba(175,217,255,0.63)];
	}

	/* 下拉菜单样式 */
	.dropdown-menu {
		animation: dropdown-fade-in 0.15s ease-out;
	}

	.dropdown-item {
		transition: background-color 0.15s ease;
	}

	.dropdown-item-active {
		background-color: var(--el-color-primary-light-8) !important;
		color: var(--el-color-primary) !important;
	}

	.dropdown-item:first-child {
		border-radius: 0.375rem 0.375rem 0 0;
	}

	.dropdown-item:last-child {
		border-radius: 0 0 0.375rem 0.375rem;
	}

	@keyframes dropdown-fade-in {
		from {
			opacity: 0;
			transform: translateY(-4px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
}
