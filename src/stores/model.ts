import { defineStore } from 'pinia';

export const useModelStore = defineStore('model', {
	state: (): { ScenarioPackUpdatedRecords: Recordable[]; ConfiguredUpdatedRecords: Recordable[]; DeclarationUpdatedRecords: Recordable } => ({
		ScenarioPackUpdatedRecords: [],
		ConfiguredUpdatedRecords: [],
		DeclarationUpdatedRecords: {},
	}),
	actions: {
		setScenarioPackUpdatedRecords(packageKey: string, packageItemKey: string, records: Recordable[]) {
			const updatedRecord = this.ScenarioPackUpdatedRecords.find((item) => item.packageKey === packageKey && item.packageItemKey === packageItemKey);
			if (updatedRecord) {
				updatedRecord.data = records;
			} else {
				this.ScenarioPackUpdatedRecords.push({ packageKey, packageItemKey, data: records });
			}
		},
		getScenarioPackUpdatedRecords(packageKey: string, packageItemKey: string) {
			return this.ScenarioPackUpdatedRecords.find((item) => item.packageKey === packageKey && item.packageItemKey === packageItemKey)?.data || [];
		},
		clearScenarioPackUpdatedRecords() {
			this.ScenarioPackUpdatedRecords = [];
		},
		setConfiguredUpdatedRecords(packageKey: string, packageItemKey: string, records: Recordable[]) {
			const configuredRecord = this.ConfiguredUpdatedRecords.find((item) => item.packageKey === packageKey && item.packageItemKey === packageItemKey);
			if (configuredRecord) {
				configuredRecord.data = records;
			} else {
				this.ConfiguredUpdatedRecords.push({ packageKey, packageItemKey, data: records });
			}
		},
		getConfiguredUpdatedRecords(packageKey: string, packageItemKey: string) {
			return this.ConfiguredUpdatedRecords.find((item) => item.packageKey === packageKey && item.packageItemKey === packageItemKey)?.data || [];
		},
		clearConfiguredUpdatedRecords() {
			this.ConfiguredUpdatedRecords = [];
		},
		setDeclarationUpdatedRecords(key: string, records: Recordable[]) {
			this.DeclarationUpdatedRecords[key] = records;
		},
		getDeclarationUpdatedRecords(key: string) {
			return this.DeclarationUpdatedRecords[key] || [];
		},
		clearDeclarationUpdatedRecords(key: string) {
			this.DeclarationUpdatedRecords[key] = [];
		},
	},
});
