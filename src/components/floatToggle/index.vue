<template>
  <div class="float-toggle-container" :class="`position-${position}`">
    <div class="float-toggle-body">
      <div 
        v-for="option in options" 
        :key="option.value"
        class="toggle-item"
        :class="{ 
          'is-selected': modelValue === option.value,
          'is-disabled': option.disabled || disabled
        }"
        @click="handleItemClick(option)"
      >
        <div v-if="option.icon" class="item-icon">
          <i :class="option.icon"></i>
        </div>
        <span class="item-text">{{ option.label }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface FloatToggleOption {
  label: string;
  value: string;
  icon?: string;
  disabled?: boolean;
}

interface FloatToggleProps {
  /** 当前选中值 */
  modelValue: string;
  /** 选项列表 */
  options: FloatToggleOption[];
  /** 位置 */
  position?: 'left' | 'right';
  /** 是否禁用 */
  disabled?: boolean;
}

interface FloatToggleEmits {
  (e: 'update:modelValue', value: string): void;
  (e: 'change', value: string, option: FloatToggleOption): void;
}

// Props定义
const props = withDefaults(defineProps<FloatToggleProps>(), {
  position: 'left',
  disabled: false,
});

// Events定义
const emit = defineEmits<FloatToggleEmits>();

// 方法
const handleItemClick = (option: FloatToggleOption) => {
  if (option.disabled || props.disabled) return;
  
  emit('update:modelValue', option.value);
  emit('change', option.value, option);
};
</script>

<style lang="scss" scoped>
.float-toggle-container {
  position: absolute;
  top: 0px;
  z-index: 1000;
  
  &.position-left {
    left: 0px;
    // border-radius:  0 8px 8px 0 ;
    overflow: hidden;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  }
}

.float-toggle-body {
  background: #fff;
  display: flex;
  flex-direction: column;
}

.toggle-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  // padding: 4px 2px;
  min-height: 50px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:hover:not(.is-disabled) {
    background: #f5f7fa;
  }
  
  &.is-selected {
    background: #e6f7ff;
    
    .item-icon i {
      color: #409eff;
    }
    
    .item-text {
      color: #409eff;
      font-weight: 500;
    }
  }
  
  &.is-disabled {
    cursor: not-allowed;
    opacity: 0.5;
    
    .item-icon i,
    .item-text {
      color: #c0c4cc;
    }
  }
}

.item-icon {
  margin-bottom: 4px;
  
  i {
    font-size: 16px;
    color: #606266;
    transition: color 0.2s ease;
  }
}

.item-text {
  writing-mode: vertical-rl;
  font-size: 10px;
  color: #606266;
  text-align: center;
  transition: color 0.2s ease;
  white-space: nowrap;
}
</style> 