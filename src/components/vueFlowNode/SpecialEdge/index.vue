<script setup lang="ts">
import { BaseEdge, EdgeLabelRenderer, getBezierPath, type EdgeProps } from '@vue-flow/core';
import { computed, useAttrs } from 'vue';

// 定义组件的 props
const props = defineProps<EdgeProps>();

// 计算贝塞尔曲线路径
const path = computed(() => getBezierPath(props));

// 如果需要禁用继承属性，可以使用 `useAttrs` 钩子
const attrs = useAttrs();
</script>

<template>
	<!-- 使用 `BaseEdge` 组件来创建自定义边 -->
	<BaseEdge :path="path[0]" />

	<!-- 使用 `EdgeLabelRenderer` 渲染自定义标签 -->
	<EdgeLabelRenderer>
		<div
			:style="{
				pointerEvents: 'all',
				position: 'absolute',
				transform: `translate(-50%, -50%) translate(${path[1]}px,${path[2]}px)`,
			}"
			class="nodrag nopan"
		>
			{{ props.data.hello }}
		</div>
	</EdgeLabelRenderer>
</template>

<style>
/* these are necessary styles for vue flow */
@import '@vue-flow/core/dist/style.css';

/* this contains the default theme, these are optional styles */
@import '@vue-flow/core/dist/theme-default.css';
</style>
