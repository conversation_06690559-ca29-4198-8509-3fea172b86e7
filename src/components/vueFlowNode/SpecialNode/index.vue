<script setup lang="ts">
import { computed } from 'vue';
import { Posi<PERSON>, <PERSON>le } from '@vue-flow/core';
import type { NodeProps } from '@vue-flow/core';

const props = defineProps<NodeProps>();

const x = computed(() => `${Math.round(props.position.x)}px`);
const y = computed(() => `${Math.round(props.position.y)}px`);
</script>

<template>
	<div class="vue-flow__node-default">
		<div>{{ data.label }}</div>
		<Handle type="source" :position="Position.Bottom" />
	</div>
</template>

<style>
/* these are necessary styles for vue flow */
@import '@vue-flow/core/dist/style.css';

/* this contains the default theme, these are optional styles */
@import '@vue-flow/core/dist/theme-default.css';
</style>
