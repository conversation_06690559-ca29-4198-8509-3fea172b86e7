<template>
  <div class="side-strip-container" :class="[`position-${position}`, { compact }]">
    <div class="side-strip-body">
      <!-- 收起状态的细长条 -->
      <div class="strip-collapsed" v-show="!isExpanded" @click="togglePanel">
        <div class="strip-indicator">
          <div class="selected-text" v-if="selectedOption">
            {{ selectedOption.label }}
          </div>
          <el-icon :size="14">
            <ArrowRight v-if="position === 'left'" />
            <ArrowLeft v-else />
          </el-icon>
        </div>
      </div>
      
      <!-- 展开状态的完整面板 -->
      <div class="strip-expanded" v-show="isExpanded">
        <div class="strip-header">
          <span class="header-title">{{ title }}</span>
          <div class="header-close" @click="togglePanel">
            <el-icon :size="12">
              <ArrowLeft v-if="position === 'left'" />
              <ArrowRight v-else />
            </el-icon>
          </div>
        </div>
        
        <div class="strip-content">
          <el-segmented 
            :model-value="modelValue" 
            :options="options" 
            :disabled="disabled"
            @change="handleChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';
interface SideStripOption {
	label: string;
	value: string;
	disabled?: boolean;
}

interface SideStripProps {
	/** 标题 */
	title?: string;
	/** 当前选中值 */
	modelValue: string;
	/** 选项列表 */
	options: SideStripOption[];
	/** 位置 */
	position?: 'left' | 'right';
	/** 是否紧凑模式 */
	compact?: boolean;
	/** 是否禁用 */
	disabled?: boolean;
}

interface SideStripEmits {
	(e: 'update:modelValue', value: string): void;
	(e: 'change', value: string, option: SideStripOption): void;
}

// Props定义
const props = withDefaults(defineProps<SideStripProps>(), {
  title: '控制面板',
  position: 'left',
  compact: true,
  disabled: false,
});

// Events定义
const emit = defineEmits<SideStripEmits>();

// 响应式状态
const isExpanded = ref(true);

// 计算属性
const selectedOption = computed((): SideStripOption | undefined => {
  return props.options.find(option => option.value === props.modelValue);
});

// 方法
const togglePanel = () => {
  isExpanded.value = !isExpanded.value;
};

const handleChange = (value: string) => {
  emit('update:modelValue', value);
  const option = props.options.find(opt => opt.value === value);
  if (option) {
    emit('change', value, option);
  }
};
</script>

<style lang="scss" scoped>
.side-strip-container {
  position: fixed;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  
  &.position-left {
    left: 0;
  }
  
  &.position-right {
    right: 0;
    
    .side-strip-body {
      border-radius: 12px 0 0 12px;
      border-right: none;
      border-left: 1px solid rgba(228, 231, 237, 0.8);
    }
  }
  
  &.compact {
    .side-strip-body {
      height: 85px;
    }
    
    .strip-expanded {
      width: 100px;
    }
    
    .strip-header {
      padding: 6px 6px 6px;
    }
    
    .strip-content {
      //padding: 12px;
    }
  }
}

.side-strip-body {
  position: relative;
  height: 140px;
  background: rgba(255, 255, 255, 0.96);
  backdrop-filter: blur(8px);
  border-radius: 0 12px 12px 0;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(228, 231, 237, 0.8);
  border-left: none;
  overflow: hidden;
}

// 收起状态的细长条
.strip-collapsed {
  width: 28px;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.25s ease;
  
  &:hover {
    background: rgba(64, 158, 255, 0.06);
  }
}

.strip-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #909399;
  transition: all 0.25s ease;
  
  &:hover {
    color: #409eff;
    transform: translateX(2px);
  }
}

.selected-text {
  writing-mode: vertical-rl;
  font-size: 11px;
  font-weight: 500;
  color: #606266;
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.2;
}

// 展开状态的完整面板
.strip-expanded {
  width: 100px;
  height: 100%;
  display: flex;
  flex-direction: column;
  animation: slideIn 0.3s ease-out;
}

.strip-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 14px 14px 10px;
  border-bottom: 1px solid #f0f0f0;
}

.header-title {
  font-size: 12px;
  font-weight: 500;
  color: #606266;
}

.header-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #f5f7fa;
    
    .el-icon {
      color: #409eff;
    }
  }
  
  .el-icon {
    color: #c0c4cc;
    transition: all 0.2s ease;
  }
}

.strip-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  
  :deep(.el-segmented) {
    border-radius: 5px;
    border: 1px solid #e4e7ed;
    background: #fafafa;
    
    .el-segmented__item {
      border-radius: 3px;
      transition: all 0.2s ease;
      font-size: 12px;
      padding: 4px 8px;
      
      &:hover {
        background: rgba(64, 158, 255, 0.06);
      }
      
      &.is-selected {
        background: #409eff;
        color: white;
      }
    }
  }
}

// 简化的动画
@keyframes slideIn {
  from {
    width: 28px;
    opacity: 0.7;
  }
  to {
    width: 100px;
    opacity: 1;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .side-strip-container {
    top: 20px;
    transform: none;
    
    &.compact .side-strip-body {
      height: 100px;
    }
    
    &.compact .strip-expanded {
      width: 140px;
    }
  }
}
</style> 