<template>
	<div class="flex-1 flex flex-col overflow-hidden" v-loading="loading" element-loading-text="数据加载中...">
		<div class="mt-2" v-if="isSearch">
			<el-input class="mb-2" v-model="searchValue" placeholder="搜索" :suffix-icon="Search"> </el-input>
		</div>
		<el-scrollbar class="flex-1 flex flex-col">
			<el-tree
				class="tree-bg-color"
				ref="treeRef"
				:data="data"
				:props="{
					children: props.props.children,
					label: props.props.label,
				}"
				default-expand-all
				:expand-on-click-node="false"
				@node-click="handleNodeClick"
				:filter-node-method="filterNode"
			>
				<template #default="{ node, data }">
					<div :class="{ 'select-bg-color': selected === data[props.props.value], 'text-gray-400': !data[props.props.value] }">
						{{ data[props.props.label] }}
						<!-- {{ data[props.props.value] }} -->
					</div>
				</template>
				<!-- <template #empty>
					<el-empty description="暂无数据" />
				</template> -->
			</el-tree>
		</el-scrollbar>
	</div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { Search } from '@element-plus/icons-vue';
import { ElTree } from 'element-plus';
import XEUtils from 'xe-utils';

const props = withDefaults(
	defineProps<{
		data: Recordable[];
		isSearch?: boolean;
		loading?: boolean;
		props?: {
			children?: string;
			label: string;
			value: string;
			disabled?: boolean | ((data: Recordable) => boolean);
		};
	}>(),
	{
		data: () => [],
		isSearch: true,
		loading: false,
		props: () => ({
			children: 'children',
			label: 'name',
			value: 'key',
			disabled: false,
		}),
	}
);

const emit = defineEmits<{
	(e: 'node-click', data: Recordable): void;
}>();

const selected = defineModel<string>({
	default: '',
});

const searchValue = ref('');
const treeRef = ref<InstanceType<typeof ElTree>>();

watch(
	() => searchValue.value,
	(val) => {
		debouncedFilter(treeRef.value, val);
	}
);

const filterNode = (value: string, data: any) => {
	if (!value) return true;
	return data[props.props.label].includes(value);
};

const debouncedFilter = XEUtils.debounce((ref, val) => {
	ref.filter(val);
}, 300);

const handleNodeClick = (data: Recordable) => {
	emit('node-click', data);
};
</script>
