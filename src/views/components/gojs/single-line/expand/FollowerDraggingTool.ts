import * as go from 'gojs';

/**
 * 自定义拖拽工具 - 实现主节点移动时关联节点跟随
 *
 * 功能特点：
 * 1. 当拖拽主节点时，其标签节点和属性节点会自动跟随移动
 * 2. 支持独立拖拽标签和属性节点，不影响主节点
 * 3. 维护精确的相对位置关系
 */
export class FollowerDraggingTool extends go.DraggingTool {
	/**
	 * 重写 computeEffectiveCollection 方法
	 * 当拖拽主节点时，自动将其关联的标签节点和属性节点加入拖拽集合
	 */
	override computeEffectiveCollection(parts: go.Set<go.Part>, options?: any): go.Map<go.Part, go.DraggingInfo> {
		// 先获取默认的拖拽集合
		const map = super.computeEffectiveCollection(parts, options);

		// 定义主节点的类型（设备类型）
		const mainNodeTypes = ['BusbarSection', 'Load', 'Synchronousmachine', 'Trafo', 'Trafo3w', 'ShuntCompensator', 'ACLineDot', 'Acline'];

		// 遍历所有被拖拽的部件
		parts.each((part) => {
			// === 关键修正：只有设备类型的节点才是主节点 ===
			if (part instanceof go.Node && part.data && mainNodeTypes.includes(part.data.category)) {
				const mainNodeKey = part.data.key;

				// 找到关联的标签节点并加入拖拽集合
				const labelNode = this.diagram.findNodeForKey(mainNodeKey + '-label');
				if (labelNode && !map.has(labelNode)) {
					map.add(labelNode, {
						point: labelNode.location.copy(),
						shifted: new go.Point(),
					});
				}

				// 找到关联的属性节点并加入拖拽集合
				const propertyNode = this.diagram.findNodeForKey(mainNodeKey + '-property');
				if (propertyNode && !map.has(propertyNode)) {
					map.add(propertyNode, {
						point: propertyNode.location.copy(),
						shifted: new go.Point(),
					});
				}

				// 找到关联的连接点标记并加入拖拽集合（新增）
				if (part.data.category === 'BusbarSection') {
					// 对于母线节点，找到所有相关的连接点标记
					this.diagram.nodes.each((node) => {
						if (node.data?.category === 'ConnectionMarker' && node.data?.parentNodeId === mainNodeKey) {
							if (!map.has(node)) {
								map.add(node, {
									point: node.location.copy(),
									shifted: new go.Point(),
								});
							}
						}
					});
				}
			}
		});

		return map;
	}
}
