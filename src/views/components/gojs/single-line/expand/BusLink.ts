/*
 * BusLink - 自定义连线类，专门处理与母线(Busbar)的智能连接
 * 基于GoJS BarLink示例设计，自动计算到母线的最佳连接点
 */

import * as go from 'gojs';

/**
 * BusLink类：智能处理与母线节点的连接
 *
 * 功能特点：
 * 1. 自动计算到母线最近的连接点
 * 2. 如果连接点超出母线范围，自动连接到母线端点
 * 3. 保持垂直连接方向，提供美观的连线效果
 * 4. 在母线连接点显示特殊标记符号
 */
export class BusLink extends go.Link {
	/**
	 * 构造函数
	 */
	constructor(init?: Partial<BusLink>) {
		super();
		if (init) Object.assign(this, init);
	}

	/**
	 * 重写computeSpot方法
	 * 为母线连接计算合适的连接方向
	 * @param from - 是否为起点
	 * @returns 连接的Spot方向
	 */
	override computeSpot(from: boolean): go.Spot {
		// 检查是否连接到母线
		if (from && this.toNode && this.toNode.data?.type === 'BusbarSection') {
			return go.Spot.TopBottomSides; // 连接到母线时使用上下方向
		}
		if (!from && this.fromNode && this.fromNode.data?.type === 'BusbarSection') {
			return go.Spot.TopBottomSides; // 从母线连出时使用上下方向
		}
		return super.computeSpot(from);
	}

	/**
	 * 重写getLinkPoint方法
	 * 智能计算与母线的连接点位置
	 * @param node - 连接的节点
	 * @param port - 连接的端口
	 * @param spot - 连接点方向
	 * @param from - 是否为起点
	 * @param ortho - 是否正交
	 * @param othernode - 另一端节点
	 * @param otherport - 另一端端口
	 * @returns 连接点坐标
	 */
	override getLinkPoint(
		node: go.Node,
		port: go.GraphObject,
		spot: go.Spot,
		from: boolean,
		ortho: boolean,
		othernode: go.Node,
		otherport: go.GraphObject
	): go.Point {
		// 如果连接的不是母线，使用默认计算
		if (!node.data || node.data.type !== 'BusbarSection') {
			return super.getLinkPoint(node, port, spot, from, ortho, othernode, otherport);
		}

		// === 母线连接的智能计算 ===

		// 1. 获取另一端节点的连接点位置
		const otherPoint = super.getLinkPoint(othernode, otherport, this.computeSpot(!from), !from, ortho, node, port);

		// 2. 获取母线的矩形边界
		const busRect = port.getDocumentBounds();

		// 3. 根据另一端节点位置确定连接到母线的上方还是下方
		const connectToTop = otherPoint.y < busRect.centerY;
		const connectY = connectToTop ? busRect.top : busRect.bottom;

		// 4. 计算水平连接位置
		let connectX: number;

		if (otherPoint.x < busRect.left) {
			// 另一端在母线左侧，连接到母线左端点
			connectX = busRect.left;
		} else if (otherPoint.x > busRect.right) {
			// 另一端在母线右侧，连接到母线右端点
			connectX = busRect.right;
		} else {
			// 另一端在母线水平范围内，连接到对应的水平位置
			connectX = otherPoint.x;
		}

		const connectionPoint = new go.Point(connectX, connectY);

		// 在母线上添加连接点标记
		this.addConnectionMarker(node, connectionPoint, otherPoint);

		return connectionPoint;
	}

	/**
	 * 在母线上添加连接点标记
	 * @param busNode 母线节点
	 * @param connectionPoint 连接点坐标
	 * @param otherPoint 另一端节点坐标
	 */
	private addConnectionMarker(busNode: go.Node, connectionPoint: go.Point, otherPoint: go.Point) {
		if (!busNode || busNode.data?.type !== 'BusbarSection') return;

		const diagram = busNode.diagram;
		if (!diagram) return;

		// 创建连接点标记的唯一key
		const markerId = `${busNode.data.key}-marker-${connectionPoint.x.toFixed(0)}-${connectionPoint.y.toFixed(0)}`;

		// 检查是否已经存在该标记
		const existingMarker = diagram.findNodeForKey(markerId);
		if (existingMarker) return;

		// 计算标记在母线上的相对位置
		const busCenter = busNode.location;
		const relativeX = connectionPoint.x - busCenter.x;

		// 创建连接点标记节点
		const markerData = {
			key: markerId,
			category: 'ConnectionMarker',
			parentNodeId: busNode.data.key,
			relativeX: relativeX,
			connectionPoint: connectionPoint,
			visible: true,
		};

		// 添加标记节点到图表
		diagram.model.addNodeData(markerData);
	}

	/**
	 * 重写getLinkDirection方法
	 * 为母线连接提供合适的连接方向角度
	 * @param node - 连接的节点
	 * @param port - 连接的端口
	 * @param linkpoint - 连接点坐标
	 * @param spot - 连接点方向
	 * @param from - 是否为起点
	 * @param ortho - 是否正交
	 * @param othernode - 另一端节点
	 * @param otherport - 另一端端口
	 * @returns 连接方向角度
	 */
	override getLinkDirection(
		node: go.Node,
		port: go.GraphObject,
		linkpoint: go.Point,
		spot: go.Spot,
		from: boolean,
		ortho: boolean,
		othernode: go.Node,
		otherport: go.GraphObject
	): number {
		// 检查是否涉及母线连接
		const isBusConnection = node.data?.type === 'BusbarSection' || othernode.data?.type === 'BusbarSection';

		if (isBusConnection) {
			// 计算两个端口的中心位置
			const nodeCenter = port.getDocumentPoint(go.Spot.Center);
			const otherCenter = otherport.getDocumentPoint(go.Spot.Center);

			// 根据相对位置确定连接方向
			const isOtherBelow = otherCenter.y > nodeCenter.y;
			return isOtherBelow ? 90 : 270; // 90度向下，270度向上
		}

		// 非母线连接使用默认方向计算
		return super.getLinkDirection(node, port, linkpoint, spot, from, ortho, othernode, otherport);
	}
}
