import { ref, computed } from 'vue';
import * as go from 'gojs';
import icons from '../icons';
import { getVoltageColor } from '/@/config/GraphConfig';

/**
 * 节点管理Hook
 * 负责节点选择、属性更新、端口管理、标签节点等功能
 */
export function useNodeManagement() {
	// ===== 状态管理 =====
	const selectedNode = ref<any>(null);
	const nodeText = ref<string>('');
	const nodeColor = ref<string>('#1c57ea');

	/**
	 * 控制节点端口的可见性
	 */
	const showPorts = (node: go.Node, show: boolean, diagram: go.Diagram | null) => {
		if (!node || node.isLinkLabel || !diagram) return;

		// 排除LabelNode和PropertyNode，它们没有端口
		if (node.data?.category === 'LabelNode' || node.data?.category === 'PropertyNode') {
			return;
		}

		// 确保在事务中进行属性修改
		const needTransaction = !diagram.undoManager.isInTransaction;
		if (needTransaction) {
			diagram.startTransaction('update ports');
		}

		try {
			node.ports.each((port: go.GraphObject) => {
				if (port.portId !== '') {
					const portShape = port as go.Shape;
					if (show) {
						portShape.fill = 'black';
						portShape.desiredSize = new go.Size(3, 3);
					} else {
						portShape.fill = 'transparent';
						portShape.desiredSize = new go.Size(1, 1);
					}
				}
			});
		} finally {
			if (needTransaction) {
				diagram.commitTransaction('update ports');
			}
		}
	};

	/**
	 * 隐藏图表中所有节点的端口
	 */
	const hideAllPorts = (diagram: go.Diagram) => {
		if (!diagram) return;

		diagram.startTransaction('hide all ports');
		try {
			diagram.nodes.each((node: go.Node) => {
				// 排除LabelNode和PropertyNode，避免误操作
				if (!node.isLinkLabel && node.data?.category !== 'PropertyNode' && node.data?.category !== 'LabelNode') {
					node.ports.each((port: go.GraphObject) => {
						if (port.portId !== '') {
							const portShape = port as go.Shape;
							portShape.fill = 'transparent';
							portShape.desiredSize = new go.Size(1, 1);
						}
					});
				}
			});
		} finally {
			diagram.commitTransaction('hide all ports');
		}
	};

	/**
	 * 处理节点选择事件
	 */
	const handleSelectionChanged = (e: go.DiagramEvent, emit?: (event: string, ...args: any[]) => void) => {
		const selected = e.diagram.selection.first();

		// 首先隐藏所有端口
		hideAllPorts(e.diagram);

		if (selected instanceof go.Node) {
			// LabelNode节点特殊处理
			if (selected.data && selected.data.category === 'LabelNode') {
				selectedNode.value = selected.data;
				nodeText.value = selected.data.text || 'LabelNode节点';
				nodeColor.value = selected.data.color || '#000000';

				console.log('选中LabelNode节点:', selected.data);
				emit?.('nodeSelected', selected.data);
				return;
			}

			// PropertyNode节点特殊处理
			if (selected.data && selected.data.category === 'PropertyNode') {
				selectedNode.value = selected.data;
				nodeText.value = selected.data.properties || 'PropertyNode节点';
				nodeColor.value = selected.data.color || '#FFFF99';

				console.log('选中PropertyNode节点:', selected.data);
				emit?.('nodeSelected', selected.data);
				return;
			}

			// 普通节点被选中，显示其端口
			showPorts(selected, true, e.diagram);

			selectedNode.value = selected.data;
			nodeText.value = selected.data.name || '';
			nodeColor.value = selected.data.color || '#1c57ea';

			console.log('选中节点:', {
				key: selected.data.key,
				name: selected.data.name,
				type: selected.data.type,
				angle: selected.data.angle,
				pos: selected.data.pos,
				properties: selected.data.properties,
			});
			emit?.('nodeSelected', selected.data);
		} else {
			// 没有节点被选中
			selectedNode.value = null;
			console.log('取消选中节点');
			emit?.('nodeSelected', null);
		}
	};

	/**
	 * 处理节点移动事件（智慧跟随逻辑）
	 */
	const handleSelectionMoved = (e: go.DiagramEvent) => {
		const diagram = e.diagram;
		if (!diagram) return;

		diagram.startTransaction('update follower offsets');

		e.diagram.selection.each((part) => {
			if (!(part instanceof go.Node)) return;
			const node = part;

			// 只处理当标签、属性框或连接点标记被独立移动时的情况
			if (node.data.category === 'LabelNode' || node.data.category === 'PropertyNode') {
				// 推断出它所依附的核心图形的key
				const mainNodeKey = node.data.key.replace('-label', '').replace('-property', '');
				const mainNode = diagram.findNodeForKey(mainNodeKey);

				if (mainNode && diagram) {
					// 计算并保存新的相对偏移量
					const newOffsetX = node.location.x - mainNode.location.x;
					const newOffsetY = node.location.y - mainNode.location.y;

					// 保存偏移量到节点数据中
					diagram.model.setDataProperty(node.data, 'offsetX', newOffsetX);
					diagram.model.setDataProperty(node.data, 'offsetY', newOffsetY);

					console.log(`更新 ${node.data.category} 偏移量: (${newOffsetX}, ${newOffsetY})`);
				}
			}
			// 处理ConnectionMarker被独立移动的情况
			else if (node.data.category === 'ConnectionMarker' && node.data.parentNodeId) {
				const busNode = diagram.findNodeForKey(node.data.parentNodeId);

				if (busNode && diagram) {
					const busCenter = busNode.location;
					const busBounds = busNode.actualBounds;

					// 计算新的相对位置
					const newRelativeX = node.location.x - busCenter.x;
					const newIsTop = node.location.y < busCenter.y;

					// 保存相对位置到节点数据中
					diagram.model.setDataProperty(node.data, 'relativeX', newRelativeX);
					diagram.model.setDataProperty(node.data, 'isTop', newIsTop);

					console.log(`更新 ConnectionMarker 相对位置: relativeX=${newRelativeX}, isTop=${newIsTop}`);
				}
			}

			// 更新连线路径
			node.findLinksConnected().each((link) => {
				if (link instanceof go.Link) {
					link.updateRoute();
				}
			});
		});

		if (diagram) {
			diagram.commitTransaction('update follower offsets');
		}
	};

	/**
	 * 更新LabelNode和PropertyNode的位置，基于其父节点的位置
	 */
	const updateFollowerNodePositions = (diagram: go.Diagram) => {
		if (!diagram) return;

		diagram.startTransaction('update label and property positions');
		try {
			diagram.nodes.each((node: go.Node) => {
				// 处理PropertyNode位置更新
				if (node.data?.category === 'PropertyNode' && node.data?.parentNodeId) {
					const parentNode = diagram.findNodeForKey(node.data.parentNodeId);
					if (parentNode) {
						const parentBounds = parentNode.actualBounds;
						// 计算PropertyNode的位置：在父节点右侧60像素处
						const newLoc = new go.Point(parentBounds.right + 60, parentBounds.centerY);
						node.location = newLoc;
					}
				}

				// 处理LabelNode位置更新
				if (node.data?.category === 'LabelNode' && node.data?.parentNodeId) {
					const parentNode = diagram.findNodeForKey(node.data.parentNodeId);
					if (parentNode) {
						const parentBounds = parentNode.actualBounds;
						// 使用保存的偏移量或默认偏移量
						const offsetX = node.data.offsetX || 0;
						const offsetY = node.data.offsetY || 10;
						const newLoc = new go.Point(parentBounds.centerX + offsetX, parentBounds.bottom + Math.abs(offsetY));
						node.location = newLoc;

						console.log(`更新LabelNode位置: ${node.data.key}, 父节点: ${node.data.parentNodeId}, 新位置: (${newLoc.x}, ${newLoc.y})`);
					}
				}

				// 处理ConnectionMarker位置更新（集成到LabelNode系统中）
				if (node.data?.category === 'ConnectionMarker' && node.data?.parentNodeId) {
					const busNode = diagram.findNodeForKey(node.data.parentNodeId);
					if (busNode) {
						const busBounds = busNode.actualBounds;
						const busCenter = busNode.location;

						// 使用保存的相对位置信息
						const relativeX = node.data.relativeX || 0;
						const isTop = node.data.isTop !== undefined ? node.data.isTop : true;

						// 计算新的绝对位置
						const newX = busCenter.x + relativeX;
						const newY = isTop ? busBounds.top : busBounds.bottom;
						const newLoc = new go.Point(newX, newY);

						// 更新标记位置
						node.location = newLoc;

						// 更新标记颜色，跟随母线颜色
						const busColor = busNode.data.color || '#1c57ea';
						if (node.data.color !== busColor) {
							diagram.model.setDataProperty(node.data, 'color', busColor);
						}

						console.log(
							`更新ConnectionMarker位置: ${node.data.key}, 父节点: ${node.data.parentNodeId}, 新位置: (${newLoc.x}, ${newLoc.y}), 颜色: ${busColor}`
						);
					}
				}
			});
		} finally {
			diagram.commitTransaction('update label and property positions');
		}
	};

	/**
	 * 更新节点颜色
	 */
	const updateNodeColor = (diagram: go.Diagram | null) => {
		if (!diagram || !selectedNode.value) return;

		diagram.startTransaction('change color');
		const nodeData = diagram.model.findNodeDataForKey(selectedNode.value.key);
		if (nodeData) {
			diagram.model.setDataProperty(nodeData, 'color', nodeColor.value);
		}
		diagram.commitTransaction('change color');
	};

	/**
	 * 更新节点文本
	 */
	const updateNodeText = (diagram: go.Diagram | null) => {
		if (!diagram || !selectedNode.value) return;

		diagram.startTransaction('change text');
		const nodeData = diagram.model.findNodeDataForKey(selectedNode.value.key);
		if (nodeData) {
			// LabelNode节点使用text属性
			if (nodeData.category === 'LabelNode') {
				diagram.model.setDataProperty(nodeData, 'text', nodeText.value);
			}
			// PropertyNode节点使用properties属性
			else if (nodeData.category === 'PropertyNode') {
				diagram.model.setDataProperty(nodeData, 'properties', nodeText.value);
			}
			// 普通节点使用name属性
			else {
				diagram.model.setDataProperty(nodeData, 'name', nodeText.value);
			}
		}
		diagram.commitTransaction('change text');
	};

	/**
	 * 处理从组件库拖拽到画布的节点
	 */
	const handleExternalObjectsDropped = (e: go.DiagramEvent) => {
		const diagram = e.diagram;
		const selection = diagram.selection;

		// 获取鼠标当前位置
		const documentPoint = diagram.lastInput.documentPoint;

		// 检查是否有节点被拖入
		selection.each((part) => {
			if (part instanceof go.Node) {
				// 开始事务，确保原子操作
				diagram.startTransaction('update dropped node');

				// 获取节点数据
				const nodeData = part.data;

				// 获取节点位置 - 使用确切的拖放位置
				const newPos = [documentPoint.x, documentPoint.y];
				diagram.model.setDataProperty(nodeData, 'pos', newPos);

				// 移除isTemplate标记
				diagram.model.setDataProperty(nodeData, 'isTemplate', false);

				// 为拖放的节点添加标签位置配置
				if (!nodeData.labelAlignment) {
					diagram.model.setDataProperty(nodeData, 'labelAlignment', go.Spot.stringify(new go.Spot(0.5, 1, 0, 10)));
				}

				// 确保使用原始组件的宽度和高度
				const iconInfo = icons[nodeData.type];
				if (iconInfo && nodeData.type === 'BusbarSection') {
					// 为母线设置正确的宽度
					const defaultBusWidth = 150;
					const width = typeof nodeData.width === 'number' && nodeData.width > 0 ? nodeData.width : defaultBusWidth;
					diagram.model.setDataProperty(nodeData, 'width', width);
					console.log('拖入母线节点，设置宽度:', width, '节点ID:', nodeData.key);
				}

				// 确保使用正确的颜色
				if (iconInfo) {
					diagram.model.setDataProperty(nodeData, 'color', iconInfo.defaultColor);
				}

				// 确保节点数据包含一个唯一的key
				const nodeKey = nodeData.key;
				let finalNodeKey = nodeKey;
				if (typeof nodeKey === 'string' && nodeKey.startsWith('palette-')) {
					const uniqueId = `node-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
					diagram.model.setDataProperty(nodeData, 'key', uniqueId);
					finalNodeKey = uniqueId;
				}

				// 提交事务
				diagram.commitTransaction('update dropped node');

				// 为拖拽的节点创建LabelNode
				setTimeout(() => {
					if (diagram.model instanceof go.GraphLinksModel) {
						// 创建LabelNode数据
						const labelKey = finalNodeKey + '-label';
						const labelOffsetX = 0;
						const labelOffsetY = 30;
						const labelPos = new go.Point(newPos[0] + labelOffsetX, newPos[1] + labelOffsetY);

						const labelNodeData = {
							key: labelKey,
							text: nodeData.name || nodeData.type || '未命名节点',
							loc: go.Point.stringify(labelPos),
							category: 'LabelNode',
							visible: true,
							parentNodeId: finalNodeKey,
							color: iconInfo?.defaultColor || '#000000',
							offsetX: labelOffsetX,
							offsetY: labelOffsetY,
						};

						// 添加LabelNode到模型
						diagram.model.addNodeData(labelNodeData);

						console.log('为拖拽节点创建LabelNode:', labelNodeData);
					}
				}, 50);

				console.log('节点已拖入位置:', newPos, `已添加组件: ${nodeData.name}`, '类型:', nodeData.type);
			}
		});
	};

	/**
	 * 清除选择状态
	 */
	const clearSelection = () => {
		selectedNode.value = null;
		nodeText.value = '';
		nodeColor.value = '#1c57ea';
	};

	return {
		// 状态
		selectedNode: computed(() => selectedNode.value),
		nodeText: computed({
			get: () => nodeText.value,
			set: (value: string) => {
				nodeText.value = value;
			},
		}),
		nodeColor: computed({
			get: () => nodeColor.value,
			set: (value: string) => {
				nodeColor.value = value;
			},
		}),

		// 方法
		showPorts,
		hideAllPorts,
		handleSelectionChanged,
		handleSelectionMoved,
		updateFollowerNodePositions,
		updateNodeColor,
		updateNodeText,
		handleExternalObjectsDropped,
		clearSelection,
	};
}
