import { ref, shallowRef, computed } from 'vue';
import * as go from 'gojs';
import { BusResizeMultipleTool } from '../expand/BusResizeMultipleTool';
import { BusLink } from '../expand/BusLink';
import { FollowerDraggingTool } from '../expand/FollowerDraggingTool';
import { setupAllNodeTemplates } from '../templates';

/**
 * 图表管理Hook
 * 负责GoJS图表的初始化、配置、模板设置和基础事件处理
 */
export function useDiagramManagement() {
	// ===== 状态管理 =====
	const isInitialized = ref<boolean>(false);
	const diagramInstance = shallowRef<go.Diagram | null>(null);
	const lastPortOver = ref<go.GraphObject | null>(null);

	// GoJS实例
	let myDiagram: go.Diagram | null = null;
	let $: any = null;

	/**
	 * 设置PropertyNode模板
	 */
	const setupPropertyNodeTemplate = (diagram: go.Diagram) => {
		const $ = go.GraphObject.make;

		// PropertyNode模板 - 独立的属性显示节点
		diagram.nodeTemplateMap.add(
			'PropertyNode',
			$(
				go.Node,
				'Auto',
				{
					locationSpot: go.Spot.Center,
					isLayoutPositioned: false,
					movable: true,
					copyable: false,
					deletable: false,
					selectable: true,
					pickable: true,
					fromLinkable: false,
					toLinkable: false,
				},
				new go.Binding('location', 'loc', go.Point.parse).makeTwoWay(go.Point.stringify),
				$(
					go.Shape,
					'RoundedRectangle',
					{
						fill: 'transparent',
						stroke: 'gray',
						strokeWidth: 1,
						strokeDashArray: [5, 3],
					},
					new go.Binding('stroke', 'color')
				),
				$(
					go.TextBlock,
					{
						margin: 4,
						textAlign: 'left',
						font: '12px sans-serif',
						isMultiline: true,
						wrap: go.TextBlock.WrapFit,
						maxSize: new go.Size(150, NaN),
					},
					new go.Binding('text', 'properties'),
					new go.Binding('stroke', 'color')
				)
			)
		);

		// AnnotationLink模板 - 连接主节点和附属节点的连线
		diagram.linkTemplateMap.add(
			'AnnotationLink',
			$(
				go.Link,
				{
					routing: go.Link.Normal,
					selectable: false,
					pickable: false,
					layerName: 'Background',
					fromSpot: go.Spot.Center,
					toSpot: go.Spot.Center,
				},
				$(
					go.Shape,
					{
						stroke: 'gray',
						strokeWidth: 1,
						strokeDashArray: [2, 3],
					},
					new go.Binding('stroke', 'color')
				)
			)
		);
	};

	/**
	 * 设置连线模板
	 */
	const setupLinkTemplate = (diagram: go.Diagram) => {
		const $ = go.GraphObject.make;

		diagram.linkTemplate = $(
			BusLink,
			{
				routing: go.Link.Orthogonal,
				relinkableFrom: true,
				relinkableTo: true,
				adjusting: go.Link.None,
				corner: 1,
				curve: go.Link.None,
				reshapable: true,
			},
			// 连线主体
			$(go.Shape, {
				name: 'SHAPE',
				strokeWidth: 2,
			}).bind('stroke', 'color'),

			$(go.Shape, 'Circle', {
				width: 8,
				height: 8,
				fill: 'white',
				strokeWidth: 2,
				segmentIndex: -1, // 在连线末端
				segmentFraction: 1, // 在末端位置
			})
				.bind(new go.Binding('visible', 'toNode', (toNode) => toNode && toNode.data?.type === 'BusbarSection'))
				.bind(
					new go.Binding('stroke', 'toNode', (toNode) => {
						if (toNode && toNode.data?.type === 'BusbarSection') {
							return toNode.data.color || '#1c57ea';
						}
						return 'black';
					})
				)
				// 位置绑定
				.bind(
					new go.Binding('segmentOffset', 'points', (points, link) => {
						// 确认是连接到母线，并且连线至少有两个点
						if (link.toNode && link.toNode.data?.type === 'BusbarSection' && points.count >= 2) {
							const secondLastPoint = points.get(points.count - 2);
							const lastPoint = points.last();

							console

							// 偏移量为圆圈半径 (8 / 2 = 4)
							const offset = 4;

							// 如果连线从上方来 (Y坐标变大)，则将圆圈向下偏移
							if (secondLastPoint.y < lastPoint.y) {
								return new go.Point(0, offset);
							} else {
								// 否则连线从下方来，将圆圈向上偏移
								return new go.Point(0, -offset);
							}
						}
						// 其他情况不偏移
						return new go.Point(0, 0);
					})
				)
		);
	};

	/**
	 * 设置连接验证
	 */
	const setupConnectionValidation = (diagram: go.Diagram) => {
		const validateConnection = function (fromNode: go.Node, fromPort: go.GraphObject, toNode: go.Node, toPort: go.GraphObject): boolean {
			// 禁止任何涉及PropertyNode节点的手动连线操作
			if ((fromNode.data && fromNode.data.category === 'PropertyNode') || (toNode.data && toNode.data.category === 'PropertyNode')) {
				console.log('连接被拒绝：禁止手动连接PropertyNode节点');
				return false;
			}

			// 普通连线必须有端口才能连接
			if (!fromPort || !toPort) {
				console.log('连接被拒绝：缺少端口信息', { fromPort: !!fromPort, toPort: !!toPort });
				return false;
			}

			// 验证端口ID
			const fromPortObj = fromPort as go.Shape;
			const toPortObj = toPort as go.Shape;

			if (fromPortObj.portId === null || fromPortObj.portId === undefined || toPortObj.portId === null || toPortObj.portId === undefined) {
				console.log('连接被拒绝：端口ID无效', { fromPortId: fromPortObj.portId, toPortId: toPortObj.portId });
				return false;
			}

			// 检查端口的连接能力
			if (fromPortObj.fromLinkable === false) {
				console.log('连接被拒绝：起点端口不允许作为连线起点', { fromPortId: fromPortObj.portId });
				return false;
			}

			if (toPortObj.toLinkable === false) {
				console.log('连接被拒绝：终点端口不允许作为连线终点', { toPortId: toPortObj.portId });
				return false;
			}

			// 检查fromPort的最大出连接数量限制
			const fromMaxLinks = fromPortObj.fromMaxLinks || Infinity;
			if (fromMaxLinks !== Infinity) {
				const fromLinks = fromNode.findLinksOutOf(fromPortObj.portId).count;
				if (fromLinks >= fromMaxLinks) {
					console.log('连接被拒绝：起点端口连接数已达上限', { fromLinks, fromMaxLinks });
					return false;
				}
			}

			// 检查toPort的最大入连接数量限制
			const toMaxLinks = toPortObj.toMaxLinks || Infinity;
			if (toMaxLinks !== Infinity) {
				const toLinks = toNode.findLinksInto(toPortObj.portId).count;
				if (toLinks >= toMaxLinks) {
					console.log('连接被拒绝：终点端口连接数已达上限', { toLinks, toMaxLinks });
					return false;
				}
			}

			// 防止自连接
			if (fromNode === toNode) {
				console.log('连接被拒绝：不允许节点连接到自己');
				return false;
			}

			console.log('连接验证通过', {
				fromNodeId: fromNode.data.key,
				fromPortId: fromPortObj.portId,
				toNodeId: toNode.data.key,
				toPortId: toPortObj.portId,
			});
			return true;
		};

		// 设置验证函数
		diagram.toolManager.linkingTool.linkValidation = validateConnection;
		diagram.toolManager.relinkingTool.linkValidation = validateConnection;

		// 确保连线工具的正确配置
		diagram.toolManager.linkingTool.isUnconnectedLinkValid = false;
		diagram.toolManager.relinkingTool.isUnconnectedLinkValid = false;

		// 设置端口吸引力
		diagram.toolManager.linkingTool.portGravity = 20;
		diagram.toolManager.relinkingTool.portGravity = 20;
	};

	/**
	 * 设置端口高亮
	 */
	const setupPortHighlight = (diagram: go.Diagram) => {
		// 设置连接工具的portTargeted事件处理
		diagram.toolManager.linkingTool.portTargeted = (fromNode, fromPort, toNode, toPort, toEnd) => {
			if (!diagram) return;
			diagram.startTransaction('port highlight');
			try {
				// 如果有上一个悬停端口，将其恢复为默认可见状态
				if (lastPortOver.value && lastPortOver.value !== toPort) {
					(lastPortOver.value as go.Shape).fill = 'rgba(0, 0, 0, 0.3)';
				}
				// 更新当前悬停端口，并将其变为高亮状态
				lastPortOver.value = toPort;
				if (toPort) {
					(toPort as go.Shape).fill = 'rgba(0, 123, 255, 0.9)';
				}
			} finally {
				diagram.commitTransaction('port highlight');
			}
		};

		// 同样处理重新连接工具
		diagram.toolManager.relinkingTool.portTargeted = (fromNode, fromPort, toNode, toPort, toEnd) => {
			if (!diagram) return;
			diagram.startTransaction('port highlight');
			try {
				if (lastPortOver.value && lastPortOver.value !== toPort) {
					(lastPortOver.value as go.Shape).fill = 'rgba(0, 0, 0, 0.3)';
				}
				lastPortOver.value = toPort;
				if (toPort) {
					(toPort as go.Shape).fill = 'rgba(0, 123, 255, 0.9)';
				}
			} finally {
				diagram.commitTransaction('port highlight');
			}
		};

		// 修改工具的doStop方法以清除lastPortOver
		const origLinkingToolDoStop = diagram.toolManager.linkingTool.doStop;
		diagram.toolManager.linkingTool.doStop = function () {
			origLinkingToolDoStop.call(this);
			if (lastPortOver.value && diagram) {
				diagram.startTransaction('reset port');
				try {
					(lastPortOver.value as go.Shape).fill = 'rgba(0, 0, 0, 0.3)';
					lastPortOver.value = null;
				} finally {
					diagram.commitTransaction('reset port');
				}
			}
		};

		const origRelinkingToolDoStop = diagram.toolManager.relinkingTool.doStop;
		diagram.toolManager.relinkingTool.doStop = function () {
			origRelinkingToolDoStop.call(this);
			if (lastPortOver.value && diagram) {
				diagram.startTransaction('reset port');
				try {
					(lastPortOver.value as go.Shape).fill = 'rgba(0, 0, 0, 0.3)';
					lastPortOver.value = null;
				} finally {
					diagram.commitTransaction('reset port');
				}
			}
		};
	};

	/**
	 * 初始化图表
	 */
	const initDiagram = (
		diagramRef: HTMLElement,
		options: {
			onExternalObjectsDropped?: (e: go.DiagramEvent) => void;
			onSelectionChanged?: (e: go.DiagramEvent) => void;
			onSelectionMoved?: (e: go.DiagramEvent) => void;
			onModelChanged?: (e: go.ChangedEvent) => void;
			onLinkDrawn?: (e: go.DiagramEvent) => void;
			onLinkRelinked?: (e: go.DiagramEvent) => void;
		} = {}
	) => {
		if (!diagramRef || isInitialized.value) return null;

		$ = go.GraphObject.make;

		// 创建图表
		myDiagram = $(go.Diagram, diagramRef as HTMLDivElement, {
			grid: $(go.Panel, 'Grid', { gridCellSize: new go.Size(10, 10) }),
			'toolManager.hoverDelay': 100,
			'undoManager.isEnabled': true,
			allowDrop: true,
			ExternalObjectsDropped: options.onExternalObjectsDropped,
			'draggingTool.isEnabled': true,
			'draggingTool.isGridSnapEnabled': true,
			'resizingTool.isEnabled': true,
			'rotatingTool.isEnabled': true,
			allowMove: true,
			'layout.isOngoing': false,
			'layout.isInitial': false,
			'layout.isValidLayout': false,
			padding: new go.Margin(50, 50, 50, 50),
			positionComputation: (diagram: go.Diagram, pt: go.Point) => {
				const grid = diagram.grid;
				const gridsize = grid ? grid.gridCellSize : new go.Size(10, 10);
				return new go.Point(Math.floor(pt.x / gridsize.width) * gridsize.width, Math.floor(pt.y / gridsize.height) * gridsize.height);
			},
			'linkingTool.isUnconnectedLinkValid': false,
			'relinkingTool.isUnconnectedLinkValid': false,
		});

		if (!myDiagram) return null;

		// 安装自定义工具
		myDiagram.toolManager.draggingTool = new FollowerDraggingTool();
		myDiagram.toolManager.draggingTool.isEnabled = true;
		myDiagram.toolManager.linkingTool.isEnabled = true;
		myDiagram.toolManager.mouseDownTools.insertAt(0, myDiagram.toolManager.draggingTool);
		myDiagram.toolManager.resizingTool = new BusResizeMultipleTool();
		myDiagram.toolManager.resizingTool.isEnabled = true;

		// 设置临时连线属性
		myDiagram.toolManager.linkingTool.temporaryLink.routing = go.Link.AvoidsNodes;
		myDiagram.toolManager.relinkingTool.temporaryLink.routing = go.Link.AvoidsNodes;
		myDiagram.toolManager.linkingTool.temporaryLink.corner = 3;
		myDiagram.toolManager.relinkingTool.temporaryLink.corner = 3;
		myDiagram.toolManager.linkingTool.temporaryLink.curve = go.Link.JumpOver;
		myDiagram.toolManager.relinkingTool.temporaryLink.curve = go.Link.JumpOver;
		myDiagram.toolManager.linkingTool.temporaryLink.adjusting = go.Link.None;
		myDiagram.toolManager.relinkingTool.temporaryLink.adjusting = go.Link.None;

		// 设置节点模板
		setupAllNodeTemplates(myDiagram);
		setupPropertyNodeTemplate(myDiagram);
		setupLinkTemplate(myDiagram);

		// 设置连接验证和端口高亮
		setupConnectionValidation(myDiagram);
		setupPortHighlight(myDiagram);

		// 对齐网格
		myDiagram.toolManager.draggingTool.isGridSnapEnabled = true;
		myDiagram.layout.isOngoing = false;

		// 创建模型
		myDiagram.model = new go.GraphLinksModel({
			nodeDataArray: [],
			linkDataArray: [],
			linkKeyProperty: 'key',
		});

		// 设置模型属性
		if (myDiagram.model instanceof go.GraphLinksModel) {
			myDiagram.model.linkFromPortIdProperty = 'fromPort';
			myDiagram.model.linkToPortIdProperty = 'toPort';
		}

		// 添加事件监听器
		if (options.onSelectionChanged) {
			myDiagram.addDiagramListener('ChangedSelection', options.onSelectionChanged);
		}

		if (options.onSelectionMoved) {
			myDiagram.addDiagramListener('SelectionMoved', options.onSelectionMoved);
		}

		if (options.onModelChanged) {
			myDiagram.addModelChangedListener(options.onModelChanged);
		}

		if (options.onLinkDrawn) {
			myDiagram.addDiagramListener('LinkDrawn', options.onLinkDrawn);
		}

		if (options.onLinkRelinked) {
			myDiagram.addDiagramListener('LinkRelinked', options.onLinkRelinked);
		}

		// 设置状态
		diagramInstance.value = myDiagram;
		isInitialized.value = true;

		console.log('图表实例初始化完成');
		return myDiagram;
	};

	/**
	 * 销毁图表
	 */
	const destroyDiagram = () => {
		if (myDiagram) {
			myDiagram.div = null;
			myDiagram = null;
		}
		diagramInstance.value = null;
		isInitialized.value = false;
		lastPortOver.value = null;
	};

	/**
	 * 获取图表实例（内部使用）
	 */
	const _getDiagram = () => myDiagram;

	/**
	 * 获取GoJS构造函数（内部使用）
	 */
	const _getGoJS = () => $;

	return {
		// 状态
		isInitialized: computed(() => isInitialized.value),
		diagramInstance: computed(() => diagramInstance.value),

		// 方法
		initDiagram,
		destroyDiagram,

		// 内部访问（仅供其他hook使用）
		_getDiagram,
		_getGoJS,
	};
}
