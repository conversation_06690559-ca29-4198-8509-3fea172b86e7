import { ref } from 'vue';
import * as go from 'gojs';

/**
 * 连接点标记管理 Hook
 * 负责管理母线上的连接点标记
 */
export function useConnectionMarkers() {
	// 存储所有连接点标记的映射
	const connectionMarkers = ref<Map<string, string>>(new Map());

	/**
	 * 更新所有连接点标记的位置
	 * @param diagram GoJS图表实例
	 */
	const updateConnectionMarkers = (diagram: go.Diagram) => {
		if (!diagram) return;

		// 获取所有母线节点
		const busNodes = diagram.nodes.filter(node => node.data?.type === 'BusbarSection');

		busNodes.forEach(busNode => {
			updateBusConnectionMarkers(diagram, busNode);
		});
	};

	/**
	 * 更新单个母线的连接点标记
	 * @param diagram GoJS图表实例
	 * @param busNode 母线节点
	 */
	const updateBusConnectionMarkers = (diagram: go.Diagram, busNode: go.Node) => {
		if (!busNode || busNode.data?.type !== 'BusbarSection') return;

		// 清除该母线的旧标记
		clearBusMarkers(diagram, busNode);

		// 获取连接到该母线的所有连线
		const connectedLinks = busNode.findLinksConnected();
		const connectionPoints = new Set<string>();

		connectedLinks.forEach(link => {
			if (link instanceof go.Link) {
				// 计算连接点
				const connectionPoint = calculateConnectionPoint(busNode, link);
				if (connectionPoint) {
					const pointKey = `${connectionPoint.x.toFixed(1)}-${connectionPoint.y.toFixed(1)}`;
					connectionPoints.add(pointKey);
					
					// 创建连接点标记
					createConnectionMarker(diagram, busNode, connectionPoint, pointKey);
				}
			}
		});
	};

	/**
	 * 计算连接点位置
	 * @param busNode 母线节点
	 * @param link 连线
	 * @returns 连接点坐标
	 */
	const calculateConnectionPoint = (busNode: go.Node, link: go.Link): go.Point | null => {
		if (!busNode || !link) return null;

		// 获取连线的端点
		const fromNode = link.fromNode;
		const toNode = link.toNode;
		
		// 确定哪一端是母线
		const isBusFrom = fromNode === busNode;
		const otherNode = isBusFrom ? toNode : fromNode;
		
		if (!otherNode) return null;

		// 获取母线的边界
		const busRect = busNode.actualBounds;
		const otherCenter = otherNode.location;

		// 计算连接点：在母线上找到最接近另一端节点的点
		let connectionX = otherCenter.x;

		// 确保连接点在母线范围内
		if (connectionX < busRect.left) {
			connectionX = busRect.left;
		} else if (connectionX > busRect.right) {
			connectionX = busRect.right;
		}

		// 根据另一端节点的位置决定连接到母线的上方还是下方
		const busCenter = busNode.location;
		const connectionY = otherCenter.y < busCenter.y ? busRect.top : busRect.bottom;

		return new go.Point(connectionX, connectionY);
	};

	/**
	 * 创建连接点标记
	 * @param diagram GoJS图表实例
	 * @param busNode 母线节点
	 * @param connectionPoint 连接点坐标
	 * @param pointKey 连接点唯一标识
	 */
	const createConnectionMarker = (diagram: go.Diagram, busNode: go.Node, connectionPoint: go.Point, pointKey: string) => {
		const markerId = `${busNode.data.key}-marker-${pointKey}`;

		// 检查是否已经存在该标记
		const existingMarker = diagram.findNodeForKey(markerId);
		if (existingMarker) {
			// 更新位置
			existingMarker.location = connectionPoint;
			return;
		}

		// 创建新的连接点标记
		const markerData = {
			key: markerId,
			category: 'ConnectionMarker',
			parentNodeId: busNode.data.key,
			connectionPoint: connectionPoint,
			color: busNode.data.color || '#ff4444',
			visible: true,
		};

		// 添加标记节点到图表
		diagram.model.addNodeData(markerData);

		// 设置标记位置
		const marker = diagram.findNodeForKey(markerId);
		if (marker) {
			marker.location = connectionPoint;
		}

		// 记录标记
		connectionMarkers.value.set(markerId, busNode.data.key);
	};

	/**
	 * 清除指定母线的所有连接点标记
	 * @param diagram GoJS图表实例
	 * @param busNode 母线节点
	 */
	const clearBusMarkers = (diagram: go.Diagram, busNode: go.Node) => {
		if (!busNode) return;

		const busKey = busNode.data.key;
		const markersToRemove: string[] = [];

		// 找到该母线的所有标记
		connectionMarkers.value.forEach((parentBusKey, markerId) => {
			if (parentBusKey === busKey) {
				markersToRemove.push(markerId);
			}
		});

		// 移除标记
		markersToRemove.forEach(markerId => {
			const marker = diagram.findNodeForKey(markerId);
			if (marker) {
				diagram.model.removeNodeData(marker.data);
			}
			connectionMarkers.value.delete(markerId);
		});
	};

	/**
	 * 清除所有连接点标记
	 * @param diagram GoJS图表实例
	 */
	const clearAllMarkers = (diagram: go.Diagram) => {
		if (!diagram) return;

		const markersToRemove = Array.from(connectionMarkers.value.keys());
		
		markersToRemove.forEach(markerId => {
			const marker = diagram.findNodeForKey(markerId);
			if (marker) {
				diagram.model.removeNodeData(marker.data);
			}
		});

		connectionMarkers.value.clear();
	};

	/**
	 * 切换连接点标记的显示状态
	 * @param diagram GoJS图表实例
	 * @param visible 是否显示
	 */
	const toggleMarkersVisibility = (diagram: go.Diagram, visible: boolean) => {
		if (!diagram) return;

		connectionMarkers.value.forEach((_, markerId) => {
			const marker = diagram.findNodeForKey(markerId);
			if (marker) {
				diagram.model.setDataProperty(marker.data, 'visible', visible);
			}
		});
	};

	/**
	 * 监听图表变化，自动更新连接点标记
	 * @param diagram GoJS图表实例
	 */
	const setupMarkerUpdater = (diagram: go.Diagram) => {
		if (!diagram) return;

		// 监听模型变化
		diagram.addModelChangedListener((e) => {
			if (e.isTransactionFinished) {
				// 延迟更新，确保布局完成
				setTimeout(() => {
					updateConnectionMarkers(diagram);
				}, 100);
			}
		});

		// 监听布局完成
		diagram.addDiagramListener('LayoutCompleted', () => {
			updateConnectionMarkers(diagram);
		});
	};

	return {
		// 状态
		connectionMarkers,

		// 方法
		updateConnectionMarkers,
		updateBusConnectionMarkers,
		clearBusMarkers,
		clearAllMarkers,
		toggleMarkersVisibility,
		setupMarkerUpdater,
	};
}
