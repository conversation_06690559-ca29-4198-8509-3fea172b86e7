import { ref } from 'vue';
import * as go from 'gojs';

/**
 * 连接点标记管理 Hook（丝滑版本）
 * 参考LabelNode的实现方式，实现丝滑的连接点标记
 */
export function useConnectionMarkers() {
	// 存储所有连接点标记的映射 - 记录标记与母线的关系
	const connectionMarkers = ref<Map<string, { busKey: string; relativeX: number; isTop: boolean }>>(new Map());

	/**
	 * 更新所有连接点标记的位置
	 * @param diagram GoJS图表实例
	 */
	const updateConnectionMarkers = (diagram: go.Diagram) => {
		if (!diagram) return;

		// 获取所有母线节点
		const busNodes: go.Node[] = [];
		diagram.nodes.each((node) => {
			if (node.data?.type === 'BusbarSection') {
				busNodes.push(node);
			}
		});

		busNodes.forEach((busNode) => {
			updateBusConnectionMarkers(diagram, busNode);
		});
	};

	/**
	 * 更新单个母线的连接点标记
	 * @param diagram GoJS图表实例
	 * @param busNode 母线节点
	 */
	const updateBusConnectionMarkers = (diagram: go.Diagram, busNode: go.Node) => {
		if (!busNode || busNode.data?.type !== 'BusbarSection') return;

		// 清除该母线的旧标记
		clearBusMarkers(diagram, busNode);

		// 获取连接到该母线的所有连线
		const connectedLinks: go.Link[] = [];
		busNode.findLinksConnected().each((link) => {
			if (link instanceof go.Link) {
				connectedLinks.push(link);
			}
		});

		const connectionPoints = new Set<string>();

		connectedLinks.forEach((link) => {
			// 计算连接点
			const connectionPoint = calculateConnectionPoint(busNode, link);
			if (connectionPoint) {
				const pointKey = `${connectionPoint.x.toFixed(1)}-${connectionPoint.y.toFixed(1)}`;
				connectionPoints.add(pointKey);

				// 创建连接点标记
				createConnectionMarker(diagram, busNode, connectionPoint, pointKey);
			}
		});
	};

	/**
	 * 计算连接点位置
	 * @param busNode 母线节点
	 * @param link 连线
	 * @returns 连接点坐标
	 */
	const calculateConnectionPoint = (busNode: go.Node, link: go.Link): go.Point | null => {
		if (!busNode || !link) return null;

		// 获取连线的端点
		const fromNode = link.fromNode;
		const toNode = link.toNode;

		// 确定哪一端是母线
		const isBusFrom = fromNode === busNode;
		const otherNode = isBusFrom ? toNode : fromNode;

		if (!otherNode) return null;

		// 获取母线的边界
		const busRect = busNode.actualBounds;
		const otherCenter = otherNode.location;

		// 计算连接点：在母线上找到最接近另一端节点的点
		let connectionX = otherCenter.x;

		// 确保连接点在母线范围内
		if (connectionX < busRect.left) {
			connectionX = busRect.left;
		} else if (connectionX > busRect.right) {
			connectionX = busRect.right;
		}

		// 根据另一端节点的位置决定连接到母线的上方还是下方
		const busCenter = busNode.location;
		const connectionY = otherCenter.y < busCenter.y ? busRect.top : busRect.bottom;

		return new go.Point(connectionX, connectionY);
	};

	/**
	 * 创建连接点标记（丝滑版本）
	 * @param diagram GoJS图表实例
	 * @param busNode 母线节点
	 * @param connectionPoint 连接点坐标
	 * @param pointKey 连接点唯一标识
	 */
	const createConnectionMarker = (diagram: go.Diagram, busNode: go.Node, connectionPoint: go.Point, pointKey: string) => {
		const markerId = `${busNode.data.key}-marker-${pointKey}`;

		// 检查是否已经存在该标记
		const existingMarker = diagram.findNodeForKey(markerId);
		if (existingMarker) {
			// 更新标记的相对位置信息
			updateMarkerRelativePosition(busNode, existingMarker, connectionPoint);
			return;
		}

		// 计算相对于母线的位置信息
		const busCenter = busNode.location;
		const busBounds = busNode.actualBounds;
		const relativeX = connectionPoint.x - busCenter.x;
		const isTop = connectionPoint.y < busCenter.y;

		// 创建新的连接点标记（类似LabelNode的结构）
		const markerData = {
			key: markerId,
			category: 'ConnectionMarker',
			parentNodeId: busNode.data.key, // 建立父子关系
			loc: go.Point.stringify(connectionPoint), // 初始位置
			color: busNode.data.color || '#1c57ea', // 跟随母线颜色
			visible: true,
			// 保存相对位置信息（类似LabelNode的offsetX/offsetY）
			relativeX: relativeX,
			isTop: isTop,
			// 标记类型，用于区分不同的连接点
			markerType: 'connection',
		};

		// 添加标记节点到图表
		diagram.model.addNodeData(markerData);

		// 记录标记信息
		connectionMarkers.value.set(markerId, {
			busKey: busNode.data.key,
			relativeX: relativeX,
			isTop: isTop,
		});

		console.log(`创建连接点标记: ${markerId}, 相对位置: ${relativeX}, 位置: ${isTop ? '上方' : '下方'}`);
	};

	/**
	 * 更新标记的相对位置信息
	 * @param busNode 母线节点
	 * @param marker 标记节点
	 * @param connectionPoint 新的连接点坐标
	 */
	const updateMarkerRelativePosition = (busNode: go.Node, marker: go.Node, connectionPoint: go.Point) => {
		const busCenter = busNode.location;
		const relativeX = connectionPoint.x - busCenter.x;
		const isTop = connectionPoint.y < busCenter.y;

		// 更新标记的相对位置数据
		const diagram = marker.diagram;
		if (diagram) {
			diagram.model.setDataProperty(marker.data, 'relativeX', relativeX);
			diagram.model.setDataProperty(marker.data, 'isTop', isTop);
			diagram.model.setDataProperty(marker.data, 'loc', go.Point.stringify(connectionPoint));
		}

		// 更新内存中的记录
		connectionMarkers.value.set(marker.data.key, {
			busKey: busNode.data.key,
			relativeX: relativeX,
			isTop: isTop,
		});
	};

	/**
	 * 更新连接点标记位置（已集成到nodeManagement.updateFollowerNodePositions中）
	 * 这个函数现在主要用于手动触发更新
	 * @param diagram GoJS图表实例
	 */
	const updateConnectionMarkerPositions = (diagram: go.Diagram) => {
		if (!diagram) return;

		// 直接调用nodeManagement的updateFollowerNodePositions，它已经包含了ConnectionMarker的处理
		console.log('ConnectionMarker位置更新已集成到updateFollowerNodePositions中，无需单独处理');
	};

	/**
	 * 清除指定母线的所有连接点标记
	 * @param diagram GoJS图表实例
	 * @param busNode 母线节点
	 */
	const clearBusMarkers = (diagram: go.Diagram, busNode: go.Node) => {
		if (!busNode) return;

		const busKey = busNode.data.key;
		const markersToRemove: string[] = [];

		// 找到该母线的所有标记
		connectionMarkers.value.forEach((markerInfo, markerId) => {
			if (markerInfo.busKey === busKey) {
				markersToRemove.push(markerId);
			}
		});

		// 移除标记
		markersToRemove.forEach((markerId) => {
			const marker = diagram.findNodeForKey(markerId);
			if (marker) {
				diagram.model.removeNodeData(marker.data);
			}
			connectionMarkers.value.delete(markerId);
		});
	};

	/**
	 * 清除所有连接点标记
	 * @param diagram GoJS图表实例
	 */
	const clearAllMarkers = (diagram: go.Diagram) => {
		if (!diagram) return;

		const markersToRemove = Array.from(connectionMarkers.value.keys());

		markersToRemove.forEach((markerId) => {
			const marker = diagram.findNodeForKey(markerId);
			if (marker) {
				diagram.model.removeNodeData(marker.data);
			}
		});

		connectionMarkers.value.clear();
	};

	/**
	 * 切换连接点标记的显示状态
	 * @param diagram GoJS图表实例
	 * @param visible 是否显示
	 */
	const toggleMarkersVisibility = (diagram: go.Diagram, visible: boolean) => {
		if (!diagram) return;

		connectionMarkers.value.forEach((markerInfo, markerId) => {
			const marker = diagram.findNodeForKey(markerId);
			if (marker) {
				diagram.model.setDataProperty(marker.data, 'visible', visible);
			}
		});
	};

	/**
	 * 监听图表变化，自动更新连接点标记（丝滑版本）
	 * 位置更新已集成到nodeManagement系统中，这里只处理连接关系的重新计算
	 * @param diagram GoJS图表实例
	 */
	const setupMarkerUpdater = (diagram: go.Diagram) => {
		if (!diagram) return;

		// 监听模型变化 - 重新计算连接点
		diagram.addModelChangedListener((e) => {
			if (e.isTransactionFinished) {
				// 延迟更新，确保布局完成
				setTimeout(() => {
					updateConnectionMarkers(diagram);
				}, 100);
			}
		});

		// 监听布局完成 - 重新计算连接点
		diagram.addDiagramListener('LayoutCompleted', () => {
			updateConnectionMarkers(diagram);
		});

		console.log('ConnectionMarker监听器已设置，位置更新已集成到nodeManagement系统中');
	};

	return {
		// 状态
		connectionMarkers,

		// 方法
		updateConnectionMarkers,
		updateBusConnectionMarkers,
		updateConnectionMarkerPositions, // 新增：位置更新函数
		clearBusMarkers,
		clearAllMarkers,
		toggleMarkersVisibility,
		setupMarkerUpdater,
	};
}
