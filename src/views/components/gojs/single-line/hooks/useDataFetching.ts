import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { query_single_line_graph } from '../api';
import { getVoltageColor } from '/@/config/GraphConfig';
import { useIncrementalUpdate } from './useIncrementalUpdate';

/**
 * 数据获取Hook
 * 负责站内接线图数据的获取、处理、缓存和错误处理
 */
export function useDataFetching() {
	// ===== 状态管理 =====
	const isLoading = ref<boolean>(false);
	const error = ref<string | null>(null);
	const lastFetchParams = ref<{ bb_case_id: string; substation_id: string } | null>(null);

	// ===== 集成增量更新 =====
	const incrementalUpdate = useIncrementalUpdate();

	// ===== 数据缓存 =====
	const rawData = ref<any>(null);
	const parsedNodes = ref<SingleLineNode[]>([]);
	const parsedLinks = ref<SingleLineLink[]>([]);
	const labelNodes = ref<any[]>([]);
	const propertyNodes = ref<any[]>([]);
	const annotationLinks = ref<any[]>([]);

	// ===== 计算属性 =====
	const hasData = computed(() => parsedNodes.value.length > 0);
	const allNodes = computed(() => [...parsedNodes.value, ...labelNodes.value, ...propertyNodes.value]);
	const allLinks = computed(() => [...parsedLinks.value, ...annotationLinks.value]);

	/**
	 * 从API数据格式化属性文本
	 */
	const formatPropertyText = (apiNode: any): string => {
		const properties = apiNode.properties || {};
		const nodeType = apiNode.type || '';
		let resultLines: string[] = [];

		if (!properties || Object.keys(properties).length === 0) {
			return '';
		}

		switch (nodeType) {
			case 'BusbarSection':
				if (properties.vn_kv !== undefined && properties.vn_kv !== null) {
					resultLines.push(`U: ${Number(properties.vn_kv).toFixed(2)}`);
				}
				if (properties.dt_vn_kv !== undefined && properties.dt_vn_kv !== null) {
					resultLines.push(`U(M): ${Number(properties.dt_vn_kv).toFixed(2)}`);
				}
				break;

			case 'ACLineDot':
				if (properties.p_mw !== undefined && properties.p_mw !== null) {
					resultLines.push(`P: ${Number(properties.p_mw).toFixed(2)}`);
				}
				if (properties.dt_p_mw !== undefined && properties.dt_p_mw !== null) {
					resultLines.push(`P(M): ${Number(properties.dt_p_mw).toFixed(2)}`);
				}
				if (properties.q_mvar !== undefined && properties.q_mvar !== null) {
					resultLines.push(`Q: ${Number(properties.q_mvar).toFixed(2)}`);
				}
				if (properties.dt_q_mvar !== undefined && properties.dt_q_mvar !== null) {
					resultLines.push(`Q(M): ${Number(properties.dt_q_mvar).toFixed(2)}`);
				}
				break;

			case 'Load':
				if (properties.p_mw !== undefined && properties.p_mw !== null) {
					resultLines.push(`P: ${Number(properties.p_mw).toFixed(2)}`);
				}
				if (properties.dt_p_mw !== undefined && properties.dt_p_mw !== null) {
					resultLines.push(`P(M): ${Number(properties.dt_p_mw).toFixed(2)}`);
				}
				if (properties.q_mvar !== undefined && properties.q_mvar !== null) {
					resultLines.push(`Q: ${Number(properties.q_mvar).toFixed(2)}`);
				}
				if (properties.dt_q_mvar !== undefined && properties.dt_q_mvar !== null) {
					resultLines.push(`Q(M): ${Number(properties.dt_q_mvar).toFixed(2)}`);
				}
				break;

			case 'Synchronousmachine':
				if (properties.vn_kv !== undefined && properties.vn_kv !== null) {
					resultLines.push(`U: ${Number(properties.vn_kv).toFixed(2)}`);
				}
				if (properties.dt_vn_kv !== undefined && properties.dt_vn_kv !== null) {
					resultLines.push(`U(M): ${Number(properties.dt_vn_kv).toFixed(2)}`);
				}
				if (properties.p_mw !== undefined && properties.p_mw !== null) {
					resultLines.push(`P: ${Number(properties.p_mw).toFixed(2)}`);
				}
				if (properties.dt_p_mw !== undefined && properties.dt_p_mw !== null) {
					resultLines.push(`P(M): ${Number(properties.dt_p_mw).toFixed(2)}`);
				}
				if (properties.q_mvar !== undefined && properties.q_mvar !== null) {
					resultLines.push(`Q: ${Number(properties.q_mvar).toFixed(2)}`);
				}
				if (properties.dt_q_mvar !== undefined && properties.dt_q_mvar !== null) {
					resultLines.push(`Q(M): ${Number(properties.dt_q_mvar).toFixed(2)}`);
				}
				break;

			case 'Trafo':
				if (properties.p_hv_mw !== undefined && properties.p_hv_mw !== null) {
					resultLines.push(`P: ${Number(properties.p_hv_mw).toFixed(2)}`);
				}
				if (properties.q_hv_mvar !== undefined && properties.q_hv_mvar !== null) {
					resultLines.push(`Q: ${Number(properties.q_hv_mvar).toFixed(2)}`);
				}
				if (properties.p_lv_mw !== undefined && properties.p_lv_mw !== null) {
					resultLines.push(`P: ${Number(properties.p_lv_mw).toFixed(2)}`);
				}
				if (properties.q_lv_mvar !== undefined && properties.q_lv_mvar !== null) {
					resultLines.push(`Q: ${Number(properties.q_lv_mvar).toFixed(2)}`);
				}
				if (properties.dt_p_hv_mw !== undefined && properties.dt_p_hv_mw !== null) {
					resultLines.push(`P(M): ${Number(properties.dt_p_hv_mw).toFixed(2)}`);
				}
				if (properties.dt_q_hv_mvar !== undefined && properties.dt_q_hv_mvar !== null) {
					resultLines.push(`Q(M): ${Number(properties.dt_q_hv_mvar).toFixed(2)}`);
				}
				if (properties.dt_p_lv_mw !== undefined && properties.dt_p_lv_mw !== null) {
					resultLines.push(`P(M): ${Number(properties.dt_p_lv_mw).toFixed(2)}`);
				}
				if (properties.dt_q_lv_mvar !== undefined && properties.dt_q_lv_mvar !== null) {
					resultLines.push(`Q(M): ${Number(properties.dt_q_lv_mvar).toFixed(2)}`);
				}
				break;

			case 'Trafo3w':
				if (properties.p_hv_mw !== undefined && properties.p_hv_mw !== null) {
					resultLines.push(`PH: ${Number(properties.p_hv_mw).toFixed(2)}`);
				}
				if (properties.q_hv_mvar !== undefined && properties.q_hv_mvar !== null) {
					resultLines.push(`QH: ${Number(properties.q_hv_mvar).toFixed(2)}`);
				}
				if (properties.p_mv_mw !== undefined && properties.p_mv_mw !== null) {
					resultLines.push(`PM: ${Number(properties.p_mv_mw).toFixed(2)}`);
				}
				if (properties.q_mv_mvar !== undefined && properties.q_mv_mvar !== null) {
					resultLines.push(`QM: ${Number(properties.q_mv_mvar).toFixed(2)}`);
				}
				if (properties.p_lv_mw !== undefined && properties.p_lv_mw !== null) {
					resultLines.push(`PL: ${Number(properties.p_lv_mw).toFixed(2)}`);
				}
				if (properties.q_lv_mvar !== undefined && properties.q_lv_mvar !== null) {
					resultLines.push(`QL: ${Number(properties.q_lv_mvar).toFixed(2)}`);
				}
				if (properties.dt_p_hv_mw !== undefined && properties.dt_p_hv_mw !== null) {
					resultLines.push(`PH(M): ${Number(properties.dt_p_hv_mw).toFixed(2)}`);
				}
				if (properties.dt_q_hv_mvar !== undefined && properties.dt_q_hv_mvar !== null) {
					resultLines.push(`QH(M): ${Number(properties.dt_q_hv_mvar).toFixed(2)}`);
				}
				if (properties.dt_p_mv_mw !== undefined && properties.dt_p_mv_mw !== null) {
					resultLines.push(`PM(M): ${Number(properties.dt_p_mv_mw).toFixed(2)}`);
				}
				if (properties.dt_q_mv_mvar !== undefined && properties.dt_q_mv_mvar !== null) {
					resultLines.push(`QM(M): ${Number(properties.dt_q_mv_mvar).toFixed(2)}`);
				}
				if (properties.dt_p_lv_mw !== undefined && properties.dt_p_lv_mw !== null) {
					resultLines.push(`PL(M): ${Number(properties.dt_p_lv_mw).toFixed(2)}`);
				}
				if (properties.dt_q_lv_mvar !== undefined && properties.dt_q_lv_mvar !== null) {
					resultLines.push(`QL(M): ${Number(properties.dt_q_lv_mvar).toFixed(2)}`);
				}
				break;

			case 'ShuntCompensator':
				if (properties.q_mvar !== undefined && properties.q_mvar !== null) {
					resultLines.push(`Q: ${Number(properties.q_mvar).toFixed(2)}`);
				}
				if (properties.p_mw !== undefined && properties.p_mw !== null) {
					resultLines.push(`Q(M): ${Number(properties.p_mw).toFixed(2)}`);
				}
				break;

			default:
				break;
		}

		return resultLines.join('\n');
	};

	/**
	 * 处理节点数据，创建主节点、标签节点和属性节点
	 */
	const processNodeData = (nodes: any[], showResult: boolean) => {
		const mainNodes: SingleLineNode[] = [];
		const labelNodesData: any[] = [];
		const propertyNodesData: any[] = [];
		const annotationLinksData: any[] = [];

		nodes.forEach((node: any) => {
			// 创建主节点
			const nodeData: SingleLineNode = {
				key: node.id,
				category: node.type,
				type: node.type,
				name: node.name || '未命名节点',
				color: getVoltageColor(node.voltage),
				pos: [0, 0],
				angle: node.angle || 0,
				voltage: node.voltage,
				voltage2: node.voltage2,
				voltage3: node.voltage3,
				color2: getVoltageColor(node.voltage2),
				color3: getVoltageColor(node.voltage3),
				width: node.type === 'BusbarSection' ? 150 : undefined,
				properties: node.properties || {},
			};
			mainNodes.push(nodeData);

			// 创建标签节点
			const labelKey = node.id + '-label';
			const labelOffsetX = 0;
			const labelOffsetY = 30;
			const labelLoc = { x: 50, y: 50 };

			labelNodesData.push({
				key: labelKey,
				text: node.name || '未命名节点',
				loc: `${labelLoc.x} ${labelLoc.y}`,
				category: 'LabelNode',
				visible: true,
				parentNodeId: node.id,
				color: getVoltageColor(node.voltage),
				offsetX: labelOffsetX,
				offsetY: labelOffsetY,
			});

			// 创建属性节点（如果有属性数据）
			const propertyText = formatPropertyText(node);
			if (propertyText?.trim()) {
				const propertyKey = node.id + '-property';
				const propertyOffsetX = 120;
				const propertyOffsetY = 0;
				const propertyLoc = { x: 50 + propertyOffsetX, y: 50 + propertyOffsetY };

				propertyNodesData.push({
					key: propertyKey,
					properties: propertyText,
					loc: `${propertyLoc.x} ${propertyLoc.y}`,
					category: 'PropertyNode',
					visible: showResult,
					parentNodeId: node.id,
					color: getVoltageColor(node.voltage),
					offsetX: propertyOffsetX,
					offsetY: propertyOffsetY,
				});

				// 创建连接主节点和属性节点的连线
				annotationLinksData.push({
					key: `link-${propertyKey}`,
					from: node.id,
					to: propertyKey,
					category: 'AnnotationLink',
					visible: showResult,
					color: getVoltageColor(node.voltage),
				});
			}
		});

		return {
			mainNodes,
			labelNodesData,
			propertyNodesData,
			annotationLinksData,
		};
	};

	/**
	 * 处理连线数据
	 */
	const processLinkData = (edges: any[]) => {
		const links: SingleLineLink[] = [];

		edges.forEach((link: any, index: number) => {
			if (!link.source || !link.target) {
				console.warn(`第${index}个连线缺少源或目标节点，已跳过`);
				return;
			}

			const linkData: SingleLineLink = {
				key: `link-${index}`,
				from: link.source,
				to: link.target,
				fromPort: link.source_port,
				toPort: link.target_port,
				properties: link.properties || {},
				voltage: link.voltage,
				color: getVoltageColor(link.voltage),
			};

			links.push(linkData);
		});

		return links;
	};

	/**
	 * 获取单线图数据
	 */
	const fetchData = async (params: { bb_case_id: string; substation_id: string }, showResult: boolean = false) => {
		isLoading.value = true;
		error.value = null;

		try {
			const { data } = await query_single_line_graph(params);

			if (!data) {
				throw new Error('服务器返回的数据为空');
			}

			// 保存原始数据和参数
			rawData.value = data;
			lastFetchParams.value = { ...params };

			// 处理节点数据
			if (data.nodes && Array.isArray(data.nodes)) {
				const nodeResult = processNodeData(data.nodes, showResult);
				parsedNodes.value = nodeResult.mainNodes;
				labelNodes.value = nodeResult.labelNodesData;
				propertyNodes.value = nodeResult.propertyNodesData;
				annotationLinks.value = nodeResult.annotationLinksData;
			} else {
				parsedNodes.value = [];
				labelNodes.value = [];
				propertyNodes.value = [];
				annotationLinks.value = [];
			}

			// 处理连线数据
			if (data.edges && Array.isArray(data.edges)) {
				parsedLinks.value = processLinkData(data.edges);
			} else {
				parsedLinks.value = [];
			}

			console.log('数据获取和处理完成:', {
				主节点: parsedNodes.value.length,
				标签节点: labelNodes.value.length,
				属性节点: propertyNodes.value.length,
				连线: parsedLinks.value.length,
			});

			return {
				nodes: allNodes.value,
				links: allLinks.value,
			};
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : '未知错误';
			error.value = errorMessage;
			console.error('获取单线图数据失败:', err);
			ElMessage.error(`加载单线图失败: ${errorMessage}`);
			throw err;
		} finally {
			isLoading.value = false;
		}
	};

	/**
	 * 重新处理属性节点的显示状态
	 */
	const updatePropertyVisibility = (showResult: boolean) => {
		propertyNodes.value.forEach((node) => {
			node.visible = showResult;
		});
		annotationLinks.value.forEach((link) => {
			link.visible = showResult;
		});
	};

	/**
	 * 清除缓存数据
	 */
	const clearCache = () => {
		rawData.value = null;
		parsedNodes.value = [];
		parsedLinks.value = [];
		labelNodes.value = [];
		propertyNodes.value = [];
		annotationLinks.value = [];
		lastFetchParams.value = null;
		error.value = null;
	};

	/**
	 * 重新获取数据（使用上次的参数）
	 */
	const refetch = async (showResult: boolean = false) => {
		if (!lastFetchParams.value) {
			throw new Error('没有可用的获取参数');
		}
		return await fetchData(lastFetchParams.value, showResult);
	};

	return {
		// 状态
		isLoading: computed(() => isLoading.value),
		error: computed(() => error.value),
		hasData,

		// 数据
		rawData: computed(() => rawData.value),
		parsedNodes: computed(() => parsedNodes.value),
		parsedLinks: computed(() => parsedLinks.value),
		labelNodes: computed(() => labelNodes.value),
		propertyNodes: computed(() => propertyNodes.value),
		annotationLinks: computed(() => annotationLinks.value),
		allNodes,
		allLinks,

		// 方法
		fetchData,
		updatePropertyVisibility,
		clearCache,
		refetch,
		formatPropertyText,

		// 增量更新功能
		incrementalUpdate,
	};
}
