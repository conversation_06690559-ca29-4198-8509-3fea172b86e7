import { ref, computed } from 'vue';
import * as go from 'gojs';

/**
 * 属性显示Hook
 * 负责PropertyNode的显示、隐藏、位置更新等功能
 */
export function usePropertyDisplay() {
	// ===== 状态管理 =====
	const showResult = ref<boolean>(false);

	/**
	 * 切换属性节点的显示状态
	 */
	const togglePropertyNodes = (diagram: go.Diagram | null, show: boolean) => {
		if (!diagram) return;
		
		diagram.startTransaction(show ? 'show properties' : 'hide properties');
		try {
			// 切换PropertyNode的可见性
			diagram.nodes.each((node: go.Node) => {
				if (node.data?.category === 'PropertyNode') {
					node.visible = show;
				}
			});

			// 切换AnnotationLink的可见性
			diagram.links.each((link: go.Link) => {
				if (link.data?.category === 'AnnotationLink') {
					link.visible = show;
				}
			});
		} finally {
			diagram.commitTransaction(show ? 'show properties' : 'hide properties');
		}
	};

	/**
	 * 更新PropertyNode的位置，基于其父节点的位置
	 */
	const updatePropertyNodePositions = (diagram: go.Diagram) => {
		if (!diagram) return;

		diagram.startTransaction('update property positions');
		try {
			diagram.nodes.each((node: go.Node) => {
				if (node.data?.category === 'PropertyNode' && node.data?.parentNodeId) {
					const parentNode = diagram.findNodeForKey(node.data.parentNodeId);
					if (parentNode) {
						const parentBounds = parentNode.actualBounds;
						// 计算PropertyNode的位置：在父节点右侧60像素处
						const newLoc = new go.Point(parentBounds.right + 60, parentBounds.centerY);
						node.location = newLoc;
					}
				}
			});
		} finally {
			diagram.commitTransaction('update property positions');
		}
	};

	/**
	 * 创建PropertyNode数据
	 */
	const createPropertyNodeData = (parentNodeId: string, propertyText: string, color: string, showProperties: boolean = false) => {
		const propertyKey = parentNodeId + '-property';
		const propertyOffsetX = 120;
		const propertyOffsetY = 0;
		const propertyLoc = new go.Point(50 + propertyOffsetX, 50 + propertyOffsetY);

		return {
			key: propertyKey,
			properties: propertyText,
			loc: go.Point.stringify(propertyLoc),
			category: 'PropertyNode',
			visible: showProperties,
			parentNodeId: parentNodeId,
			color: color,
			offsetX: propertyOffsetX,
			offsetY: propertyOffsetY,
		};
	};

	/**
	 * 创建AnnotationLink数据
	 */
	const createAnnotationLinkData = (fromNodeId: string, toNodeId: string, color: string, showProperties: boolean = false) => {
		return {
			key: `link-${toNodeId}`,
			from: fromNodeId,
			to: toNodeId,
			category: 'AnnotationLink',
			visible: showProperties,
			color: color,
		};
	};

	/**
	 * 为指定节点添加PropertyNode
	 */
	const addPropertyNodeForNode = (diagram: go.Diagram, nodeId: string, propertyText: string, color: string) => {
		if (!diagram || !propertyText?.trim()) return null;

		const propertyNodeData = createPropertyNodeData(nodeId, propertyText, color, showResult.value);
		const annotationLinkData = createAnnotationLinkData(nodeId, propertyNodeData.key, color, showResult.value);

		diagram.startTransaction('add property node');
		try {
			if (diagram.model instanceof go.GraphLinksModel) {
				// 添加PropertyNode
				diagram.model.addNodeData(propertyNodeData);
				// 添加连接线
				diagram.model.addLinkData(annotationLinkData);

				// 更新位置
				setTimeout(() => {
					updatePropertyNodePositions(diagram);
				}, 50);

				return propertyNodeData.key;
			}
		} catch (error) {
			console.error('添加PropertyNode失败:', error);
		} finally {
			diagram.commitTransaction('add property node');
		}

		return null;
	};

	/**
	 * 移除指定节点的PropertyNode
	 */
	const removePropertyNodeForNode = (diagram: go.Diagram, nodeId: string) => {
		if (!diagram) return false;

		const propertyKey = nodeId + '-property';
		const linkKey = `link-${propertyKey}`;

		diagram.startTransaction('remove property node');
		try {
			// 查找并移除PropertyNode
			const propertyNode = diagram.findNodeForKey(propertyKey);
			if (propertyNode) {
				diagram.model.removeNodeData(propertyNode.data);
			}

			// 查找并移除AnnotationLink
			const annotationLink = diagram.findLinkForKey(linkKey);
			if (annotationLink) {
				diagram.model.removeLinkData(annotationLink.data);
			}

			return true;
		} catch (error) {
			console.error('移除PropertyNode失败:', error);
			return false;
		} finally {
			diagram.commitTransaction('remove property node');
		}
	};

	/**
	 * 更新PropertyNode的内容
	 */
	const updatePropertyNodeContent = (diagram: go.Diagram, nodeId: string, newPropertyText: string) => {
		if (!diagram) return false;

		const propertyKey = nodeId + '-property';
		const propertyNode = diagram.findNodeForKey(propertyKey);

		if (!propertyNode) return false;

		diagram.startTransaction('update property content');
		try {
			diagram.model.setDataProperty(propertyNode.data, 'properties', newPropertyText);
			return true;
		} catch (error) {
			console.error('更新PropertyNode内容失败:', error);
			return false;
		} finally {
			diagram.commitTransaction('update property content');
		}
	};

	/**
	 * 获取所有PropertyNode
	 */
	const getAllPropertyNodes = (diagram: go.Diagram): go.Node[] => {
		if (!diagram) return [];

		const propertyNodes: go.Node[] = [];
		diagram.nodes.each((node: go.Node) => {
			if (node.data?.category === 'PropertyNode') {
				propertyNodes.push(node);
			}
		});

		return propertyNodes;
	};

	/**
	 * 获取所有AnnotationLink
	 */
	const getAllAnnotationLinks = (diagram: go.Diagram): go.Link[] => {
		if (!diagram) return [];

		const annotationLinks: go.Link[] = [];
		diagram.links.each((link: go.Link) => {
			if (link.data?.category === 'AnnotationLink') {
				annotationLinks.push(link);
			}
		});

		return annotationLinks;
	};

	/**
	 * 批量更新所有PropertyNode的可见性
	 */
	const updateAllPropertyVisibility = (diagram: go.Diagram, visible: boolean) => {
		if (!diagram) return;

		diagram.startTransaction('update all property visibility');
		try {
			// 更新PropertyNode可见性
			diagram.nodes.each((node: go.Node) => {
				if (node.data?.category === 'PropertyNode') {
					node.visible = visible;
				}
			});

			// 更新AnnotationLink可见性
			diagram.links.each((link: go.Link) => {
				if (link.data?.category === 'AnnotationLink') {
					link.visible = visible;
				}
			});
		} finally {
			diagram.commitTransaction('update all property visibility');
		}
	};

	/**
	 * 清理孤立的PropertyNode（父节点不存在的PropertyNode）
	 */
	const cleanupOrphanedPropertyNodes = (diagram: go.Diagram): number => {
		if (!diagram) return 0;

		const orphanedNodes: go.Node[] = [];
		const orphanedLinks: go.Link[] = [];

		// 查找孤立的PropertyNode
		diagram.nodes.each((node: go.Node) => {
			if (node.data?.category === 'PropertyNode' && node.data?.parentNodeId) {
				const parentNode = diagram.findNodeForKey(node.data.parentNodeId);
				if (!parentNode) {
					orphanedNodes.push(node);
				}
			}
		});

		// 查找孤立的AnnotationLink
		diagram.links.each((link: go.Link) => {
			if (link.data?.category === 'AnnotationLink') {
				const fromNode = diagram.findNodeForKey(link.data.from);
				const toNode = diagram.findNodeForKey(link.data.to);
				if (!fromNode || !toNode) {
					orphanedLinks.push(link);
				}
			}
		});

		// 移除孤立的节点和连线
		if (orphanedNodes.length > 0 || orphanedLinks.length > 0) {
			diagram.startTransaction('cleanup orphaned property nodes');
			try {
				orphanedNodes.forEach(node => {
					diagram.model.removeNodeData(node.data);
				});
				orphanedLinks.forEach(link => {
					diagram.model.removeLinkData(link.data);
				});
			} finally {
				diagram.commitTransaction('cleanup orphaned property nodes');
			}
		}

		return orphanedNodes.length + orphanedLinks.length;
	};

	/**
	 * 获取PropertyNode统计信息
	 */
	const getPropertyStatistics = (diagram: go.Diagram) => {
		if (!diagram) return { total: 0, visible: 0, hidden: 0 };

		let total = 0;
		let visible = 0;
		let hidden = 0;

		diagram.nodes.each((node: go.Node) => {
			if (node.data?.category === 'PropertyNode') {
				total++;
				if (node.visible) {
					visible++;
				} else {
					hidden++;
				}
			}
		});

		return { total, visible, hidden };
	};

	/**
	 * 设置显示状态并更新所有PropertyNode
	 */
	const setShowResult = (diagram: go.Diagram | null, show: boolean) => {
		showResult.value = show;
		if (diagram) {
			togglePropertyNodes(diagram, show);
		}
	};

	return {
		// 状态
		showResult: computed({
			get: () => showResult.value,
			set: (value: boolean) => { showResult.value = value; }
		}),

		// 基础操作
		togglePropertyNodes,
		updatePropertyNodePositions,
		setShowResult,

		// PropertyNode管理
		createPropertyNodeData,
		createAnnotationLinkData,
		addPropertyNodeForNode,
		removePropertyNodeForNode,
		updatePropertyNodeContent,

		// 查询和统计
		getAllPropertyNodes,
		getAllAnnotationLinks,
		getPropertyStatistics,

		// 批量操作
		updateAllPropertyVisibility,
		cleanupOrphanedPropertyNodes,
	};
}
