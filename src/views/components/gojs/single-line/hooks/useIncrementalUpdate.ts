import { ref, computed } from 'vue';
import * as go from 'gojs';

/**
 * 增量更新Hook
 * 参考地理接线图的性能优化策略，实现增量更新、视图优化、内存管理等功能
 */
export function useIncrementalUpdate() {
	// ===== 状态管理 =====
	const isUpdating = ref<boolean>(false);
	const lastUpdateTime = ref<number>(0);
	const updateQueue = ref<Array<{ type: 'node' | 'link', action: 'add' | 'update' | 'remove', data: any }>>([]);
	
	// 缓存数据
	const nodeCache = ref<Map<string, any>>(new Map());
	const linkCache = ref<Map<string, any>>(new Map());
	
	// 性能监控
	const performanceMetrics = ref({
		totalNodes: 0,
		totalLinks: 0,
		lastUpdateDuration: 0,
		averageUpdateDuration: 0,
		updateCount: 0,
	});

	/**
	 * 初始化空白图表
	 */
	const initBlankGraph = (diagram: go.Diagram) => {
		if (!diagram) return;

		diagram.startTransaction('init blank graph');
		try {
			// 清空现有数据
			diagram.model.nodeDataArray = [];
			if (diagram.model instanceof go.GraphLinksModel) {
				diagram.model.linkDataArray = [];
			}

			// 清空缓存
			nodeCache.value.clear();
			linkCache.value.clear();
			updateQueue.value = [];

			// 重置性能指标
			performanceMetrics.value = {
				totalNodes: 0,
				totalLinks: 0,
				lastUpdateDuration: 0,
				averageUpdateDuration: 0,
				updateCount: 0,
			};

			console.log('空白图表初始化完成');
		} finally {
			diagram.commitTransaction('init blank graph');
		}
	};

	/**
	 * 加载图表数据（全量加载）
	 */
	const loadGraphData = (diagram: go.Diagram, nodeData: any[], linkData: any[]) => {
		if (!diagram) return;

		const startTime = performance.now();
		isUpdating.value = true;

		diagram.startTransaction('load graph data');
		try {
			// 更新缓存
			nodeCache.value.clear();
			linkCache.value.clear();

			nodeData.forEach(node => {
				nodeCache.value.set(node.key, { ...node });
			});

			linkData.forEach(link => {
				linkCache.value.set(link.key, { ...link });
			});

			// 设置模型数据
			diagram.model.nodeDataArray = nodeData;
			if (diagram.model instanceof go.GraphLinksModel) {
				diagram.model.linkDataArray = linkData;
			}

			// 更新性能指标
			const duration = performance.now() - startTime;
			updatePerformanceMetrics(nodeData.length, linkData.length, duration);

			console.log(`图表数据加载完成: ${nodeData.length} 节点, ${linkData.length} 连线, 耗时: ${duration.toFixed(2)}ms`);
		} finally {
			diagram.commitTransaction('load graph data');
			isUpdating.value = false;
			lastUpdateTime.value = Date.now();
		}
	};

	/**
	 * 增量更新图表数据
	 */
	const updateGraphData = (diagram: go.Diagram, nodeData: any[], linkData: any[]) => {
		if (!diagram) return;

		const startTime = performance.now();
		isUpdating.value = true;

		diagram.startTransaction('update graph data');
		try {
			// 分析变化
			const changes = analyzeDataChanges(nodeData, linkData);
			
			// 应用增量更新
			applyIncrementalChanges(diagram, changes);

			// 更新缓存
			updateCache(nodeData, linkData);

			// 更新性能指标
			const duration = performance.now() - startTime;
			updatePerformanceMetrics(nodeData.length, linkData.length, duration);

			console.log(`增量更新完成: 新增 ${changes.addedNodes.length} 节点, 更新 ${changes.updatedNodes.length} 节点, 删除 ${changes.removedNodes.length} 节点, 耗时: ${duration.toFixed(2)}ms`);
		} finally {
			diagram.commitTransaction('update graph data');
			isUpdating.value = false;
			lastUpdateTime.value = Date.now();
		}
	};

	/**
	 * 分析数据变化
	 */
	const analyzeDataChanges = (newNodeData: any[], newLinkData: any[]) => {
		const changes = {
			addedNodes: [] as any[],
			updatedNodes: [] as any[],
			removedNodes: [] as string[],
			addedLinks: [] as any[],
			updatedLinks: [] as any[],
			removedLinks: [] as string[],
		};

		// 分析节点变化
		const newNodeMap = new Map(newNodeData.map(node => [node.key, node]));
		const oldNodeKeys = new Set(nodeCache.value.keys());

		// 找出新增和更新的节点
		newNodeData.forEach(node => {
			const oldNode = nodeCache.value.get(node.key);
			if (!oldNode) {
				changes.addedNodes.push(node);
			} else if (!deepEqual(oldNode, node)) {
				changes.updatedNodes.push(node);
			}
		});

		// 找出删除的节点
		oldNodeKeys.forEach(key => {
			if (!newNodeMap.has(key)) {
				changes.removedNodes.push(key);
			}
		});

		// 分析连线变化
		const newLinkMap = new Map(newLinkData.map(link => [link.key, link]));
		const oldLinkKeys = new Set(linkCache.value.keys());

		// 找出新增和更新的连线
		newLinkData.forEach(link => {
			const oldLink = linkCache.value.get(link.key);
			if (!oldLink) {
				changes.addedLinks.push(link);
			} else if (!deepEqual(oldLink, link)) {
				changes.updatedLinks.push(link);
			}
		});

		// 找出删除的连线
		oldLinkKeys.forEach(key => {
			if (!newLinkMap.has(key)) {
				changes.removedLinks.push(key);
			}
		});

		return changes;
	};

	/**
	 * 应用增量变化
	 */
	const applyIncrementalChanges = (diagram: go.Diagram, changes: any) => {
		const model = diagram.model;

		// 删除节点
		changes.removedNodes.forEach((nodeKey: string) => {
			const nodeData = model.findNodeDataForKey(nodeKey);
			if (nodeData) {
				model.removeNodeData(nodeData);
			}
		});

		// 删除连线
		if (model instanceof go.GraphLinksModel) {
			changes.removedLinks.forEach((linkKey: string) => {
				const linkData = model.findLinkDataForKey(linkKey);
				if (linkData) {
					model.removeLinkData(linkData);
				}
			});
		}

		// 添加新节点
		changes.addedNodes.forEach((nodeData: any) => {
			model.addNodeData(nodeData);
		});

		// 添加新连线
		if (model instanceof go.GraphLinksModel) {
			changes.addedLinks.forEach((linkData: any) => {
				model.addLinkData(linkData);
			});
		}

		// 更新节点
		changes.updatedNodes.forEach((nodeData: any) => {
			const existingData = model.findNodeDataForKey(nodeData.key);
			if (existingData) {
				Object.keys(nodeData).forEach(key => {
					if (key !== 'key') {
						model.setDataProperty(existingData, key, nodeData[key]);
					}
				});
			}
		});

		// 更新连线
		if (model instanceof go.GraphLinksModel) {
			changes.updatedLinks.forEach((linkData: any) => {
				const existingData = model.findLinkDataForKey(linkData.key);
				if (existingData) {
					Object.keys(linkData).forEach(key => {
						if (key !== 'key') {
							model.setDataProperty(existingData, key, linkData[key]);
						}
					});
				}
			});
		}
	};

	/**
	 * 更新缓存
	 */
	const updateCache = (nodeData: any[], linkData: any[]) => {
		nodeCache.value.clear();
		linkCache.value.clear();

		nodeData.forEach(node => {
			nodeCache.value.set(node.key, { ...node });
		});

		linkData.forEach(link => {
			linkCache.value.set(link.key, { ...link });
		});
	};

	/**
	 * 更新性能指标
	 */
	const updatePerformanceMetrics = (nodeCount: number, linkCount: number, duration: number) => {
		const metrics = performanceMetrics.value;
		metrics.totalNodes = nodeCount;
		metrics.totalLinks = linkCount;
		metrics.lastUpdateDuration = duration;
		metrics.updateCount++;
		metrics.averageUpdateDuration = (metrics.averageUpdateDuration * (metrics.updateCount - 1) + duration) / metrics.updateCount;
	};

	/**
	 * 深度比较两个对象
	 */
	const deepEqual = (obj1: any, obj2: any): boolean => {
		if (obj1 === obj2) return true;
		if (obj1 == null || obj2 == null) return false;
		if (typeof obj1 !== typeof obj2) return false;

		if (typeof obj1 === 'object') {
			const keys1 = Object.keys(obj1);
			const keys2 = Object.keys(obj2);
			if (keys1.length !== keys2.length) return false;

			for (const key of keys1) {
				if (!keys2.includes(key) || !deepEqual(obj1[key], obj2[key])) {
					return false;
				}
			}
			return true;
		}

		return obj1 === obj2;
	};

	/**
	 * 优化视图性能
	 */
	const optimizeViewport = (diagram: go.Diagram) => {
		if (!diagram) return;

		// 禁用动画以提高性能
		diagram.animationManager.isEnabled = false;

		// 设置视图优化
		diagram.skipsUndoManager = true;
		diagram.allowVerticalScroll = true;
		diagram.allowHorizontalScroll = true;

		// 启用虚拟化（对大量节点有效）
		if (performanceMetrics.value.totalNodes > 500) {
			diagram.contentAlignment = go.Spot.Center;
			diagram.padding = new go.Margin(20);
		}
	};

	/**
	 * 清理内存
	 */
	const cleanupMemory = () => {
		nodeCache.value.clear();
		linkCache.value.clear();
		updateQueue.value = [];
		
		// 强制垃圾回收（如果可用）
		if (window.gc) {
			window.gc();
		}
	};

	/**
	 * 获取性能报告
	 */
	const getPerformanceReport = () => {
		return {
			...performanceMetrics.value,
			cacheSize: {
				nodes: nodeCache.value.size,
				links: linkCache.value.size,
			},
			memoryUsage: {
				// 估算内存使用（简化版本）
				estimatedNodeMemory: nodeCache.value.size * 1024, // 假设每个节点1KB
				estimatedLinkMemory: linkCache.value.size * 512,   // 假设每个连线512B
			},
			lastUpdateTime: lastUpdateTime.value,
		};
	};

	return {
		// 状态
		isUpdating: computed(() => isUpdating.value),
		performanceMetrics: computed(() => performanceMetrics.value),

		// 核心方法
		initBlankGraph,
		loadGraphData,
		updateGraphData,

		// 性能优化
		optimizeViewport,
		cleanupMemory,
		getPerformanceReport,

		// 工具方法
		analyzeDataChanges,
		deepEqual,
	};
}
