import { ref } from 'vue';
import * as go from 'gojs';

/**
 * 连线管理Hook
 * 负责连线创建、验证、路径更新等功能
 */
export function useLinkManagement() {
	// ===== 状态管理 =====
	const linkDataArray = ref<SingleLineLink[]>([]);

	/**
	 * 处理连线绘制完成事件
	 */
	const handleLinkDrawn = (e: go.DiagramEvent) => {
		const link = e.subject as go.Link;
		if (link) {
			console.log(`连线已绘制: ${link.data.key} (从 ${link.data.from} 到 ${link.data.to})`);
			// 确保连线立即可见和路径正确
			link.updateRoute();
		}
	};

	/**
	 * 处理连线重连完成事件
	 */
	const handleLinkRelinked = (e: go.DiagramEvent) => {
		const link = e.subject as go.Link;
		if (link) {
			console.log(`连线已重连: ${link.data.key}`);
			// 确保重连后的连线路径正确
			link.updateRoute();
		}
	};

	/**
	 * 更新所有连线的路径
	 */
	const updateAllLinkRoutes = (diagram: go.Diagram) => {
		if (!diagram) return;

		diagram.links.each((link: go.Link) => {
			link.updateRoute();
		});
	};

	/**
	 * 根据电压等级更新连线颜色
	 */
	const updateLinkColors = (diagram: go.Diagram, colorMapping: Record<string, string>) => {
		if (!diagram) return;

		diagram.startTransaction('update link colors');
		try {
			diagram.links.each((link: go.Link) => {
				const voltage = link.data?.voltage;
				if (voltage && colorMapping[voltage]) {
					diagram.model.setDataProperty(link.data, 'color', colorMapping[voltage]);
				}
			});
		} finally {
			diagram.commitTransaction('update link colors');
		}
	};

	/**
	 * 获取连接到指定节点的所有连线
	 */
	const getLinksConnectedToNode = (diagram: go.Diagram, nodeKey: string): go.Link[] => {
		if (!diagram) return [];

		const node = diagram.findNodeForKey(nodeKey);
		if (!node) return [];

		const links: go.Link[] = [];
		node.findLinksConnected().each((link: go.Link) => {
			links.push(link);
		});

		return links;
	};

	/**
	 * 获取从指定端口出发的连线
	 */
	const getLinksFromPort = (diagram: go.Diagram, nodeKey: string, portId: string): go.Link[] => {
		if (!diagram) return [];

		const node = diagram.findNodeForKey(nodeKey);
		if (!node) return [];

		const links: go.Link[] = [];
		node.findLinksOutOf(portId).each((link: go.Link) => {
			links.push(link);
		});

		return links;
	};

	/**
	 * 获取连接到指定端口的连线
	 */
	const getLinksToPort = (diagram: go.Diagram, nodeKey: string, portId: string): go.Link[] => {
		if (!diagram) return [];

		const node = diagram.findNodeForKey(nodeKey);
		if (!node) return [];

		const links: go.Link[] = [];
		node.findLinksInto(portId).each((link: go.Link) => {
			links.push(link);
		});

		return links;
	};

	/**
	 * 删除指定的连线
	 */
	const removeLink = (diagram: go.Diagram, linkKey: string): boolean => {
		if (!diagram) return false;

		const link = diagram.findLinkForKey(linkKey);
		if (!link) return false;

		diagram.startTransaction('remove link');
		try {
			diagram.model.removeLinkData(link.data);
			return true;
		} catch (error) {
			console.error('删除连线失败:', error);
			return false;
		} finally {
			diagram.commitTransaction('remove link');
		}
	};

	/**
	 * 添加新连线
	 */
	const addLink = (diagram: go.Diagram, linkData: SingleLineLink): boolean => {
		if (!diagram) return false;

		diagram.startTransaction('add link');
		try {
			if (diagram.model instanceof go.GraphLinksModel) {
				diagram.model.addLinkData(linkData);
				return true;
			}
			return false;
		} catch (error) {
			console.error('添加连线失败:', error);
			return false;
		} finally {
			diagram.commitTransaction('add link');
		}
	};

	/**
	 * 更新连线属性
	 */
	const updateLinkProperties = (diagram: go.Diagram, linkKey: string, properties: Partial<SingleLineLink>): boolean => {
		if (!diagram) return false;

		const linkData = diagram.model.findLinkDataForKey(linkKey);
		if (!linkData) return false;

		diagram.startTransaction('update link properties');
		try {
			Object.entries(properties).forEach(([key, value]) => {
				diagram.model.setDataProperty(linkData, key, value);
			});
			return true;
		} catch (error) {
			console.error('更新连线属性失败:', error);
			return false;
		} finally {
			diagram.commitTransaction('update link properties');
		}
	};

	/**
	 * 检查连线是否有效（两端都连接到有效节点）
	 */
	const isLinkValid = (diagram: go.Diagram, linkData: SingleLineLink): boolean => {
		if (!diagram) return false;

		const fromNode = diagram.findNodeForKey(linkData.from);
		const toNode = diagram.findNodeForKey(linkData.to);

		return !!(fromNode && toNode);
	};

	/**
	 * 清理无效连线（连接到不存在节点的连线）
	 */
	const cleanupInvalidLinks = (diagram: go.Diagram): number => {
		if (!diagram) return 0;

		const invalidLinks: go.Link[] = [];

		diagram.links.each((link: go.Link) => {
			if (!isLinkValid(diagram, link.data)) {
				invalidLinks.push(link);
			}
		});

		if (invalidLinks.length > 0) {
			diagram.startTransaction('cleanup invalid links');
			try {
				invalidLinks.forEach(link => {
					diagram.model.removeLinkData(link.data);
				});
			} finally {
				diagram.commitTransaction('cleanup invalid links');
			}
		}

		return invalidLinks.length;
	};

	/**
	 * 获取连线统计信息
	 */
	const getLinkStatistics = (diagram: go.Diagram) => {
		if (!diagram) return { total: 0, byVoltage: {}, byType: {} };

		const stats = {
			total: 0,
			byVoltage: {} as Record<string, number>,
			byType: {} as Record<string, number>,
		};

		diagram.links.each((link: go.Link) => {
			stats.total++;

			const voltage = link.data?.voltage || 'unknown';
			stats.byVoltage[voltage] = (stats.byVoltage[voltage] || 0) + 1;

			const type = link.data?.category || 'default';
			stats.byType[type] = (stats.byType[type] || 0) + 1;
		});

		return stats;
	};

	/**
	 * 批量更新连线数据
	 */
	const updateLinkDataArray = (newLinkData: SingleLineLink[]) => {
		linkDataArray.value = [...newLinkData];
	};

	/**
	 * 设置连线的可见性
	 */
	const setLinkVisibility = (diagram: go.Diagram, linkKey: string, visible: boolean): boolean => {
		if (!diagram) return false;

		const link = diagram.findLinkForKey(linkKey);
		if (!link) return false;

		diagram.startTransaction('set link visibility');
		try {
			link.visible = visible;
			return true;
		} finally {
			diagram.commitTransaction('set link visibility');
		}
	};

	/**
	 * 批量设置连线可见性
	 */
	const setBatchLinkVisibility = (diagram: go.Diagram, linkKeys: string[], visible: boolean): number => {
		if (!diagram) return 0;

		let updatedCount = 0;
		diagram.startTransaction('set batch link visibility');
		try {
			linkKeys.forEach(linkKey => {
				const link = diagram.findLinkForKey(linkKey);
				if (link) {
					link.visible = visible;
					updatedCount++;
				}
			});
		} finally {
			diagram.commitTransaction('set batch link visibility');
		}

		return updatedCount;
	};

	/**
	 * 高亮指定的连线
	 */
	const highlightLink = (diagram: go.Diagram, linkKey: string, highlight: boolean = true): boolean => {
		if (!diagram) return false;

		const link = diagram.findLinkForKey(linkKey);
		if (!link) return false;

		diagram.startTransaction('highlight link');
		try {
			if (highlight) {
				// 设置高亮样式
				diagram.model.setDataProperty(link.data, 'strokeWidth', 4);
				diagram.model.setDataProperty(link.data, 'stroke', '#ff0000');
			} else {
				// 恢复原始样式
				diagram.model.setDataProperty(link.data, 'strokeWidth', 2);
				diagram.model.setDataProperty(link.data, 'stroke', link.data.color || '#000000');
			}
			return true;
		} finally {
			diagram.commitTransaction('highlight link');
		}
	};

	return {
		// 状态
		linkDataArray,

		// 事件处理
		handleLinkDrawn,
		handleLinkRelinked,

		// 连线操作
		updateAllLinkRoutes,
		updateLinkColors,
		removeLink,
		addLink,
		updateLinkProperties,
		updateLinkDataArray,

		// 连线查询
		getLinksConnectedToNode,
		getLinksFromPort,
		getLinksToPort,
		isLinkValid,
		getLinkStatistics,

		// 连线管理
		cleanupInvalidLinks,
		setLinkVisibility,
		setBatchLinkVisibility,
		highlightLink,
	};
}
