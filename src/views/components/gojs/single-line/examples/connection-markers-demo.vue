<template>
  <div class="h-screen">
    <div class="p-4 bg-gray-100 border-b">
      <h2 class="text-lg font-bold mb-2">母线连接点标记演示</h2>
      <div class="flex space-x-4">
        <button @click="loadDemoData" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
          加载演示数据
        </button>
        <button @click="toggleMarkers" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
          {{ showMarkers ? '隐藏' : '显示' }}连接点标记
        </button>
        <button @click="updateMarkers" class="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600">
          更新标记
        </button>
        <button @click="updatePositions" class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
          更新位置
        </button>
        <button @click="clearMarkers" class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
          清除标记
        </button>
      </div>
    </div>
    
    <SingleLine 
      ref="singleLineRef"
      title="连接点标记演示"
      :showToolbar="true"
      :showResultToggle="false"
      :autoInitialize="true"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import SingleLine from '../index.vue';

const singleLineRef = ref<InstanceType<typeof SingleLine> | null>(null);
const showMarkers = ref(true);

// 演示数据 - 简化版本，确保能正常加载
const demoData = {
  nodes: [
    // 主母线
    {
      key: 'bus1',
      category: 'BusbarSection',
      type: 'BusbarSection',
      name: '220kV主母线',
      color: '#1c57ea',
      pos: '400 200',
      voltage: '220',
      width: 300,
      height: 8,
      properties: { vn_kv: 220.5 }
    },
    // 发电机
    {
      key: 'gen1',
      category: 'Synchronousmachine',
      type: 'Synchronousmachine',
      name: '发电机1',
      color: '#4ecdc4',
      pos: '200 100',
      voltage: '220',
      properties: { p_mw: 200.0, q_mvar: 80.0 }
    },
    // 负荷
    {
      key: 'load1',
      category: 'Load',
      type: 'Load',
      name: '负荷1',
      color: '#ff6b6b',
      pos: '300 300',
      voltage: '110',
      properties: { p_mw: 100.5, q_mvar: 50.2 }
    },
    {
      key: 'load2',
      category: 'Load',
      type: 'Load',
      name: '负荷2',
      color: '#ff6b6b',
      pos: '500 300',
      voltage: '110',
      properties: { p_mw: 80.0, q_mvar: 40.0 }
    },
  ],
  links: [
    // 发电机连接到主母线
    {
      key: 'link1',
      from: 'gen1',
      to: 'bus1',
      voltage: '220',
      color: '#1c57ea'
    },
    // 负荷1连接到母线
    {
      key: 'link2',
      from: 'load1',
      to: 'bus1',
      voltage: '220',
      color: '#1c57ea'
    },
    // 负荷2连接到母线
    {
      key: 'link3',
      from: 'load2',
      to: 'bus1',
      voltage: '220',
      color: '#1c57ea'
    },
  ]
};

const loadDemoData = () => {
  if (singleLineRef.value) {
    const success = singleLineRef.value.loadGraphData(demoData.nodes, demoData.links, {
      autoCenter: true,
      preserveViewport: false,
      showProperties: false
    });
    
    if (success) {
      console.log('演示数据加载成功');
      // 延迟更新连接点标记
      setTimeout(() => {
        updateMarkers();
      }, 500);
    }
  }
};

const toggleMarkers = () => {
  showMarkers.value = !showMarkers.value;
  if (singleLineRef.value) {
    singleLineRef.value.toggleConnectionMarkers(showMarkers.value);
  }
};

const updateMarkers = () => {
  if (singleLineRef.value) {
    singleLineRef.value.updateConnectionMarkers();
    console.log('连接点标记已更新');
  }
};

const updatePositions = () => {
  if (singleLineRef.value) {
    singleLineRef.value.updateConnectionMarkerPositions();
    console.log('连接点标记位置已更新');
  }
};

const clearMarkers = () => {
  if (singleLineRef.value) {
    singleLineRef.value.clearConnectionMarkers();
    console.log('连接点标记已清除');
  }
};

onMounted(() => {
  // 自动加载演示数据
  setTimeout(() => {
    loadDemoData();
  }, 1000);
});
</script>

<style scoped>
button {
  transition: all 0.2s ease;
}

button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
