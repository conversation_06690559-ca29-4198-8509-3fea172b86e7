# 站内接线图组件使用示例

## 三种使用场景

参考地理接线图组件的设计模式，站内接线图组件提供了三种主要的使用场景：

### 场景1：初始化空白图表

适用于页面加载时快速显示图表容器，提升用户体验。

```vue
<template>
  <div>
    <button @click="initGraph">初始化图表</button>
    <SingleLineComponent ref="singleLineRef" :autoInitialize="false" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import SingleLineComponent from '@/views/components/gojs/single-line/index.vue'

const singleLineRef = ref()

const initGraph = () => {
  const success = singleLineRef.value.initBlankGraph()
  if (success) {
    console.log('空白图表初始化成功')
  }
}
</script>
```

### 场景2：加载数据到图表

适用于向已预初始化的空白图表加载数据，性能优化场景。

```vue
<template>
  <div>
    <button @click="loadData">加载数据</button>
    <SingleLineComponent ref="singleLineRef" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import SingleLineComponent from '@/views/components/gojs/single-line/index.vue'

const singleLineRef = ref()

// 准备数据
const nodeData = [
  {
    key: 'bus1',
    category: 'BusbarSection',
    type: 'BusbarSection',
    name: '220kV母线',
    color: '#1c57ea',
    pos: [200, 100],
    voltage: '220',
    width: 150,
    properties: { vn_kv: 220.5, dt_vn_kv: 221.0 }
  },
  {
    key: 'load1',
    category: 'Load',
    type: 'Load',
    name: '负荷1',
    color: '#ff6b6b',
    pos: [400, 200],
    voltage: '110',
    properties: { p_mw: 100.5, q_mvar: 50.2 }
  },
  {
    key: 'gen1',
    category: 'Synchronousmachine',
    type: 'Synchronousmachine',
    name: '发电机1',
    color: '#4ecdc4',
    pos: [100, 200],
    voltage: '220',
    properties: { p_mw: 200.0, q_mvar: 80.0 }
  }
]

const linkData = [
  {
    key: 'link1',
    from: 'bus1',
    to: 'load1',
    fromPort: 'port1',
    toPort: 'port1',
    voltage: '220',
    color: '#1c57ea'
  },
  {
    key: 'link2',
    from: 'gen1',
    to: 'bus1',
    fromPort: 'port1',
    toPort: 'port2',
    voltage: '220',
    color: '#1c57ea'
  }
]

const loadData = () => {
  const success = singleLineRef.value.loadGraphData(nodeData, linkData, {
    autoCenter: true,
    preserveViewport: false,
    showProperties: true
  })
  
  if (success) {
    console.log('数据加载成功')
  }
}
</script>
```

### 场景3：更新图表数据

适用于数据变更、过滤、排序等需要完整重建的场景。

```vue
<template>
  <div>
    <button @click="updateData">更新数据</button>
    <button @click="incrementalUpdate">增量更新</button>
    <SingleLineComponent ref="singleLineRef" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import SingleLineComponent from '@/views/components/gojs/single-line/index.vue'

const singleLineRef = ref()

// 初始数据
const initialData = {
  nodes: [
    {
      key: 'bus1',
      category: 'BusbarSection',
      type: 'BusbarSection',
      name: '220kV母线',
      color: '#1c57ea',
      pos: [200, 100],
      voltage: '220'
    }
  ],
  links: []
}

// 更新后的数据
const updatedData = {
  nodes: [
    ...initialData.nodes,
    {
      key: 'load1',
      category: 'Load',
      type: 'Load',
      name: '新增负荷',
      color: '#ff6b6b',
      pos: [400, 200],
      voltage: '110'
    }
  ],
  links: [
    {
      key: 'link1',
      from: 'bus1',
      to: 'load1',
      voltage: '220',
      color: '#1c57ea'
    }
  ]
}

onMounted(() => {
  // 初始加载
  singleLineRef.value.loadGraphData(initialData.nodes, initialData.links)
})

const updateData = () => {
  // 完整更新模式
  const success = singleLineRef.value.updateGraphData(updatedData.nodes, updatedData.links, {
    incremental: false,
    forceLayout: true,
    preserveViewport: false
  })
  
  if (success) {
    console.log('数据更新成功')
  }
}

const incrementalUpdate = () => {
  // 增量更新模式（性能更好）
  const success = singleLineRef.value.updateGraphData(updatedData.nodes, updatedData.links, {
    incremental: true,
    forceLayout: false,
    preserveViewport: true
  })
  
  if (success) {
    console.log('增量更新成功')
  }
}
</script>
```

## 完整的业务集成示例

```vue
<template>
  <div class="substation-diagram">
    <div class="controls">
      <el-button @click="loadSubstationData" :loading="loading">加载变电站数据</el-button>
      <el-button @click="refreshData">刷新数据</el-button>
      <el-button @click="showPerformance">性能报告</el-button>
      <el-switch v-model="showProperties" @change="toggleProperties">显示属性</el-switch>
    </div>
    
    <SingleLineComponent 
      ref="singleLineRef"
      :showToolbar="true"
      :showPropertyPanel="true"
      @nodeSelected="handleNodeSelected"
      @modelChanged="handleModelChanged"
    />
    
    <div v-if="selectedNodeInfo" class="node-info">
      <h3>选中节点信息</h3>
      <p>名称: {{ selectedNodeInfo.name }}</p>
      <p>类型: {{ selectedNodeInfo.type }}</p>
      <p>电压: {{ selectedNodeInfo.voltage }}kV</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElButton, ElSwitch, ElMessage } from 'element-plus'
import SingleLineComponent from '@/views/components/gojs/single-line/index.vue'
import { getSubstationData } from '@/api/substation'

const singleLineRef = ref()
const loading = ref(false)
const showProperties = ref(false)
const selectedNodeInfo = ref(null)

// 业务数据获取
const loadSubstationData = async () => {
  loading.value = true
  try {
    // 调用业务API获取数据
    const response = await getSubstationData({
      substationId: 'substation-001',
      includeProperties: showProperties.value
    })
    
    // 转换为组件需要的格式
    const { nodes, links } = transformApiData(response.data)
    
    // 加载到图表
    const success = singleLineRef.value.loadGraphData(nodes, links, {
      autoCenter: true,
      showProperties: showProperties.value
    })
    
    if (success) {
      ElMessage.success('数据加载成功')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 数据格式转换
const transformApiData = (apiData) => {
  const nodes = apiData.devices.map(device => ({
    key: device.id,
    category: device.deviceType,
    type: device.deviceType,
    name: device.name,
    color: getDeviceColor(device.deviceType),
    pos: [device.x || 0, device.y || 0],
    voltage: device.voltage,
    properties: device.measurements || {}
  }))
  
  const links = apiData.connections.map((conn, index) => ({
    key: `link-${index}`,
    from: conn.fromDevice,
    to: conn.toDevice,
    fromPort: conn.fromPort,
    toPort: conn.toPort,
    voltage: conn.voltage,
    color: getVoltageColor(conn.voltage)
  }))
  
  return { nodes, links }
}

// 事件处理
const handleNodeSelected = (node) => {
  selectedNodeInfo.value = node
  console.log('选中节点:', node)
}

const handleModelChanged = (data) => {
  console.log('模型变化:', data)
  // 可以在这里保存用户的修改
}

const toggleProperties = (show) => {
  singleLineRef.value.toggleProperties(show)
}

const refreshData = () => {
  loadSubstationData()
}

const showPerformance = () => {
  const report = singleLineRef.value.getPerformanceReport()
  console.log('性能报告:', report)
  ElMessage.info(`节点数: ${report.totalNodes}, 更新耗时: ${report.lastUpdateDuration.toFixed(2)}ms`)
}

// 工具函数
const getDeviceColor = (deviceType) => {
  const colorMap = {
    'BusbarSection': '#1c57ea',
    'Load': '#ff6b6b',
    'Synchronousmachine': '#4ecdc4',
    'Trafo': '#f39c12',
    'Switch': '#95a5a6'
  }
  return colorMap[deviceType] || '#000000'
}

const getVoltageColor = (voltage) => {
  if (voltage >= 500) return '#e74c3c'
  if (voltage >= 220) return '#3498db'
  if (voltage >= 110) return '#2ecc71'
  return '#95a5a6'
}

onMounted(() => {
  // 页面加载时自动加载数据
  loadSubstationData()
})
</script>

<style scoped>
.substation-diagram {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.controls {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  gap: 12px;
  align-items: center;
}

.node-info {
  position: absolute;
  top: 80px;
  right: 16px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 12px;
  min-width: 200px;
}
</style>
```

这个示例展示了如何在实际业务中使用站内接线图组件，包括：

1. **数据获取**: 从业务API获取数据
2. **数据转换**: 将API数据转换为组件需要的格式
3. **事件处理**: 处理节点选择和模型变化事件
4. **性能监控**: 显示性能报告
5. **用户交互**: 属性显示切换、数据刷新等功能
