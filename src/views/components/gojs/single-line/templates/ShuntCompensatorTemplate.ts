import * as go from 'gojs';
import { createPortStyle, createNodeShape, createBaseNodeConfig, createBaseNodeBindings, createMainPanel } from './BaseTemplate';

/**
 * 并联电抗器节点模板
 */

/**
 * 创建并联电抗器节点模板
 * @returns GoJS Node模板
 */
export const createShuntCompensatorTemplate = () => {
	const $ = go.GraphObject.make;

	// 并联电抗器的几何路径
	const shuntPath =
		'M345.6 394.24c0-87.040 69.12-156.16 153.6-156.16s153.6 69.12 153.6 156.16-69.12 156.16-153.6 156.16c0 0 0 0 0 0' +
		'M509.44 230.4v143.36h-156.16' +
		'M522.24 540.16v133.12' +
		'M640 673.28h-232.96' +
		'M601.6 732.16h-156.16' +
		'M576 793.6h-104.96';

	return $(
		go.Node,
		'Spot',
		createBaseNodeConfig(),
		...createBaseNodeBindings(),
		createMainPanel(
			// 图标部分
			createNodeShape(shuntPath, '#1c57ea', 1.5, 8, 15),
			// 添加端口 - 只能连入，不能连出
			createPortStyle('O', new go.Spot(0.5, 0), true, true, 0, 1) // 顶部端口，只能连入
		)
	);
};
