/**
 * 节点模板统一导出
 * 提供所有电力系统设备节点模板的导出接口
 */

// 导出基础工具
export * from './BaseTemplate';

// 导出各个设备模板
export { createBusbarTemplate } from './BusbarTemplate';
export { createAclineTemplate } from './AclineTemplate';
export { createACLineDotTemplate } from './ACLineDotTemplate';
export { createTrafoTemplate } from './TrafoTemplate';
export { createTrafo3wTemplate } from './Trafo3wTemplate';
export { createSynchronousmachineTemplate } from './SynchronousmachineTemplate';
export { createLoadTemplate } from './LoadTemplate';
export { createSwitchTemplate } from './SwitchTemplate';
export { createShuntCompensatorTemplate } from './ShuntCompensatorTemplate';

// 导出模板管理器
export { TemplateManager, setupAllNodeTemplates, templateRegistry, type TemplateRegistry } from './TemplateManager';

// 导出默认设置函数
export { setupAllNodeTemplates as default } from './TemplateManager';
