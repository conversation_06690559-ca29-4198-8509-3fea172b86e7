import * as go from 'gojs';
import { createBusbarTemplate } from './BusbarTemplate';
import { createAclineTemplate } from './AclineTemplate';
import { createACLineDotTemplate } from './ACLineDotTemplate';
import { createTrafoTemplate } from './TrafoTemplate';
import { createTrafo3wTemplate } from './Trafo3wTemplate';
import { createSynchronousmachineTemplate } from './SynchronousmachineTemplate';
import { createLoadTemplate } from './LoadTemplate';
import { createSwitchTemplate } from './SwitchTemplate';
import { createShuntCompensatorTemplate } from './ShuntCompensatorTemplate';
import { createLabelNodeTemplate } from './BaseTemplate';

/**
 * 模板管理器
 * 负责注册和管理所有节点模板
 */

export interface TemplateRegistry {
	[key: string]: () => go.Node;
}

/**
 * 模板注册表
 * 包含所有电力系统设备的模板创建函数
 */
export const templateRegistry: TemplateRegistry = {
	BusbarSection: createBusbarTemplate,
	Acline: createAclineTemplate,
	ACLineDot: createACLineDotTemplate,
	Trafo: createTrafoTemplate,
	Trafo3w: createTrafo3wTemplate,
	Synchronousmachine: createSynchronousmachineTemplate,
	Load: createLoadTemplate,
	Switch: createSwitchTemplate,
	ShuntCompensator: createShuntCompensatorTemplate,
};

/**
 * 模板管理器类
 */
export class TemplateManager {
	private diagram: go.Diagram;

	constructor(diagram: go.Diagram) {
		this.diagram = diagram;
	}

	/**
	 * 注册所有节点模板到图表
	 */
	public registerAllTemplates(): void {
		Object.entries(templateRegistry).forEach(([templateType, createTemplate]) => {
			this.diagram.nodeTemplateMap.add(templateType, createTemplate());
		});
	}

	/**
	 * 注册单个模板
	 * @param templateType 模板类型
	 * @param createTemplate 模板创建函数
	 */
	public registerTemplate(templateType: string, createTemplate: () => go.Node): void {
		this.diagram.nodeTemplateMap.add(templateType, createTemplate());
	}

	/**
	 * 获取已注册的模板类型列表
	 * @returns 模板类型数组
	 */
	public getRegisteredTemplateTypes(): string[] {
		return Object.keys(templateRegistry);
	}

	/**
	 * 检查模板是否已注册
	 * @param templateType 模板类型
	 * @returns 是否已注册
	 */
	public isTemplateRegistered(templateType: string): boolean {
		return templateType in templateRegistry;
	}
}

/**
 * 创建ConnectionMarker模板 - 母线连接点标记（丝滑版本）
 */
const createConnectionMarkerTemplate = (): go.Node => {
	const $ = go.GraphObject.make;

	return $(
		go.Node,
		'Spot',
		{
			layerName: 'Foreground',
			locationSpot: go.Spot.Center,
			selectable: false,
			avoidable: false,
			pickable: false,
			width: 10,
			height: 10,
			// 支持位置绑定（类似LabelNode）
		},
		// 外圈 - 白色背景，提供对比度
		$(
			go.Shape,
			'Circle',
			{
				fill: 'white',
				stroke: null,
				width: 10,
				height: 10,
				alignment: go.Spot.Center,
			},
			new go.Binding('visible', 'visible').makeTwoWay()
		),
		// 内圈 - 红色×标记
		$(
			go.Shape,
			'XLine', // 使用X形状作为连接点标记
			{
				fill: 'transparent',
				stroke: '#ff4444',
				strokeWidth: 2.5,
				width: 8,
				height: 8,
				alignment: go.Spot.Center,
			},
			new go.Binding('stroke', 'color').makeTwoWay(),
			new go.Binding('visible', 'visible').makeTwoWay()
		),
		// 位置绑定（类似LabelNode的loc绑定）
		new go.Binding('location', 'loc', go.Point.parse).makeTwoWay(go.Point.stringify)
	);
};

/**
 * 创建PropertyNode模板
 */
const createPropertyNodeTemplate = (): go.Node => {
	const $ = go.GraphObject.make;

	return $(
		go.Node,
		'Auto',
		{
			layerName: 'Foreground',
			locationSpot: go.Spot.TopLeft,
			selectable: false,
			avoidable: false,
			pickable: false,
		},
		$(go.Shape, 'RoundedRectangle', {
			fill: 'rgba(255, 255, 255, 0.9)',
			stroke: '#ccc',
			strokeWidth: 1,
			parameter1: 4,
		}),
		$(
			go.TextBlock,
			{
				margin: 4,
				font: '10px Arial',
				stroke: '#333',
				textAlign: 'left',
				verticalAlignment: go.Spot.Top,
			},
			new go.Binding('text', 'properties')
		)
	);
};

/**
 * 便捷函数：直接为图表设置所有节点模板
 * @param diagram GoJS图表实例
 */
export const setupAllNodeTemplates = (diagram: go.Diagram): void => {
	const manager = new TemplateManager(diagram);
	manager.registerAllTemplates();

	// 注册LabelNode模板
	diagram.nodeTemplateMap.add('LabelNode', createLabelNodeTemplate());

	// 注册PropertyNode模板
	diagram.nodeTemplateMap.add('PropertyNode', createPropertyNodeTemplate());

	// 注册ConnectionMarker模板
	diagram.nodeTemplateMap.add('ConnectionMarker', createConnectionMarkerTemplate());
};
