import * as go from 'gojs';
import { createBaseNodeBindings } from './BaseTemplate';

/**
 * 创建母线节点模板 - 基于GoJS HBar示例设计
 * 特点：
 * - 整个矩形作为一个端口，可在任意位置连接
 * - 使用内置resizable机制，左右调整手柄
 * - 智能连接点计算（需配合BusLink使用）
 * @returns GoJS Node模板
 */
export const createBusbarTemplate = () => {
	const $ = go.GraphObject.make;

	return $(
		go.Node,
		'Spot',
		{
			layerName: 'Background', // 母线放在背景层，避免遮挡其他元素
			locationSpot: go.Spot.Center,
			selectionObjectName: 'SHAPE',

			// === 启用内置调整大小机制 ===
			resizable: true,
			resizeObjectName: 'SHAPE',

			// === 自定义调整手柄 - 仅左右两端 ===
			resizeAdornmentTemplate: $(
				go.Adornment,
				'Spot',
				$(go.Placeholder),

				// 左侧调整手柄
				$(go.Shape, {
					alignment: go.Spot.Left,
					cursor: 'col-resize',
					desiredSize: new go.Size(6, 6),
					fill: 'lightblue',
					stroke: 'dodgerblue',
				}),

				// 右侧调整手柄
				$(go.Shape, {
					alignment: go.Spot.Right,
					cursor: 'col-resize',
					desiredSize: new go.Size(6, 6),
					fill: 'lightblue',
					stroke: 'dodgerblue',
				})
			),

			rotatable: false,
			fromLinkable: false,
			toLinkable: false,
			cursor: 'move',
			movable: true,
			dragComputation: function (node, pt, gridpt) {
				return gridpt; // 对齐到网格
			},
		},
		...createBaseNodeBindings(),

		// === 母线矩形主体 ===
		$(
			go.Shape,
			'Rectangle', // 使用Rectangle而不是LineH
			{
				name: 'SHAPE',
				fill: 'black', // 母线填充为黑色
				stroke: null,
				strokeWidth: 0,
				width: 150, // 默认宽度
				height: 4, // 固定高度
				minSize: new go.Size(50, 4), // 最小尺寸
				maxSize: new go.Size(500, 4), // 最大尺寸

				// === 关键：整个矩形作为端口 ===
				portId: '', // 空字符串portId，表示整个形状都是端口
				toLinkable: true, // 允许连线连入
				fromLinkable: false, // 母线本身不连出线

				// === 修复：确保拖动优先于连接 ===
				cursor: 'move', // 明确设置为拖动光标
				pickable: true, // 确保可以被拾取用于拖动
				mouseEnter: function (e, shape) {
					// 禁用连接工具在母线上的激活
					if (e.diagram && e.diagram.toolManager && e.diagram.toolManager.linkingTool) {
						e.diagram.toolManager.linkingTool.isEnabled = false;
					}
				},
				mouseLeave: function (e, shape) {
					// 重新启用连接工具
					if (e.diagram && e.diagram.toolManager && e.diagram.toolManager.linkingTool) {
						e.diagram.toolManager.linkingTool.isEnabled = true;
					}
				},
			},
			// 宽度数据绑定
			new go.Binding('desiredSize', 'width', (w) => {
				const width = w === undefined || w === null ? 150 : w;
				return new go.Size(width, 4);
			}).makeTwoWay((size) => size.width),
			// 颜色绑定
			new go.Binding('fill', 'color')
		)
	);
};
