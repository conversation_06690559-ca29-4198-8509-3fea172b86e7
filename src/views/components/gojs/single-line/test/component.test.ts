import { describe, it, expect, beforeEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { ref } from 'vue';
import SingleLineComponent from '../index.vue';

// Mock dependencies
vi.mock('element-plus', () => ({
	ElMessage: {
		success: vi.fn(),
		error: vi.fn(),
		warning: vi.fn(),
	},
}));

vi.mock('/@/config/GraphConfig', () => ({
	getVoltageColor: vi.fn((voltage) => `#${voltage || '000000'}`),
	spacer: {},
}));

vi.mock('../api', () => ({
	query_single_line_graph: vi.fn(() => Promise.resolve({
		data: {
			nodes: [
				{
					id: 'node1',
					type: 'BusbarSection',
					name: '测试母线',
					voltage: '220',
					properties: { vn_kv: 220.5 }
				},
				{
					id: 'node2',
					type: 'Load',
					name: '测试负荷',
					voltage: '110',
					properties: { p_mw: 100.5, q_mvar: 50.2 }
				}
			],
			edges: [
				{
					source: 'node1',
					target: 'node2',
					source_port: 'port1',
					target_port: 'port2',
					voltage: '220'
				}
			]
		}
	})),
}));

describe('SingleLineComponent', () => {
	let wrapper: any;
	const mockDataPacket = ref({
		id: 'test-case-id'
	});

	const mockNodeInfo = {
		key: 'test-substation-id'
	};

	beforeEach(() => {
		// 提供必要的依赖注入
		wrapper = mount(SingleLineComponent, {
			props: {
				nodeInfo: mockNodeInfo
			},
			global: {
				provide: {
					dataPacket: mockDataPacket
				},
				stubs: {
					// 模拟GoJS相关的DOM操作
					'div': {
						template: '<div><slot /></div>'
					}
				}
			}
		});
	});

	describe('组件初始化', () => {
		it('应该正确渲染组件结构', () => {
			expect(wrapper.find('.single-line-diagram-container').exists()).toBe(true);
			expect(wrapper.find('.w-64.bg-gray-50').exists()).toBe(true); // 组件库区域
			expect(wrapper.find('.flex-1.flex.flex-col').exists()).toBe(true); // 主内容区域
		});

		it('应该包含搜索框和分类选择器', () => {
			expect(wrapper.find('input[placeholder="搜索组件..."]').exists()).toBe(true);
			expect(wrapper.find('select').exists()).toBe(true);
		});

		it('应该包含工具栏按钮', () => {
			expect(wrapper.find('button').exists()).toBe(true);
			const buttons = wrapper.findAll('button');
			expect(buttons.length).toBeGreaterThan(0);
		});
	});

	describe('数据获取功能', () => {
		it('应该在挂载时自动加载数据', async () => {
			// 等待组件完全挂载
			await wrapper.vm.$nextTick();
			
			// 验证数据获取hook是否被正确初始化
			expect(wrapper.vm.dataFetching).toBeDefined();
			expect(wrapper.vm.dataFetching.isLoading).toBeDefined();
		});

		it('应该正确处理API响应数据', async () => {
			await wrapper.vm.$nextTick();
			
			// 验证数据处理
			expect(wrapper.vm.dataFetching.hasData).toBeDefined();
			expect(wrapper.vm.dataFetching.parsedNodes).toBeDefined();
			expect(wrapper.vm.dataFetching.parsedLinks).toBeDefined();
		});
	});

	describe('Hook集成', () => {
		it('应该正确初始化所有hooks', () => {
			expect(wrapper.vm.dataFetching).toBeDefined();
			expect(wrapper.vm.diagramManagement).toBeDefined();
			expect(wrapper.vm.nodeManagement).toBeDefined();
			expect(wrapper.vm.linkManagement).toBeDefined();
			expect(wrapper.vm.propertyDisplay).toBeDefined();
			expect(wrapper.vm.palette).toBeDefined();
		});

		it('应该正确暴露组件接口', () => {
			const exposedMethods = [
				'loadData',
				'clearData',
				'updateNodeText',
				'updateNodeColor',
				'saveModel',
				'loadModel',
				'getPerformanceReport',
				'cleanupMemory'
			];

			exposedMethods.forEach(method => {
				expect(typeof wrapper.vm[method]).toBe('function');
			});
		});
	});

	describe('性能优化功能', () => {
		it('应该包含增量更新功能', () => {
			expect(wrapper.vm.dataFetching.incrementalUpdate).toBeDefined();
			expect(wrapper.vm.dataFetching.incrementalUpdate.isUpdating).toBeDefined();
			expect(wrapper.vm.dataFetching.incrementalUpdate.performanceMetrics).toBeDefined();
		});

		it('应该提供性能监控方法', () => {
			expect(typeof wrapper.vm.getPerformanceReport).toBe('function');
			expect(typeof wrapper.vm.cleanupMemory).toBe('function');
		});
	});

	describe('用户交互', () => {
		it('应该响应属性显示切换', async () => {
			const toggleButton = wrapper.find('button');
			await toggleButton.trigger('click');
			
			// 验证showResult状态变化
			expect(wrapper.vm.showResult).toBeDefined();
		});

		it('应该支持模型保存和加载', async () => {
			// 模拟localStorage
			Object.defineProperty(window, 'localStorage', {
				value: {
					getItem: vi.fn(),
					setItem: vi.fn(),
				},
				writable: true
			});

			await wrapper.vm.saveModel();
			expect(window.localStorage.setItem).toHaveBeenCalled();
		});
	});

	describe('错误处理', () => {
		it('应该正确处理API错误', async () => {
			// 模拟API错误
			const mockError = new Error('API Error');
			vi.mocked(require('../api').query_single_line_graph).mockRejectedValueOnce(mockError);

			await wrapper.vm.loadSingleLineData();
			
			// 验证错误处理
			expect(wrapper.vm.dataFetching.error).toBeDefined();
		});

		it('应该在图表未初始化时正确处理', () => {
			// 测试在图表未初始化时调用方法
			expect(() => {
				wrapper.vm.updateNodeText();
				wrapper.vm.updateNodeColor();
			}).not.toThrow();
		});
	});
});

describe('Hook单元测试', () => {
	describe('useDataFetching', () => {
		it('应该正确格式化属性文本', async () => {
			const { useDataFetching } = await import('../hooks/useDataFetching');
			const dataFetching = useDataFetching();

			const testNode = {
				type: 'BusbarSection',
				properties: {
					vn_kv: 220.5,
					dt_vn_kv: 221.0
				}
			};

			const result = dataFetching.formatPropertyText(testNode);
			expect(result).toContain('U: 220.50');
			expect(result).toContain('U(M): 221.00');
		});
	});

	describe('useIncrementalUpdate', () => {
		it('应该正确分析数据变化', async () => {
			const { useIncrementalUpdate } = await import('../hooks/useIncrementalUpdate');
			const incrementalUpdate = useIncrementalUpdate();

			const oldData = [{ key: 'node1', name: 'old' }];
			const newData = [
				{ key: 'node1', name: 'updated' },
				{ key: 'node2', name: 'new' }
			];

			// 模拟缓存
			incrementalUpdate.nodeCache.value.set('node1', { key: 'node1', name: 'old' });

			const changes = incrementalUpdate.analyzeDataChanges(newData, []);
			
			expect(changes.addedNodes).toHaveLength(1);
			expect(changes.updatedNodes).toHaveLength(1);
			expect(changes.addedNodes[0].key).toBe('node2');
			expect(changes.updatedNodes[0].key).toBe('node1');
		});

		it('应该正确进行深度比较', async () => {
			const { useIncrementalUpdate } = await import('../hooks/useIncrementalUpdate');
			const incrementalUpdate = useIncrementalUpdate();

			const obj1 = { a: 1, b: { c: 2 } };
			const obj2 = { a: 1, b: { c: 2 } };
			const obj3 = { a: 1, b: { c: 3 } };

			expect(incrementalUpdate.deepEqual(obj1, obj2)).toBe(true);
			expect(incrementalUpdate.deepEqual(obj1, obj3)).toBe(false);
		});
	});
});
