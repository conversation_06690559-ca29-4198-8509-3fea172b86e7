import * as go from 'gojs';

// 创建公共端口样式函数
const createPortInfo = (
	id: string,
	alignment: go.Spot,
	fromLinkable: boolean = true,
	toLinkable: boolean = true,
	fromMaxLinks: number = Infinity, // 默认无限制
	toMaxLinks: number = Infinity // 默认无限制
): PortInfo => {
	// 根据位置判断端口的连接方向
	let fromSpot = go.Spot.AllSides;
	let toSpot = go.Spot.AllSides;

	// 根据端口位置设置更精确的连接点方向
	if (alignment.equals(go.Spot.TopLeft) || alignment.equals(go.Spot.Top) || alignment.equals(go.Spot.TopRight)) {
		fromSpot = go.Spot.Top;
		toSpot = go.Spot.Top;
	} else if (alignment.equals(go.Spot.BottomLeft) || alignment.equals(go.Spot.Bottom) || alignment.equals(go.Spot.BottomRight)) {
		fromSpot = go.Spot.Bottom;
		toSpot = go.Spot.Bottom;
	} else if (alignment.equals(go.Spot.Left)) {
		fromSpot = go.Spot.Left;
		toSpot = go.Spot.Left;
	} else if (alignment.equals(go.Spot.Right)) {
		fromSpot = go.Spot.Right;
		toSpot = go.Spot.Right;
	} else if (alignment.x < 0.4) {
		fromSpot = go.Spot.Left;
		toSpot = go.Spot.Left;
	} else if (alignment.x > 0.6) {
		fromSpot = go.Spot.Right;
		toSpot = go.Spot.Right;
	}

	return {
		id,
		alignment,
		fromSpot, // 根据位置设置方向
		toSpot, // 根据位置设置方向
		fromLinkable,
		toLinkable,
		fromMaxLinks, // 最大可连出线数量
		toMaxLinks, // 最大可连入线数量
	};
};

const icons: IconsCollection = {
	BusbarSection: {
		path: 'M263.68 517.12h512',
		width: 30,
		height: 2,
		name: '母线',
		type: 'BusbarSection',
		defaultColor: '#1c57ea', // 更新颜色为黑色，与新模板一致
		strokeWidth: 4,
		ports: [createPortInfo('', new go.Spot(0.5, 0.5), false, true, 0, Infinity)], // 空字符串portId，整条母线作为端口
	},
	Acline: {
		path:
			'F M252.416 570.88c0-33.932 27.508-61.44 61.44-61.44s61.44 27.508 61.44 61.44v-0 ' +
			'M650.752 572.928c4.82-34.459 34.099-60.69 69.504-60.69s64.684 26.232 69.461 60.32l0.043 0.37 ' +
			'M377.344 572.928c0-37.184 30.144-67.328 67.328-67.328s67.328 30.144 67.328 67.328v0 ' +
			'M512 572.928c-0.082-1.243-0.128-2.695-0.128-4.158 0-37.326 30.258-67.584 67.584-67.584s67.584 30.258 67.584 67.584c0 1.463-0.046 2.914-0.138 4.354l0.010-0.197',
		width: 22,
		height: 3,
		name: '交流线路',
		type: 'Acline',
		defaultColor: '#1c57ea',
		strokeWidth: 1.5,
		ports: [
			createPortInfo('J', new go.Spot(0, 0.5), true, true, 1, 1), // 左右端口各只允许1个连接
			createPortInfo('O', new go.Spot(1, 0.5), true, true, 1, 1),
		],
	},
	ACLineDot: {
		path: 'M512 0c-282.77 0-512 229.23-512 512s229.23 512 512 512 512-229.23 512-512-229.23-512-512-512zM512 896c-212.078 0-384-171.922-384-384s171.922-384 384-384c212.078 0 384 171.922 384 384s-171.922 384-384 384zM320 512c0-106.039 85.961-192 192-192s192 85.961 192 192c0 106.039-85.961 192-192 192s-192-85.961-192-192z',
		width: 8,
		height: 8,
		name: '线端',
		type: 'ACLineDot',
		defaultColor: '#1c57ea',
		strokeWidth: 1.5,
		ports: [createPortInfo('J', new go.Spot(0.5, 0), true, true, 1, 1)],
	},
	Trafo: {
		path:
			'M706.56 409.6c0 96.142-80.231 174.080-179.2 174.080s-179.2-77.938-179.2-174.080c0-96.142 80.231-174.080 179.2-174.080s179.2 77.938 179.2 174.080z' +
			'M706.56 624.64c0 96.142-80.231 174.080-179.2 174.080s-179.2-77.938-179.2-174.080c0-96.142 80.231-174.080 179.2-174.080s179.2 77.938 179.2 174.080z',
		width: 14,
		height: 22,
		name: '两绕变',
		type: 'Trafo',
		defaultColor: '#1c57ea',
		strokeWidth: 1.5,
		ports: [createPortInfo('H', new go.Spot(0.5, 0), true, true, 1, 1), createPortInfo('L', new go.Spot(0.5, 1), true, true, 1, 1)],
	},
	Trafo3w: {
		path:
			'M642.56 419.84c0 87.659-69.915 158.72-156.16 158.72s-156.16-71.061-156.16-158.72c0-87.659 69.915-158.72 156.16-158.72s156.16 71.061 156.16 158.72z' +
			'M545.28 616.96c0 87.659-69.915 158.72-156.16 158.72s-156.16-71.061-156.16-158.72c0-87.659 69.915-158.72 156.16-158.72s156.16 71.061 156.16 158.72z' +
			'M744.96 616.96c0 87.659-69.915 158.72-156.16 158.72s-156.16-71.061-156.16-158.72c0-87.659 69.915-158.72 156.16-158.72s156.16 71.061 156.16 158.72z',
		width: 20,
		height: 20,
		name: '三绕变',
		type: 'Trafo3w',
		defaultColor: '#1c57ea',
		strokeWidth: 1.5,
		ports: [
			createPortInfo('H', new go.Spot(0.5, 0), true, true, 1, 1),
			createPortInfo('M', new go.Spot(0, 0.7), true, true, 1, 1),
			createPortInfo('L', new go.Spot(1, 0.7), true, true, 1, 1),
		],
	},
	Synchronousmachine: {
		path:
			'M737.28 545.28c0 108.866-91.692 197.12-204.8 197.12s-204.8-88.254-204.8-197.12c0-108.866 91.692-197.12 204.8-197.12s204.8 88.254 204.8 197.12z' +
			'M404.48 547.84c0-33.28 28.16-61.44 64-61.44s64 28.16 64 61.44' +
			'M660.48 545.28c0 33.28-28.16 61.44-64 61.44s-64-28.16-64-61.44' +
			'M527.36 281.6v61.44',
		width: 16,
		height: 18,
		name: '发电机',
		type: 'Synchronousmachine',
		defaultColor: '#1c57ea',
		strokeWidth: 1.5,
		ports: [createPortInfo('O', new go.Spot(0.5, 0), true, true, 1, 1)],
	},
	Load: {
		path: 'M232.96 363.52h563.2v307.2h-563.2v-307.2z' + 'M232.96 368.64l558.080 296.96' + 'M232.96 665.6l558.080-296.96',
		width: 22,
		height: 12,
		name: '负荷',
		type: 'Load',
		defaultColor: '#1c57ea',
		strokeWidth: 1.5,
		ports: [createPortInfo('O', new go.Spot(0.5, 0), true, true, 0, 1)], // 负荷只能连入，不能连出
	},
	Switch: {
		path: 'M281.6 286.976v460.8h460.8v-460.8zM512 389.376v256',
		width: 12,
		height: 12,
		name: '开关',
		type: 'Switch',
		defaultColor: '#1c57ea',
		strokeWidth: 1.5,
		ports: [createPortInfo('I', new go.Spot(0.5, 0), true, true, 1, 1), createPortInfo('J', new go.Spot(0.5, 1), true, true, 1, 1)],
	},
	// 'equivalent-external-grid': {
	// 	path:
	// 		'M258.56 337.92l94.72-94.72' +
	// 		'M256 442.88l199.68-199.68' +
	// 		'M256 540.16l302.080-296.96' +
	// 		'M657.92 243.2l94.72 92.16' +
	// 		'M547.84 238.080l204.8 194.56' +
	// 		'M450.56 238.080l302.080 299.52' +
	// 		'M256 232.96h509.44v504.32h-509.44v-504.32z' +
	// 		'M256 640l404.48-399.36' +
	// 		'M256 739.84l506.88-499.2' +
	// 		'M762.88 632.32l-104.96 97.28' +
	// 		'M765.44 522.24l-207.36 207.36' +
	// 		'M765.44 424.96l-307.2 304.64' +
	// 		'M765.44 327.68l-404.48 401.92' +
	// 		'M360.96 737.28l-97.28-94.72' +
	// 		'M471.040 742.4l-207.36-199.68' +
	// 		'M565.76 742.4l-302.080-302.080' +
	// 		'M668.16 742.4l-407.040-404.48' +
	// 		'M768 742.4l-506.88-499.2' +
	// 		'M350.72 238.080l407.040 401.92' +
	// 		'M512 734.72v61.44',
	// 	width: 20,
	// 	height: 22,
	// 	name: '等值外电网',
	// 	type: 'equivalent-external-grid',
	// 	defaultColor: '#1c57ea',
	// 	strokeWidth: 1.5,
	// 	ports: [createPortInfo('top', new go.Spot(0.5, 0), true, true, 1, 1)],
	// },
	ShuntCompensator: {
		path:
			'M345.6 394.24c0-87.040 69.12-156.16 153.6-156.16s153.6 69.12 153.6 156.16-69.12 156.16-153.6 156.16c0 0 0 0 0 0' +
			'M509.44 230.4v143.36h-156.16' +
			'M522.24 540.16v133.12' +
			'M640 673.28h-232.96' +
			'M601.6 732.16h-156.16' +
			'M576 793.6h-104.96',
		width: 12,
		height: 22,
		name: '并联电抗器',
		type: 'ShuntCompensator',
		defaultColor: '#1c57ea',
		strokeWidth: 1.5,
		ports: [createPortInfo('O', new go.Spot(0.5, 0), true, true, 0, 1)], // 只能连入，不能连出
	},
	// 'shunt-capacitor': {
	// 	path:
	// 		'M284.16 238.080h407.040' +
	// 		'M284.16 450.56h407.040' +
	// 		'M335.36 673.28h307.2' +
	// 		'M378.88 734.72h220.16' +
	// 		'M430.080 801.28h117.76' +
	// 		'M488.96 453.12v207.36',
	// 	width: 16,
	// 	height: 22,
	// 	name: '并联电容器',
	// 	type: 'shunt-capacitor',
	// 	defaultColor: '#1c57ea',
	// 	strokeWidth: 1.5,
	// 	ports: [createPortInfo('top', new go.Spot(0.5, 0), true, true, 0, 1)], // 只能连入，不能连出
	// },
	// 'knife-switch': {
	// 	path:
	// 		'F M198.912 572.672h248.064v30.72h-248.064v-30.72z ' +
	// 		'M577.024 572.672h248.064v30.72h-248.064v-30.72z ' +
	// 		'M428.73 578.029l199.922-146.857 18.187 24.758-199.922 146.857-18.187-24.758z',
	// 	width: 24,
	// 	height: 6,
	// 	name: '刀闸',
	// 	type: 'knife-switch',
	// 	defaultColor: '#1c57ea',
	// 	strokeWidth: 1.5,
	// 	ports: [createPortInfo('left', new go.Spot(0, 0.8), true, true, 1, 1), createPortInfo('right', new go.Spot(1, 0.8), true, true, 1, 1)],
	// },
};

export default icons;
