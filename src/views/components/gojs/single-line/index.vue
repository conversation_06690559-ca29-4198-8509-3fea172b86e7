<template>
	<div class="single-line-diagram-container h-full flex">
		<!-- 左侧组件库 -->
		<div class="w-64 bg-gray-50 border-r border-gray-200 flex flex-col">
			<!-- 组件库标题 -->
			<div class="p-3 border-b border-gray-200 bg-white">
				<h3 class="text-sm font-medium text-gray-900">组件库</h3>
			</div>

			<!-- 搜索框 -->
			<div class="p-3 border-b border-gray-200">
				<input
					v-model="palette.searchTerm.value"
					type="text"
					placeholder="搜索组件..."
					class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
				/>
			</div>

			<!-- 分类选择 -->
			<div class="p-3 border-b border-gray-200">
				<select
					v-model="palette.selectedCategory.value"
					class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
				>
					<option v-for="category in palette.categoryList.value" :key="category.key" :value="category.key">
						{{ category.name }} ({{ category.count }})
					</option>
				</select>
			</div>

			<!-- 组件库内容 -->
			<div class="flex-1 overflow-hidden">
				<div ref="paletteRef" class="w-full h-full"></div>
			</div>
		</div>

		<!-- 右侧主要内容区域 -->
		<div class="flex-1 flex flex-col">
			<!-- 工具栏 -->
			<div class="h-12 bg-white border-b border-gray-200 flex items-center justify-between px-4">
				<div class="flex items-center space-x-4">
					<h2 class="text-lg font-medium text-gray-900">站内接线图</h2>
					<div class="flex items-center space-x-2">
						<button
							@click="showResult = !showResult"
							:class="[
								'flex items-center px-3 py-1.5 text-sm rounded-md transition-colors',
								showResult
									? 'bg-blue-100 text-blue-700 hover:bg-blue-200'
									: 'bg-gray-100 text-gray-700 hover:bg-gray-200'
							]"
						>
							<component :is="showResult ? View : Hide" class="w-4 h-4 mr-1" />
							{{ showResult ? '隐藏属性' : '显示属性' }}
						</button>
					</div>
				</div>

				<div class="flex items-center space-x-2">
					<button
						@click="saveModel"
						class="px-3 py-1.5 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
					>
						保存模型
					</button>
					<button
						@click="loadModel"
						class="px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
					>
						加载模型
					</button>
				</div>
			</div>

			<!-- 图表区域 -->
			<div class="flex-1 flex">
				<!-- 图表画布 -->
				<div class="flex-1 relative">
					<div ref="diagramRef" class="w-full h-full bg-white"></div>
					<!-- 加载状态 -->
					<div
						v-if="dataFetching.isLoading.value"
						class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center"
					>
						<div class="text-center">
							<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
							<p class="text-sm text-gray-600">加载中...</p>
						</div>
					</div>
				</div>

				<!-- 右侧属性面板 -->
				<div
					v-if="nodeManagement.selectedNode.value"
					class="w-64 bg-gray-50 border-l border-gray-200 flex flex-col"
				>
					<div class="p-3 border-b border-gray-200 bg-white">
						<h3 class="text-sm font-medium text-gray-900">节点属性</h3>
					</div>

					<div class="flex-1 p-3 space-y-3 custom-scrollbar overflow-y-auto">
						<div class="flex items-center py-1.5">
							<div class="w-16 text-xs text-gray-600">名称</div>
							<div class="flex-1">
								<input
									type="text"
									v-model="nodeManagement.nodeText.value"
									@change="nodeManagement.updateNodeText(diagramManagement._getDiagram())"
									class="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:border-blue-500"
								/>
							</div>
						</div>

						<div class="flex items-center py-1.5">
							<div class="w-16 text-xs text-gray-600">颜色</div>
							<div class="flex-1">
								<input
									type="color"
									v-model="nodeManagement.nodeColor.value"
									@change="nodeManagement.updateNodeColor(diagramManagement._getDiagram())"
									class="w-full h-6 border border-gray-300 rounded"
								/>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed, inject, ComputedRef } from 'vue';
import * as go from 'gojs';
import { ElMessage } from 'element-plus';
import { View, Hide } from '@element-plus/icons-vue';
import icons from './icons';

// 导入优化后的hooks
import { useDataFetching } from './hooks/useDataFetching';
import { useDiagramManagement } from './hooks/useDiagramManagement';
import { useNodeManagement } from './hooks/useNodeManagement';
import { useLinkManagement } from './hooks/useLinkManagement';
import { usePropertyDisplay } from './hooks/usePropertyDisplay';
import { usePalette } from './hooks/usePalette';

// ===== 组件配置 =====
const dataPacket = inject('dataPacket') as ComputedRef<Recordable>;
const componentObjects = Object.values(icons);

const props = defineProps({
	nodeInfo: {
		type: Object,
		default: () => ({}),
	},
});

// 定义事件抛出
const emit = defineEmits<{
	nodeSelected: [node: any | null];
}>();

// ===== DOM引用 =====
const diagramRef = ref<HTMLElement | null>(null);
const paletteRef = ref<HTMLElement | null>(null);

// ===== 初始化Hooks =====
const dataFetching = useDataFetching();
const diagramManagement = useDiagramManagement();
const nodeManagement = useNodeManagement();
const linkManagement = useLinkManagement();
const propertyDisplay = usePropertyDisplay();
const palette = usePalette(paletteRef, componentObjects, {
	enableSearch: true,
	enableCategorization: true,
});

// ===== 计算属性 =====
const showResult = computed({
	get: () => propertyDisplay.showResult.value,
	set: (value: boolean) => propertyDisplay.setShowResult(diagramManagement._getDiagram(), value)
});

/**
 * 加载单线图数据
 */
async function loadSingleLineData() {
	try {
		const result = await dataFetching.fetchData({
			bb_case_id: dataPacket.value.id,
			substation_id: props.nodeInfo.key,
		}, showResult.value);

		const diagram = diagramManagement._getDiagram();
		if (!diagram) {
			throw new Error('图表实例未初始化');
		}

		// 确保没有正在进行的事务
		if (diagram.isModified) {
			try {
				diagram.commitTransaction('清理旧事务');
			} catch (e) {
				console.warn('尝试提交未知事务:', e);
				diagram.rollbackTransaction();
			}
		}

		// 创建新的模型
		const newModel = new go.GraphLinksModel();
		newModel.linkFromPortIdProperty = 'fromPort';
		newModel.linkToPortIdProperty = 'toPort';
		newModel.linkKeyProperty = 'key';

		// 使用增量更新加载数据
		if (dataFetching.hasData.value) {
			// 如果已有数据，使用增量更新
			dataFetching.incrementalUpdate.updateGraphData(diagram, result.nodes, result.links);
		} else {
			// 首次加载，使用全量加载
			dataFetching.incrementalUpdate.loadGraphData(diagram, result.nodes, result.links);

			// 设置布局（仅首次加载时）
			diagram.layout = new go.ForceDirectedLayout();
			const layout = diagram.layout as go.ForceDirectedLayout;
			layout.maxIterations = 300;
			layout.arrangementSpacing = new go.Size(100, 100);
			layout.isInitial = true;
			layout.isOngoing = false;

			// 应用布局
			diagram.layoutDiagram(true);
		}

		// 优化视图性能
		dataFetching.incrementalUpdate.optimizeViewport(diagram);

		// 更新本地状态
		linkManagement.updateLinkDataArray(dataFetching.parsedLinks.value);

		// 布局完成后的处理
		setTimeout(() => {
			// 隐藏所有端口
			nodeManagement.hideAllPorts(diagram);

			// 重新计算PropertyNode和LabelNode的位置
			nodeManagement.updateFollowerNodePositions(diagram);

			// 强制更新所有连线的路径
			linkManagement.updateAllLinkRoutes(diagram);

			console.log('图表数据加载完成');
		}, 100);

	} catch (error) {
		console.error('加载单线图失败:', error);
		ElMessage.error(`加载单线图失败: ${error instanceof Error ? error.message : '未知错误'}`);
	}
}

// ===== 事件处理函数 =====

/**
 * 处理模型变化事件
 */
const handleModelChanged = (e: go.ChangedEvent) => {
	if (e.isTransactionFinished) {
		const diagram = diagramManagement._getDiagram();
		if (diagram) {
			// 更新视图数据
			const links = diagram.model instanceof go.GraphLinksModel ? diagram.model.linkDataArray.slice() : [];
			linkManagement.updateLinkDataArray(links as SingleLineLink[]);
			console.log('模型已更新');
		}
	}
};

/**
 * 初始化图表
 */
const initDiagram = () => {
	if (!diagramRef.value) return;

	const diagram = diagramManagement.initDiagram(diagramRef.value, {
		onExternalObjectsDropped: nodeManagement.handleExternalObjectsDropped,
		onSelectionChanged: (e) => nodeManagement.handleSelectionChanged(e, (event: string, ...args: any[]) => {
			if (event === 'nodeSelected') {
				emit('nodeSelected', args[0]);
			}
		}),
		onSelectionMoved: nodeManagement.handleSelectionMoved,
		onModelChanged: handleModelChanged,
		onLinkDrawn: linkManagement.handleLinkDrawn,
		onLinkRelinked: linkManagement.handleLinkRelinked,
	});

	if (diagram) {
		console.log('图表初始化完成');
	}
};

// ===== 监听器设置 =====

// 监听结果显示切换
watch(
	() => showResult.value,
	(show) => {
		const diagram = diagramManagement._getDiagram();
		if (diagram) {
			propertyDisplay.togglePropertyNodes(diagram, show);
		}
	}
);

// ===== 组件生命周期 =====

/**
 * 组件初始化函数
 */
onMounted(() => {
	// 1. 初始化主图表
	initDiagram();

	// 2. 初始化组件库
	palette.initPalette();

	// 3. 加载图表数据
	loadSingleLineData();

	// 确保初始时所有节点的端口都是隐藏状态
	setTimeout(() => {
		const diagram = diagramManagement._getDiagram();
		if (diagram) {
			nodeManagement.hideAllPorts(diagram);
		}
	}, 200);
});

/**
 * 保存模型到localStorage
 */
function saveModel() {
	const diagram = diagramManagement._getDiagram();
	if (!diagram) return;

	const modelJson = diagram.model.toJson();
	console.log('modelJson', modelJson);
	localStorage.setItem('gojs-demo3-model', modelJson);

	ElMessage.success('模型已保存');
}

/**
 * 获取性能报告
 */
function getPerformanceReport() {
	return dataFetching.incrementalUpdate.getPerformanceReport();
}

/**
 * 清理内存
 */
function cleanupMemory() {
	dataFetching.incrementalUpdate.cleanupMemory();
	ElMessage.success('内存清理完成');
}

/**
 * 从localStorage加载模型
 */
function loadModel() {
	const diagram = diagramManagement._getDiagram();
	if (!diagram) return;

	const savedModel = localStorage.getItem('gojs-demo3-model');
	if (savedModel) {
		try {
			// 保存当前选中状态
			const savedSelection = diagram.selection.toArray();

			// 加载模型
			diagram.model = go.Model.fromJson(savedModel);

			// 恢复选中状态（如果节点仍存在）
			savedSelection.forEach((obj: go.Part) => {
				const node = diagram.findNodeForKey(obj.data.key);
				if (node) node.isSelected = true;
			});

			ElMessage.success('模型已加载');

			// 强制布局更新，确保所有元素正确显示
			diagram.layoutDiagram(true);
		} catch (error) {
			console.error('加载模型时发生错误:', error);
			ElMessage.error('加载模型失败');
		}
	} else {
		ElMessage.warning('没有找到保存的模型');
	}
}

// ===== 组件接口暴露 =====
defineExpose({
	diagram: diagramManagement.diagramInstance,
	// 数据相关
	isLoading: dataFetching.isLoading,
	hasData: dataFetching.hasData,
	loadData: loadSingleLineData,
	clearData: dataFetching.clearCache,
	// 节点相关
	selectedNode: nodeManagement.selectedNode,
	updateNodeText: () => nodeManagement.updateNodeText(diagramManagement._getDiagram()),
	updateNodeColor: () => nodeManagement.updateNodeColor(diagramManagement._getDiagram()),
	// 属性显示相关
	showResult,
	toggleProperties: (show: boolean) => propertyDisplay.setShowResult(diagramManagement._getDiagram(), show),
	// 模型操作
	saveModel,
	loadModel,
	// 性能相关
	getPerformanceReport,
	cleanupMemory,
	isUpdating: dataFetching.incrementalUpdate.isUpdating,
	performanceMetrics: dataFetching.incrementalUpdate.performanceMetrics,
});
</script>

<style scoped>
/* 自定义滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
	width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
	background: #f1f1f1;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
	background: #ccc;
	border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
	background: #999;
}
</style>