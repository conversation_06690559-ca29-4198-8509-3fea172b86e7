<template>
	<div class="single-line-diagram-container h-full flex">
		<!-- 左侧组件库，缩小为140px -->
		<div class="w-24 bg-gray-50 border-r border-gray-200 flex flex-col">
			<div class="px-3 h-12 text-base font-medium text-gray-700 border-b border-gray-200 flex items-center">组件库</div>
			<div ref="paletteRef" class="flex-1 py-2 flex flex-col justify-center overflow-y-auto custom-scrollbar"></div>
		</div>

		<!-- 右侧主要内容区域 -->
		<div class="flex-1 flex flex-col">
			<!-- 工具栏 -->
			<div v-if="props.showToolbar" class="h-12 bg-white border-b border-gray-200 flex items-center justify-between px-4">
				<div class="flex items-center gap-2">
					<h2 class="text-lg font-medium text-gray-900">{{ props.title || '站内接线图' }}</h2>
					<el-tooltip v-if="props.showResultToggle" :content="showResult ? '显示结果' : '隐藏结果'" placement="top">
						<el-button
							class="!m-0"
							:icon="showResult ? View : Hide"
							size="small"
							:type="showResult ? 'primary' : 'default'"
							circle
							@click="showResult = !showResult"
						/>
					</el-tooltip>
				</div>

				<!-- <div class="flex items-center space-x-2">
					<button
						@click="
							() => {
								const report = getPerformanceReport();
								console.log('性能报告:', report);
							}
						"
						class="px-3 py-1.5 text-sm bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
					>
						性能报告
					</button>
					<button @click="saveModel" class="px-3 py-1.5 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
						保存模型
					</button>
					<button @click="loadModel" class="px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
						加载模型
					</button>
				</div> -->
			</div>

			<!-- 图表区域 -->
			<div class="flex-1 flex">
				<!-- 图表画布 -->
				<div class="flex-1 relative">
					<div ref="diagramRef" class="w-full h-full bg-white"></div>
					<!-- 加载状态 -->
					<div v-if="loading" class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
						<div class="text-center">
							<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
							<p class="text-sm text-gray-600">加载中...</p>
						</div>
					</div>
				</div>

				<!-- 右侧属性面板 -->
				<div v-if="false && nodeManagement.selectedNode.value" class="w-64 bg-gray-50 border-l border-gray-200 flex flex-col">
					<div class="p-3 border-b border-gray-200 bg-white">
						<h3 class="text-sm font-medium text-gray-900">节点属性</h3>
					</div>

					<div class="flex-1 p-3 space-y-3 custom-scrollbar overflow-y-auto">
						<div class="flex items-center py-1.5">
							<div class="w-16 text-xs text-gray-600">名称</div>
							<div class="flex-1">
								<input
									type="text"
									v-model="nodeManagement.nodeText.value"
									@change="nodeManagement.updateNodeText(diagramManagement._getDiagram())"
									class="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:border-blue-500"
								/>
							</div>
						</div>

						<div class="flex items-center py-1.5">
							<div class="w-16 text-xs text-gray-600">颜色</div>
							<div class="flex-1">
								<input
									type="color"
									v-model="nodeManagement.nodeColor.value"
									@change="nodeManagement.updateNodeColor(diagramManagement._getDiagram())"
									class="w-full h-6 border border-gray-300 rounded"
								/>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue';
import * as go from 'gojs';
import { ElMessage } from 'element-plus';
import { View, Hide } from '@element-plus/icons-vue';
import icons from './icons';

// 导入优化后的hooks
import { useDiagramManagement } from './hooks/useDiagramManagement';
import { useNodeManagement } from './hooks/useNodeManagement';
import { useLinkManagement } from './hooks/useLinkManagement';
import { usePropertyDisplay } from './hooks/usePropertyDisplay';
import { usePalette } from './hooks/usePalette';
import { useIncrementalUpdate } from './hooks/useIncrementalUpdate';
import { useConnectionMarkers } from './hooks/useConnectionMarkers';

// ===== 组件配置 =====
const componentObjects = Object.values(icons);

const props = withDefaults(
	defineProps<{
		showToolbar?: boolean;
		showOverview?: boolean;
		showPropertyPanel?: boolean;
		autoInitialize?: boolean;
		enableSearch?: boolean;
		enableCategorization?: boolean;
		title?: string;
		showResultToggle?: boolean;
	}>(),
	{
		showToolbar: true,
		showOverview: false,
		showPropertyPanel: true,
		autoInitialize: true,
		enableSearch: true,
		enableCategorization: true,
		title: '',
		showResultToggle: true,
	}
);

// 定义事件抛出
const emit = defineEmits<{
	nodeSelected: [node: any | null];
	linkSelected: [link: any | null];
	modelChanged: [data: { nodes: any[]; links: any[] }];
}>();

// ===== DOM引用 =====
const diagramRef = ref<HTMLElement | null>(null);
const paletteRef = ref<HTMLElement | null>(null);

// ===== 基础状态管理 =====
const loading = ref<boolean>(false);
const isGraphInitialized = ref<boolean>(false);
const originalNodeData = ref<SingleLineNode[]>([]);
const originalLinkData = ref<SingleLineLink[]>([]);

// ===== 初始化Hooks =====
const diagramManagement = useDiagramManagement();
const nodeManagement = useNodeManagement();
const linkManagement = useLinkManagement();
const propertyDisplay = usePropertyDisplay();
const incrementalUpdate = useIncrementalUpdate();
const connectionMarkers = useConnectionMarkers();
const palette = usePalette(paletteRef, componentObjects);

// ===== 计算属性 =====
const showResult = computed({
	get: () => propertyDisplay.showResult.value,
	set: (value: boolean) => propertyDisplay.setShowResult(diagramManagement._getDiagram(), value),
});

/**
 * 场景1：初始化空白图表实例
 * 用途：页面加载时快速显示图表容器，提升用户体验
 */
const initBlankGraph = (): boolean => {
	if (!diagramRef.value) {
		console.warn('图表容器未找到，无法初始化');
		return false;
	}

	// 初始化图表实例
	if (!diagramManagement.isInitialized.value) {
		const diagram = diagramManagement.initDiagram(diagramRef.value, {
			onExternalObjectsDropped: nodeManagement.handleExternalObjectsDropped,
			onSelectionChanged: (e) =>
				nodeManagement.handleSelectionChanged(e, (event: string, ...args: any[]) => {
					if (event === 'nodeSelected') {
						emit('nodeSelected', args[0]);
					}
				}),
			onSelectionMoved: nodeManagement.handleSelectionMoved,
			onModelChanged: handleModelChanged,
			onLinkDrawn: linkManagement.handleLinkDrawn,
			onLinkRelinked: linkManagement.handleLinkRelinked,
		});

		if (!diagram) {
			console.warn('图表实例初始化失败');
			return false;
		}
	}

	const myDiagram = diagramManagement._getDiagram();
	if (!myDiagram || !diagramManagement.isInitialized.value) {
		console.warn('图表实例初始化失败');
		return false;
	}

	try {
		// 创建空的数据模型
		const emptyModel = new go.GraphLinksModel([], []);
		emptyModel.linkFromPortIdProperty = 'fromPort';
		emptyModel.linkToPortIdProperty = 'toPort';
		emptyModel.linkKeyProperty = 'key';
		myDiagram.model = emptyModel;

		// 设置连接点标记更新器
		connectionMarkers.setupMarkerUpdater(myDiagram);

		isGraphInitialized.value = true;
		console.log('空白图表初始化完成');
		return true;
	} catch (error) {
		console.error('初始化空白图表失败:', error);
		return false;
	}
};

/**
 * 场景2：加载数据到已初始化的图表中
 * 用途：向已预初始化的空白图表加载数据，性能优化场景
 */
const loadGraphData = (
	nodeData: SingleLineNode[],
	linkData: SingleLineLink[],
	options: {
		autoCenter?: boolean;
		preserveViewport?: boolean;
		showProperties?: boolean;
	} = {}
): boolean => {
	const { autoCenter = true, preserveViewport = false, showProperties = false } = options;

	// 如果图表未初始化，先初始化空白图表
	if (!isGraphInitialized.value) {
		const success = initBlankGraph();
		if (!success) {
			return false;
		}
	}

	const myDiagram = diagramManagement._getDiagram();
	if (!myDiagram || !diagramManagement.isInitialized.value) {
		console.warn('图表实例未准备好，无法加载数据');
		return false;
	}

	try {
		loading.value = true;

		// 保存原始数据
		originalNodeData.value = [...nodeData];
		originalLinkData.value = [...linkData];

		// 使用增量更新加载数据
		incrementalUpdate.loadGraphData(myDiagram, nodeData, linkData);

		// 设置布局（首次加载时）
		if (!preserveViewport) {
			myDiagram.layout = new go.ForceDirectedLayout();
			const layout = myDiagram.layout as go.ForceDirectedLayout;
			layout.maxIterations = 300;
			layout.arrangementSpacing = new go.Size(100, 100);
			layout.isInitial = true;
			layout.isOngoing = false;

			// 应用布局
			myDiagram.layoutDiagram(true);
		}

		// 优化视图性能
		incrementalUpdate.optimizeViewport(myDiagram);

		// 设置属性显示状态
		if (showProperties !== undefined) {
			propertyDisplay.setShowResult(myDiagram, showProperties);
		}

		// 布局完成后的处理
		setTimeout(() => {
			// 隐藏所有端口
			nodeManagement.hideAllPorts(myDiagram);

			// 重新计算PropertyNode和LabelNode的位置
			nodeManagement.updateFollowerNodePositions(myDiagram);

			// 强制更新所有连线的路径
			linkManagement.updateAllLinkRoutes(myDiagram);

			// 更新连接点标记
			connectionMarkers.updateConnectionMarkers(myDiagram);

			// 更新连接点标记位置（确保丝滑跟随）
			connectionMarkers.updateConnectionMarkerPositions(myDiagram);

			// 视图居中
			if (autoCenter && !preserveViewport) {
				myDiagram.scale = 1;
				myDiagram.centerRect(myDiagram.documentBounds);
			}

			console.log('图表数据加载完成');
		}, 100);

		return true;
	} catch (error) {
		console.error('加载图表数据失败:', error);
		ElMessage.error(`加载图表数据失败: ${error instanceof Error ? error.message : '未知错误'}`);
		return false;
	} finally {
		setTimeout(() => {
			loading.value = false;
		}, 200);
	}
};

/**
 * 场景3：完整更新图表数据（支持增量更新选项）
 * 用途：数据变更、过滤、排序等需要完整重建的场景
 */
const updateGraphData = (
	nodeData: SingleLineNode[],
	linkData: SingleLineLink[],
	options: {
		incremental?: boolean;
		forceLayout?: boolean;
		preserveViewport?: boolean;
		showProperties?: boolean;
	} = {}
): boolean => {
	const myDiagram = diagramManagement._getDiagram();
	if (!myDiagram || !diagramManagement.isInitialized.value) {
		console.warn('图表实例未初始化，无法更新数据');
		return false;
	}

	const { incremental = false, forceLayout = true, preserveViewport = false, showProperties } = options;

	try {
		loading.value = true;

		// 保存原始数据
		originalNodeData.value = [...nodeData];
		originalLinkData.value = [...linkData];

		// 优先使用增量更新（性能优化）
		if (incremental) {
			incrementalUpdate.updateGraphData(myDiagram, nodeData, linkData);
		} else {
			// 完整更新模式
			incrementalUpdate.loadGraphData(myDiagram, nodeData, linkData);
		}

		// 布局处理
		if (forceLayout) {
			myDiagram.layoutDiagram(true);
		}

		// 视图处理
		if (!preserveViewport) {
			myDiagram.scale = 1;
			myDiagram.centerRect(myDiagram.documentBounds);
		}

		// 设置属性显示状态
		if (showProperties !== undefined) {
			propertyDisplay.setShowResult(myDiagram, showProperties);
		}

		// 更新连接点标记
		setTimeout(() => {
			connectionMarkers.updateConnectionMarkers(myDiagram);
			// 确保标记位置正确
			connectionMarkers.updateConnectionMarkerPositions(myDiagram);
		}, 100);

		console.log('图表数据更新完成');
		return true;
	} catch (error) {
		console.error('更新图表数据失败:', error);
		ElMessage.error(`更新图表数据失败: ${error instanceof Error ? error.message : '未知错误'}`);
		return false;
	} finally {
		setTimeout(
			() => {
				loading.value = false;
			},
			preserveViewport ? 100 : 200
		);
	}
};

// ===== 事件处理函数 =====

/**
 * 处理模型变化事件
 */
const handleModelChanged = (e: go.ChangedEvent) => {
	if (e.isTransactionFinished) {
		const diagram = diagramManagement._getDiagram();
		if (diagram) {
			// 更新视图数据
			const nodes = diagram.model.nodeDataArray.slice();
			const links = diagram.model instanceof go.GraphLinksModel ? diagram.model.linkDataArray.slice() : [];
			linkManagement.updateLinkDataArray(links as SingleLineLink[]);

			// 触发模型变化事件
			emit('modelChanged', { nodes, links });
			console.log('模型已更新');
		}
	}
};

// ===== 监听器设置 =====

// 监听结果显示切换
watch(
	() => showResult.value,
	(show) => {
		const diagram = diagramManagement._getDiagram();
		if (diagram) {
			propertyDisplay.togglePropertyNodes(diagram, show);
		}
	}
);

/**
 * 销毁图表
 */
const destroyDiagram = () => {
	diagramManagement.destroyDiagram();
	palette.destroyPalette();
	isGraphInitialized.value = false;
};

// ===== 组件生命周期 =====

// 组件挂载时的自动初始化逻辑
watch(
	() => diagramRef.value,
	(newRef) => {
		if (newRef && props.autoInitialize && !isGraphInitialized.value) {
			console.log('自动初始化空白图表');
			initBlankGraph();
		}
	},
	{ immediate: true }
);

/**
 * 组件初始化函数
 */
onMounted(() => {
	// 初始化组件库
	palette.initPalette();

	// 如果没有自动初始化，手动初始化
	if (!props.autoInitialize) {
		initBlankGraph();
	}
});

/**
 * 保存模型到localStorage
 */
function saveModel() {
	const diagram = diagramManagement._getDiagram();
	if (!diagram) return;

	const modelJson = diagram.model.toJson();
	console.log('modelJson', modelJson);
	localStorage.setItem('single-line-model', modelJson);

	ElMessage.success('模型已保存');
}

/**
 * 获取性能报告
 */
function getPerformanceReport() {
	return incrementalUpdate.getPerformanceReport();
}

/**
 * 清理内存
 */
function cleanupMemory() {
	incrementalUpdate.cleanupMemory();
	ElMessage.success('内存清理完成');
}

/**
 * 从localStorage加载模型
 */
function loadModel() {
	const diagram = diagramManagement._getDiagram();
	if (!diagram) return;

	const savedModel = localStorage.getItem('single-line-model');
	if (savedModel) {
		try {
			// 保存当前选中状态
			const savedSelection = diagram.selection.toArray();

			// 加载模型
			diagram.model = go.Model.fromJson(savedModel);

			// 恢复选中状态（如果节点仍存在）
			savedSelection.forEach((obj: go.Part) => {
				const node = diagram.findNodeForKey(obj.data.key);
				if (node) node.isSelected = true;
			});

			ElMessage.success('模型已加载');

			// 强制布局更新，确保所有元素正确显示
			diagram.layoutDiagram(true);
		} catch (error) {
			console.error('加载模型时发生错误:', error);
			ElMessage.error('加载模型失败');
		}
	} else {
		ElMessage.warning('没有找到保存的模型');
	}
}

/**
 * 清空图表数据
 */
function clearGraphData() {
	const diagram = diagramManagement._getDiagram();
	if (!diagram) return;

	try {
		diagram.startTransaction('clear data');
		diagram.model.nodeDataArray = [];
		if (diagram.model instanceof go.GraphLinksModel) {
			diagram.model.linkDataArray = [];
		}
		diagram.commitTransaction('clear data');

		// 清空原始数据
		originalNodeData.value = [];
		originalLinkData.value = [];

		console.log('图表数据已清空');
	} catch (error) {
		console.error('清空图表数据失败:', error);
	}
}

// ===== 组件接口暴露 =====
defineExpose({
	// 图表实例
	diagram: diagramManagement.diagramInstance,

	// 核心数据操作方法（参考地理接线图模式）
	initBlankGraph, // 场景1：初始化空白图表
	loadGraphData, // 场景2：加载数据到图表
	updateGraphData, // 场景3：更新图表数据

	// 状态相关
	isLoading: computed(() => loading.value),
	isInitialized: computed(() => isGraphInitialized.value),
	hasData: computed(() => originalNodeData.value.length > 0),

	// 数据相关
	getOriginalData: () => ({ nodes: originalNodeData.value, links: originalLinkData.value }),
	clearData: clearGraphData,

	// 节点相关
	selectedNode: nodeManagement.selectedNode,
	updateNodeText: () => nodeManagement.updateNodeText(diagramManagement._getDiagram()),
	updateNodeColor: () => nodeManagement.updateNodeColor(diagramManagement._getDiagram()),

	// 属性显示相关
	showResult,
	toggleProperties: (show: boolean) => propertyDisplay.setShowResult(diagramManagement._getDiagram(), show),

	// 模型操作
	saveModel,
	loadModel,

	// 性能相关
	getPerformanceReport,
	cleanupMemory,
	isUpdating: incrementalUpdate.isUpdating,
	performanceMetrics: incrementalUpdate.performanceMetrics,

	// 图表管理
	destroyDiagram,

	// 连接点标记
	updateConnectionMarkers: () => {
		const diagram = diagramManagement._getDiagram();
		if (diagram) connectionMarkers.updateConnectionMarkers(diagram);
	},
	updateConnectionMarkerPositions: () => {
		const diagram = diagramManagement._getDiagram();
		if (diagram) connectionMarkers.updateConnectionMarkerPositions(diagram);
	},
	toggleConnectionMarkers: (visible: boolean) => {
		const diagram = diagramManagement._getDiagram();
		if (diagram) connectionMarkers.toggleMarkersVisibility(diagram, visible);
	},
	clearConnectionMarkers: () => {
		const diagram = diagramManagement._getDiagram();
		if (diagram) connectionMarkers.clearAllMarkers(diagram);
	},
});
</script>

<style scoped>
/* 自定义滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
	width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
	background: #f1f1f1;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
	background: #ccc;
	border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
	background: #999;
}
</style>
