<template>
	<div class="flex h-full overflow-hidden" v-loading="isLoading" :element-loading-text="isLoading ? '加载单线图中...' : '正在计算布局，请稍候...'">
		<!-- 左侧组件库，缩小为140px -->
		<div class="w-32 bg-gray-50 border-r border-gray-200 flex flex-col">
			<div class="px-3 text-base py-2 font-medium text-gray-700 leading-[24px] border-b border-gray-200">组件库</div>
			<div ref="paletteRef" class="flex-1 py-2 flex flex-col justify-center overflow-y-auto custom-scrollbar"></div>
		</div>

		<!-- 中间图表区域，让画布自适应 -->
		<div class="flex-1 flex flex-col">
			<div class="py-2 px-2 flex items-center bg-gray-50 border-b border-gray-200">
				<el-space :size="10" :spacer="spacer">
					<div class="leading-[24px] font-bold">{{ nodeInfo.name }}</div>
					<el-tooltip :content="showResult ? '隐藏结果' : '显示结果'" placement="top">
						<el-button
							class="!m-0"
							:icon="showResult ? Hide : View"
							size="small"
							:type="showResult ? 'default' : 'primary'"
							circle
							@click="showResult = !showResult"
						>
						</el-button>
					</el-tooltip>
				</el-space>
			</div>
			<div class="flex-1 overflow-hidden bg-white relative">
				<div ref="diagramRef" class="w-full h-full"></div>
				<div id="tooltipDiv" class="absolute px-2 py-1 bg-gray-800 text-white text-xs rounded shadow-lg" style="display: none; z-index: 1000"></div>
			</div>
		</div>

		<!-- 右侧属性面板 -->
		<div v-if="false" class="w-60 bg-gray-50 border-l border-gray-200 flex flex-col">
			<div class="px-3 text-base py-2 font-medium text-gray-700 leading-[24px] border-b border-gray-200">节点信息</div>

			<div v-if="selectedNode" class="px-3 py-2 overflow-y-auto">
				<div class="mb-4">
					<div class="flex items-center py-1.5">
						<div class="w-16 text-xs text-gray-600">ID</div>
						<div class="flex-1 text-xs">{{ selectedNode.key }}</div>
					</div>

					<div class="flex items-center py-1.5">
						<div class="w-16 text-xs text-gray-600">名称</div>
						<div class="flex-1">
							<input
								type="text"
								v-model="nodeText"
								@change="updateNodeText"
								class="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:border-blue-500"
							/>
						</div>
					</div>

					<div class="flex items-center py-1.5">
						<div class="w-16 text-xs text-gray-600">颜色</div>
						<div class="flex-1">
							<input type="color" v-model="nodeColor" @change="updateNodeColor" class="w-full h-6 border border-gray-300 rounded" />
						</div>
					</div>
				</div>
			</div>
			<div v-else class="p-5 text-center text-gray-500 text-sm flex-1">请选择一个节点</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed, inject, ComputedRef, shallowRef } from 'vue';
import * as go from 'gojs';
import { ElMessage } from 'element-plus';
import { View, Hide } from '@element-plus/icons-vue';
import icons from './icons';
import { usePalette } from './hooks/usePalette';
import { BusResizeMultipleTool } from './expand/BusResizeMultipleTool';
import { BusLink } from './expand/BusLink';
import { FollowerDraggingTool } from './expand/FollowerDraggingTool';
import { setupAllNodeTemplates } from './templates';
import { getVoltageColor, spacer } from '/@/config/GraphConfig';
import { query_single_line_graph } from '../../api';

const showResult = ref<boolean>(false);

const dataPacket = inject('dataPacket') as ComputedRef<Recordable>;
const componentObjects = Object.values(icons);

const props = defineProps({
	nodeInfo: {
		type: Object,
		default: () => ({}),
	},
});

// 定义事件抛出
const emit = defineEmits<{
	nodeSelected: [node: any | null];
}>();

// ===== 数据加载 =====

const isLoading = ref<boolean>(false);

/**
 * 从API数据格式化属性文本
 */
const formatPropertyText = (apiNode: any): string => {
	const properties = apiNode.properties || {};
	const nodeType = apiNode.type || '';
	let resultLines: string[] = [];

	if (!properties || Object.keys(properties).length === 0) {
		return '';
	}

	switch (nodeType) {
		case 'BusbarSection':
			if (properties.vn_kv !== undefined && properties.vn_kv !== null) {
				resultLines.push(`U: ${Number(properties.vn_kv).toFixed(2)}`);
			}
			if (properties.dt_vn_kv !== undefined && properties.dt_vn_kv !== null) {
				resultLines.push(`U(M): ${Number(properties.dt_vn_kv).toFixed(2)}`);
			}
			break;

		case 'ACLineDot':
			if (properties.p_mw !== undefined && properties.p_mw !== null) {
				resultLines.push(`P: ${Number(properties.p_mw).toFixed(2)}`);
			}
			if (properties.dt_p_mw !== undefined && properties.dt_p_mw !== null) {
				resultLines.push(`P(M): ${Number(properties.dt_p_mw).toFixed(2)}`);
			}
			if (properties.q_mvar !== undefined && properties.q_mvar !== null) {
				resultLines.push(`Q: ${Number(properties.q_mvar).toFixed(2)}`);
			}
			if (properties.dt_q_mvar !== undefined && properties.dt_q_mvar !== null) {
				resultLines.push(`Q(M): ${Number(properties.dt_q_mvar).toFixed(2)}`);
			}
			break;

		case 'Load':
			if (properties.p_mw !== undefined && properties.p_mw !== null) {
				resultLines.push(`P: ${Number(properties.p_mw).toFixed(2)}`);
			}
			if (properties.dt_p_mw !== undefined && properties.dt_p_mw !== null) {
				resultLines.push(`P(M): ${Number(properties.dt_p_mw).toFixed(2)}`);
			}
			if (properties.q_mvar !== undefined && properties.q_mvar !== null) {
				resultLines.push(`Q: ${Number(properties.q_mvar).toFixed(2)}`);
			}
			if (properties.dt_q_mvar !== undefined && properties.dt_q_mvar !== null) {
				resultLines.push(`Q(M): ${Number(properties.dt_q_mvar).toFixed(2)}`);
			}
			break;

		case 'Synchronousmachine':
			if (properties.vn_kv !== undefined && properties.vn_kv !== null) {
				resultLines.push(`U: ${Number(properties.vn_kv).toFixed(2)}`);
			}
			if (properties.dt_vn_kv !== undefined && properties.dt_vn_kv !== null) {
				resultLines.push(`U(M): ${Number(properties.dt_vn_kv).toFixed(2)}`);
			}
			if (properties.p_mw !== undefined && properties.p_mw !== null) {
				resultLines.push(`P: ${Number(properties.p_mw).toFixed(2)}`);
			}
			if (properties.dt_p_mw !== undefined && properties.dt_p_mw !== null) {
				resultLines.push(`P(M): ${Number(properties.dt_p_mw).toFixed(2)}`);
			}
			if (properties.q_mvar !== undefined && properties.q_mvar !== null) {
				resultLines.push(`Q: ${Number(properties.q_mvar).toFixed(2)}`);
			}
			if (properties.dt_q_mvar !== undefined && properties.dt_q_mvar !== null) {
				resultLines.push(`Q(M): ${Number(properties.dt_q_mvar).toFixed(2)}`);
			}
			break;

		case 'Trafo':
			if (properties.p_hv_mw !== undefined && properties.p_hv_mw !== null) {
				resultLines.push(`P: ${Number(properties.p_hv_mw).toFixed(2)}`);
			}
			if (properties.q_hv_mvar !== undefined && properties.q_hv_mvar !== null) {
				resultLines.push(`Q: ${Number(properties.q_hv_mvar).toFixed(2)}`);
			}
			if (properties.p_lv_mw !== undefined && properties.p_lv_mw !== null) {
				resultLines.push(`P: ${Number(properties.p_lv_mw).toFixed(2)}`);
			}
			if (properties.q_lv_mvar !== undefined && properties.q_lv_mvar !== null) {
				resultLines.push(`Q: ${Number(properties.q_lv_mvar).toFixed(2)}`);
			}
			if (properties.dt_p_hv_mw !== undefined && properties.dt_p_hv_mw !== null) {
				resultLines.push(`P(M): ${Number(properties.dt_p_hv_mw).toFixed(2)}`);
			}
			if (properties.dt_q_hv_mvar !== undefined && properties.dt_q_hv_mvar !== null) {
				resultLines.push(`Q(M): ${Number(properties.dt_q_hv_mvar).toFixed(2)}`);
			}
			if (properties.dt_p_lv_mw !== undefined && properties.dt_p_lv_mw !== null) {
				resultLines.push(`P(M): ${Number(properties.dt_p_lv_mw).toFixed(2)}`);
			}
			if (properties.dt_q_lv_mvar !== undefined && properties.dt_q_lv_mvar !== null) {
				resultLines.push(`Q(M): ${Number(properties.dt_q_lv_mvar).toFixed(2)}`);
			}
			break;

		case 'Trafo3w':
			if (properties.p_hv_mw !== undefined && properties.p_hv_mw !== null) {
				resultLines.push(`PH: ${Number(properties.p_hv_mw).toFixed(2)}`);
			}
			if (properties.q_hv_mvar !== undefined && properties.q_hv_mvar !== null) {
				resultLines.push(`QH: ${Number(properties.q_hv_mvar).toFixed(2)}`);
			}
			if (properties.p_mv_mw !== undefined && properties.p_mv_mw !== null) {
				resultLines.push(`PM: ${Number(properties.p_mv_mw).toFixed(2)}`);
			}
			if (properties.q_mv_mvar !== undefined && properties.q_mv_mvar !== null) {
				resultLines.push(`QM: ${Number(properties.q_mv_mvar).toFixed(2)}`);
			}
			if (properties.p_lv_mw !== undefined && properties.p_lv_mw !== null) {
				resultLines.push(`PL: ${Number(properties.p_lv_mw).toFixed(2)}`);
			}
			if (properties.q_lv_mvar !== undefined && properties.q_lv_mvar !== null) {
				resultLines.push(`QL: ${Number(properties.q_lv_mvar).toFixed(2)}`);
			}
			if (properties.dt_p_hv_mw !== undefined && properties.dt_p_hv_mw !== null) {
				resultLines.push(`PH(M): ${Number(properties.dt_p_hv_mw).toFixed(2)}`);
			}
			if (properties.dt_q_hv_mvar !== undefined && properties.dt_q_hv_mvar !== null) {
				resultLines.push(`QH(M): ${Number(properties.dt_q_hv_mvar).toFixed(2)}`);
			}
			if (properties.dt_p_mv_mw !== undefined && properties.dt_p_mv_mw !== null) {
				resultLines.push(`PM(M): ${Number(properties.dt_p_mv_mw).toFixed(2)}`);
			}
			if (properties.dt_q_mv_mvar !== undefined && properties.dt_q_mv_mvar !== null) {
				resultLines.push(`QM(M): ${Number(properties.dt_q_mv_mvar).toFixed(2)}`);
			}
			if (properties.dt_p_lv_mw !== undefined && properties.dt_p_lv_mw !== null) {
				resultLines.push(`PL(M): ${Number(properties.dt_p_lv_mw).toFixed(2)}`);
			}
			if (properties.dt_q_lv_mvar !== undefined && properties.dt_q_lv_mvar !== null) {
				resultLines.push(`QL(M): ${Number(properties.dt_q_lv_mvar).toFixed(2)}`);
			}
			break;

		case 'ShuntCompensator':
			if (properties.q_mvar !== undefined && properties.q_mvar !== null) {
				resultLines.push(`Q: ${Number(properties.q_mvar).toFixed(2)}`);
			}
			if (properties.p_mw !== undefined && properties.p_mw !== null) {
				resultLines.push(`Q(M): ${Number(properties.p_mw).toFixed(2)}`);
			}
			break;

		default:
			// resultLines.push(`暂无结果信息`);
			break;
	}

	return resultLines.join('\n');
};

/**
 * 加载单线图数据
 */
async function get_query_single_line_graph() {
	isLoading.value = true;
	try {
		const { data } = await query_single_line_graph({
			bb_case_id: dataPacket.value.id,
			substation_id: props.nodeInfo.key,
		});

		// 确保数据存在且有效
		if (!data) {
			throw new Error('服务器返回的数据为空');
		}

		if (!myDiagram) {
			throw new Error('图表实例未初始化');
		}

		// 确保没有正在进行的事务
		if (myDiagram.isModified) {
			try {
				myDiagram.commitTransaction('清理旧事务');
			} catch (e) {
				console.warn('尝试提交未知事务:', e);
				myDiagram.rollbackTransaction();
			}
		}

		// 解析节点和连线数据
		const parsedNodes: SingleLineNode[] = [];
		const parsedLinks: SingleLineLink[] = [];

		// 处理节点数据，同时创建标签节点和属性节点数据
		const labelNodesData: any[] = [];
		const propertyNodesData: any[] = [];
		const annotationLinksData: any[] = [];

		if (data.nodes && Array.isArray(data.nodes)) {
			data.nodes.forEach((node: any) => {
				// 创建符合GoJS格式的主节点数据
				const nodeData: SingleLineNode = {
					key: node.id,
					category: node.type,
					type: node.type,
					name: node.name || '未命名节点',
					color: getVoltageColor(node.voltage),
					pos: [0, 0],
					angle: node.angle || 0, // 从API数据中读取角度值，如果没有则默认为0
					voltage: node.voltage,
					voltage2: node.voltage2,
					voltage3: node.voltage3,
					color2: getVoltageColor(node.voltage2),
					color3: getVoltageColor(node.voltage3),
					width: node.type === 'BusbarSection' ? 150 : undefined,
					properties: node.properties || {},
				};
				parsedNodes.push(nodeData);

				// === 创建独立的标签节点 ===
				const labelKey = node.id + '-label';
				// 修复：初始位置设置为合理的默认值，等待后续布局定位
				const labelOffsetX = 0;
				const labelOffsetY = 30; // 标签在主节点下方
				const labelLoc = new go.Point(50, 50); // 给一个临时位置，布局后会重新计算

				labelNodesData.push({
					key: labelKey,
					text: node.name || '未命名节点',
					loc: go.Point.stringify(labelLoc),
					category: 'LabelNode',
					visible: true,
					parentNodeId: node.id,
					color: getVoltageColor(node.voltage),
					// 保存初始偏移量，用于智慧跟随
					offsetX: labelOffsetX,
					offsetY: labelOffsetY,
				});

				// === 关键修改：LabelNode不创建连线，实现纯跟随效果 ===
				// LabelNode通过FollowerDraggingTool与主节点强绑定，无需连线

				// === 创建独立的属性节点（如果有属性数据）===
				const propertyText = formatPropertyText(node);
				if (propertyText?.trim()) {
					const propertyKey = node.id + '-property';
					const propertyOffsetX = 120; // 属性框在主节点右侧
					const propertyOffsetY = 0;
					const propertyLoc = new go.Point(50 + propertyOffsetX, 50 + propertyOffsetY);

					propertyNodesData.push({
						key: propertyKey,
						properties: propertyText,
						loc: go.Point.stringify(propertyLoc),
						category: 'PropertyNode',
						visible: showResult.value,
						parentNodeId: node.id,
						color: getVoltageColor(node.voltage),
						// 保存初始偏移量，用于智慧跟随
						offsetX: propertyOffsetX,
						offsetY: propertyOffsetY,
					});

					// 创建连接主节点和属性节点的连线
					annotationLinksData.push({
						key: `link-${propertyKey}`,
						from: node.id,
						to: propertyKey,
						category: 'AnnotationLink',
						visible: showResult.value,
						color: getVoltageColor(node.voltage),
					});
				}
			});
		}

		// 处理连线数据
		if (data.edges && Array.isArray(data.edges)) {
			data.edges.forEach((link: any, index: number) => {
				// 跳过没有源或目标的连线
				if (!link.source || !link.target) {
					console.warn(`第${index}个连线缺少源或目标节点，已跳过`);
					return;
				}

				// 创建符合GoJS格式的连线数据
				const linkData: SingleLineLink = {
					key: `link-${index}`, // 为连线生成唯一ID
					from: link.source, // 起点节点ID
					to: link.target, // 终点节点ID
					fromPort: link.source_port, // 起点端口ID
					toPort: link.target_port, // 终点端口ID
					properties: link.properties || {}, // 保存原始属性
					voltage: link.voltage,
					color: getVoltageColor(link.voltage),
				};

				parsedLinks.push(linkData);
			});
		}

		const diagram = myDiagram!;

		try {
			// 先清除选中状态和正在进行的事务
			diagram.clearSelection();

			// 创建新的模型
			const newModel = new go.GraphLinksModel();

			// 设置模型属性
			newModel.linkFromPortIdProperty = 'fromPort';
			newModel.linkToPortIdProperty = 'toPort';
			newModel.linkKeyProperty = 'key';

			// 将PropertyNode数据合并到节点和连线数组中
			const allNodes = [...parsedNodes, ...labelNodesData, ...propertyNodesData];
			const allLinks = [...parsedLinks, ...annotationLinksData];

			// 调试信息：检查LabelNode是否正确创建
			console.log('创建的节点数据统计:', {
				主节点: parsedNodes.length,
				LabelNode: labelNodesData.length,
				PropertyNode: propertyNodesData.length,
				总节点数: allNodes.length,
				LabelNode样例: labelNodesData.slice(0, 2),
			});

			// 设置模型数据
			newModel.nodeDataArray = allNodes;
			newModel.linkDataArray = allLinks;

			// 替换图表模型
			diagram.model = newModel;

			// 设置布局
			diagram.layout = new go.ForceDirectedLayout();
			const layout = diagram.layout as go.ForceDirectedLayout;

			// 配置布局参数
			layout.maxIterations = 300; // 最大迭代次数
			layout.arrangementSpacing = new go.Size(100, 100); // 节点间距
			layout.isInitial = true; // 标记为初始布局
			layout.isOngoing = false; // 禁用持续布局

			// 应用布局
			diagram.layoutDiagram(true);

			// 更新本地状态变量
			nodeDataArray.value = parsedNodes;
			linkDataArray.value = parsedLinks;

			// *** 关键优化：确保连线在布局完成后正确渲染 ***
			setTimeout(() => {
				// 确保初始时所有节点的端口都是隐藏状态
				hideAllPorts(diagram);

				// 重新计算PropertyNode和LabelNode的位置
				updatePropertyNodePositions(diagram);

				// 强制更新所有连线的路径
				diagram.links.each((link: go.Link) => {
					link.updateRoute();
				});

				// 立即应用当前的属性显示状态
				togglePropertyNodes(showResult.value);

				// 调试信息：检查LabelNode创建情况
				console.log('布局完成，连线路径已更新，属性节点位置已计算');
				let labelNodeCount = 0;
				let labelNodeSample: any = null;
				const labelNodeDetails: any[] = [];
				diagram.nodes.each((node: go.Node) => {
					if (node.data?.category === 'LabelNode') {
						labelNodeCount++;
						if (!labelNodeSample) labelNodeSample = node.data;
						labelNodeDetails.push({
							key: node.data.key,
							text: node.data.text,
							visible: node.visible,
							location: `(${node.location.x}, ${node.location.y})`,
							parentNodeId: node.data.parentNodeId,
						});
					}
				});
				console.log('LabelNode总数:', labelNodeCount);
				console.log('LabelNode样例:', labelNodeSample);
				console.log('LabelNode详细信息:', labelNodeDetails);
			}, 100); // 稍微延长时间，确保布局完全完成
		} catch (modelError) {
			console.error('设置模型时发生错误:', modelError);
			throw new Error(`设置图表模型失败: ${modelError instanceof Error ? modelError.message : '未知错误'}`);
		}
	} catch (error) {
		console.error('加载单线图失败:', error);
		ElMessage.error(`加载单线图失败: ${error instanceof Error ? error.message : '未知错误'}`);
	} finally {
		isLoading.value = false;
	}
}

// ===== 组件状态管理 =====

const nodeDataArray = ref<SingleLineNode[]>([]);
const linkDataArray = ref<SingleLineLink[]>([]);
const diagramRef = ref<HTMLElement | null>(null);
const paletteRef = ref<HTMLElement | null>(null);
const selectedNode = ref<any>(null);
const nodeText = ref<string>('');
const nodeColor = ref<string>('#1c57ea');

let myDiagram: go.Diagram | null = null;
// 创建响应式的 diagram 引用，供外部组件使用
const diagramInstance = shallowRef<go.Diagram | null>(null);
let lastPortOver: go.GraphObject | null = null;

const { initPalette } = usePalette(paletteRef, componentObjects);

// ===== 端口管理 =====

/**
 * 控制节点端口的可见性
 */
const showPorts = (node: go.Node, show: boolean) => {
	if (!node || node.isLinkLabel || !myDiagram) return;

	// 修复：排除LabelNode和PropertyNode，它们没有端口
	if (node.data?.category === 'LabelNode' || node.data?.category === 'PropertyNode') {
		return;
	}

	// 确保在事务中进行属性修改
	const needTransaction = !myDiagram.undoManager.isInTransaction;
	if (needTransaction) {
		myDiagram.startTransaction('update ports');
	}

	try {
		node.ports.each((port: go.GraphObject) => {
			if (port.portId !== '') {
				const portShape = port as go.Shape;
				if (show) {
					portShape.fill = 'black';
					portShape.desiredSize = new go.Size(3, 3);
				} else {
					portShape.fill = 'transparent';
					portShape.desiredSize = new go.Size(1, 1);
				}
			}
		});
	} finally {
		if (needTransaction) {
			myDiagram.commitTransaction('update ports');
		}
	}
};

/**
 * 隐藏图表中所有节点的端口
 */
const hideAllPorts = (diagram: go.Diagram) => {
	if (!diagram) return;

	diagram.startTransaction('hide all ports');
	try {
		diagram.nodes.each((node: go.Node) => {
			// 修复：排除LabelNode和PropertyNode，避免误操作
			if (!node.isLinkLabel && node.data?.category !== 'PropertyNode' && node.data?.category !== 'LabelNode') {
				node.ports.each((port: go.GraphObject) => {
					if (port.portId !== '') {
						const portShape = port as go.Shape;
						portShape.fill = 'transparent';
						portShape.desiredSize = new go.Size(1, 1);
					}
				});
			}
		});
	} finally {
		diagram.commitTransaction('hide all ports');
	}
};

// ===== 属性节点系统 =====
// 新的独立属性节点模式，替代原有的Comment气球显示系统

/**
 * 设置PropertyNode模板 - LabelNode模板已通过TemplateManager统一注册
 */
const setupPropertyNodeTemplate = (diagram: go.Diagram) => {
	const $ = go.GraphObject.make;

	// PropertyNode模板 - 独立的属性显示节点
	diagram.nodeTemplateMap.add(
		'PropertyNode',
		$(
			go.Node,
			'Auto',
			{
				locationSpot: go.Spot.Center,
				// 关键：告诉布局引擎不要移动这个节点
				isLayoutPositioned: false,
				movable: true,
				copyable: false,
				deletable: false,
				selectable: true,
				pickable: true,
				fromLinkable: false,
				toLinkable: false,
			},
			new go.Binding('location', 'loc', go.Point.parse).makeTwoWay(go.Point.stringify),
			$(
				go.Shape,
				'RoundedRectangle',
				{
					fill: 'transparent', // 1. 将背景填充设置为透明
					stroke: 'gray',
					strokeWidth: 1,
					strokeDashArray: [5, 3], // 2. 添加虚线样式
				},
				new go.Binding('stroke', 'color')
			),
			$(
				go.TextBlock,
				{
					margin: 4,
					textAlign: 'left',
					font: '12px sans-serif',
					isMultiline: true,
					wrap: go.TextBlock.WrapFit,
					maxSize: new go.Size(150, NaN),
				},
				new go.Binding('text', 'properties'),
				new go.Binding('stroke', 'color')
			)
		)
	);

	// AnnotationLink模板 - 连接主节点和附属节点的连线
	diagram.linkTemplateMap.add(
		'AnnotationLink',
		$(
			go.Link,
			{
				routing: go.Link.Normal,
				selectable: false,
				pickable: false,
				layerName: 'Background',
				// === 关键修复：明确指定连接点为节点中心，避免受label影响 ===
				fromSpot: go.Spot.Center, // 起点连接到主节点的中心
				toSpot: go.Spot.Center, // 终点连接到PropertyNode的中心
			},
			$(
				go.Shape,
				{
					stroke: 'gray',
					strokeWidth: 1,
					strokeDashArray: [2, 3], // 使用虚线样式
				},
				new go.Binding('stroke', 'color')
			)
		)
	);
};

/**
 * 更新LabelNode和PropertyNode的位置，基于其父节点的位置
 */
const updatePropertyNodePositions = (diagram: go.Diagram) => {
	if (!diagram) return;

	diagram.startTransaction('update label and property positions');
	try {
		diagram.nodes.each((node: go.Node) => {
			// 处理PropertyNode位置更新
			if (node.data?.category === 'PropertyNode' && node.data?.parentNodeId) {
				const parentNode = diagram.findNodeForKey(node.data.parentNodeId);
				if (parentNode) {
					const parentBounds = parentNode.actualBounds;
					// 计算PropertyNode的位置：在父节点右侧60像素处
					const newLoc = new go.Point(parentBounds.right + 60, parentBounds.centerY);
					node.location = newLoc;
				}
			}

			// 处理LabelNode位置更新
			if (node.data?.category === 'LabelNode' && node.data?.parentNodeId) {
				const parentNode = diagram.findNodeForKey(node.data.parentNodeId);
				if (parentNode) {
					const parentBounds = parentNode.actualBounds;
					// 使用保存的偏移量或默认偏移量
					const offsetX = node.data.offsetX || 0;
					const offsetY = node.data.offsetY || 10; // 默认在主节点下方55像素
					const newLoc = new go.Point(parentBounds.centerX + offsetX, parentBounds.bottom + Math.abs(offsetY));
					node.location = newLoc;

					console.log(`更新LabelNode位置: ${node.data.key}, 父节点: ${node.data.parentNodeId}, 新位置: (${newLoc.x}, ${newLoc.y})`);
				}
			}
		});
	} finally {
		diagram.commitTransaction('update label and property positions');
	}
};

/**
 * 切换属性节点的显示状态
 */
const togglePropertyNodes = (show: boolean) => {
	if (!myDiagram) return;
	myDiagram.startTransaction(show ? 'show properties' : 'hide properties');

	// 切换PropertyNode和AnnotationLink的可见性
	myDiagram.nodes.each((node: go.Node) => {
		if (node.data?.category === 'PropertyNode') {
			node.visible = show;
		}
	});

	myDiagram.links.each((link: go.Link) => {
		if (link.data?.category === 'AnnotationLink') {
			link.visible = show;
		}
	});

	myDiagram.commitTransaction(show ? 'show properties' : 'hide properties');
};

// 监听结果显示切换
watch(
	() => showResult.value,
	(show) => {
		if (!myDiagram) return;
		togglePropertyNodes(show);
	}
);

/**
 * 初始化图表
 * 创建GoJS图表实例，设置各种配置和事件处理
 */
const initDiagram = () => {
	if (!diagramRef.value) return;

	const $ = go.GraphObject.make;

	// 创建图表
	myDiagram = $(go.Diagram, diagramRef.value as HTMLDivElement, {
		// 启用网格布局和对齐
		grid: $(go.Panel, 'Grid', { gridCellSize: new go.Size(10, 10) }),
		'toolManager.hoverDelay': 100,
		'undoManager.isEnabled': true, // 启用撤销/重做功能
		allowDrop: true, // 允许从组件库拖拽
		ExternalObjectsDropped: handleExternalObjectsDropped, // 处理拖拽放置的回调
		// 确保拖动工具启用和网格对齐
		'draggingTool.isEnabled': true,
		'draggingTool.isGridSnapEnabled': true,
		// 启用调整大小工具
		'resizingTool.isEnabled': true,
		'rotatingTool.isEnabled': true, // 启用旋转功能，支持角度调整
		// 明确设置allowMove为true
		allowMove: true,
		// 禁止自动布局（关键设置）
		'layout.isOngoing': false,
		'layout.isInitial': false,
		'layout.isValidLayout': false,
		padding: new go.Margin(50, 50, 50, 50),
		// 在拖动过程中显示位置，对齐到网格
		positionComputation: (diagram: go.Diagram, pt: go.Point) => {
			// 获取网格大小
			const grid = diagram.grid;
			const gridsize = grid ? grid.gridCellSize : new go.Size(10, 10);
			// 对齐到网格
			return new go.Point(Math.floor(pt.x / gridsize.width) * gridsize.width, Math.floor(pt.y / gridsize.height) * gridsize.height);
		},
		// 强制要求连线必须连接到端口，不允许连接空白区域
		'linkingTool.isUnconnectedLinkValid': false,
		'relinkingTool.isUnconnectedLinkValid': false,
	});

	if (!myDiagram) return;

	// === 3. 安装FollowerDraggingTool工具 ===
	// 使用自定义的FollowerDraggingTool替代默认的拖拽工具
	myDiagram.toolManager.draggingTool = new FollowerDraggingTool();

	// === 修复：确保拖动工具优先于连接工具 ===
	// 设置工具优先级，确保母线等节点优先响应拖动而不是连接
	myDiagram.toolManager.draggingTool.isEnabled = true;
	myDiagram.toolManager.linkingTool.isEnabled = true;
	// 通过设置更高的优先级确保拖动优先
	myDiagram.toolManager.mouseDownTools.insertAt(0, myDiagram.toolManager.draggingTool);

	// 注册自定义ResizingTool，仅对母线节点有效
	console.log('初始化图表 - 注册自定义ResizingTool');
	// 使用新的BusResizeMultipleTool替代原有的BusResizingTool
	myDiagram.toolManager.resizingTool = new BusResizeMultipleTool();

	// 确保调整大小工具生效
	console.log('初始化调整大小工具配置');
	// 开启调整大小功能
	myDiagram.toolManager.resizingTool.isEnabled = true;

	// 自定义复制粘贴，保持层级结构
	myDiagram.toolManager.linkingTool.temporaryLink.routing = go.Link.AvoidsNodes;
	myDiagram.toolManager.relinkingTool.temporaryLink.routing = go.Link.AvoidsNodes;

	// 设置临时连线的其他属性，与正式连线保持一致
	myDiagram.toolManager.linkingTool.temporaryLink.corner = 3;
	myDiagram.toolManager.relinkingTool.temporaryLink.corner = 3;
	myDiagram.toolManager.linkingTool.temporaryLink.curve = go.Link.JumpOver;
	myDiagram.toolManager.relinkingTool.temporaryLink.curve = go.Link.JumpOver;
	// 设置临时连线禁止自动调整
	myDiagram.toolManager.linkingTool.temporaryLink.adjusting = go.Link.None;
	myDiagram.toolManager.relinkingTool.temporaryLink.adjusting = go.Link.None;

	// 设置连接工具的portTargeted事件处理
	myDiagram.toolManager.linkingTool.portTargeted = (fromNode, fromPort, toNode, toPort, toEnd) => {
		if (!myDiagram) return;
		myDiagram.startTransaction('port highlight');
		try {
			// 如果有上一个悬停端口，将其恢复为默认可见状态
			if (lastPortOver && lastPortOver !== toPort) {
				(lastPortOver as go.Shape).fill = 'rgba(0, 0, 0, 0.3)';
			}
			// 更新当前悬停端口，并将其变为高亮状态
			lastPortOver = toPort;
			if (toPort) {
				(toPort as go.Shape).fill = 'rgba(0, 123, 255, 0.9)';
			}
		} finally {
			myDiagram.commitTransaction('port highlight');
		}
	};

	// 同样处理重新连接工具
	myDiagram.toolManager.relinkingTool.portTargeted = (fromNode, fromPort, toNode, toPort, toEnd) => {
		if (!myDiagram) return;
		myDiagram.startTransaction('port highlight');
		try {
			// 如果有上一个悬停端口，将其恢复为默认可见状态
			if (lastPortOver && lastPortOver !== toPort) {
				(lastPortOver as go.Shape).fill = 'rgba(0, 0, 0, 0.3)';
			}
			// 更新当前悬停端口，并将其变为高亮状态
			lastPortOver = toPort;
			if (toPort) {
				(toPort as go.Shape).fill = 'rgba(0, 123, 255, 0.9)';
			}
		} finally {
			myDiagram.commitTransaction('port highlight');
		}
	};

	// 修改工具的doStop方法以清除lastPortOver
	const origLinkingToolDoStop = myDiagram.toolManager.linkingTool.doStop;
	myDiagram.toolManager.linkingTool.doStop = function () {
		// 调用原始函数
		origLinkingToolDoStop.call(this);
		// 清除最后悬停的端口，恢复为默认可见状态
		if (lastPortOver && myDiagram) {
			myDiagram.startTransaction('reset port');
			try {
				(lastPortOver as go.Shape).fill = 'rgba(0, 0, 0, 0.3)';
				lastPortOver = null;
			} finally {
				myDiagram.commitTransaction('reset port');
			}
		}
	};

	const origRelinkingToolDoStop = myDiagram.toolManager.relinkingTool.doStop;
	myDiagram.toolManager.relinkingTool.doStop = function () {
		// 调用原始函数
		origRelinkingToolDoStop.call(this);
		// 清除最后悬停的端口，恢复为默认可见状态
		if (lastPortOver && myDiagram) {
			myDiagram.startTransaction('reset port');
			try {
				(lastPortOver as go.Shape).fill = 'rgba(0, 0, 0, 0.3)';
				lastPortOver = null;
			} finally {
				myDiagram.commitTransaction('reset port');
			}
		}
	};

	// 设置节点模板 - 使用新的模板系统
	setupAllNodeTemplates(myDiagram);

	// 设置PropertyNode模板
	setupPropertyNodeTemplate(myDiagram);

	// 设置连线模板
	setupLinkTemplate(myDiagram);

	// 对齐网格
	myDiagram.toolManager.draggingTool.isGridSnapEnabled = true;

	// 禁用持续布局调整
	myDiagram.layout.isOngoing = false;

	// 创建模型 - 使用GraphLinksModel来支持连线
	myDiagram.model = new go.GraphLinksModel({
		nodeDataArray: nodeDataArray.value,
		linkDataArray: linkDataArray.value,
		linkKeyProperty: 'key', // 确保连线有唯一标识
	});

	// 确保模型正确设置了必要的属性
	if (myDiagram.model instanceof go.GraphLinksModel) {
		// 设置数据属性
		myDiagram.model.linkFromPortIdProperty = 'fromPort';
		myDiagram.model.linkToPortIdProperty = 'toPort';
		// 确保所有节点都有key
		myDiagram.model.nodeDataArray.forEach((nodeData: any) => {
			if (!nodeData.key) {
				// 修复：使用正确的方式生成唯一ID
				const uniqueId = `node-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
				myDiagram?.model.setDataProperty(nodeData, 'key', uniqueId);
			}
		});
	}

	// 选择事件处理 - 支持LabelNode、PropertyNode和普通节点的不同处理方式
	myDiagram.addDiagramListener('ChangedSelection', (e) => {
		const selected = e.diagram.selection.first();

		// 首先隐藏所有端口
		hideAllPorts(e.diagram);

		if (selected instanceof go.Node) {
			// LabelNode节点特殊处理：允许选择和编辑
			if (selected.data && selected.data.category === 'LabelNode') {
				selectedNode.value = selected.data;
				nodeText.value = selected.data.text || 'LabelNode节点';
				nodeColor.value = selected.data.color || '#000000';

				console.log('选中LabelNode节点:', selected.data);
				emit('nodeSelected', selected.data);
				return;
			}

			// PropertyNode节点特殊处理：允许选择但不显示端口
			if (selected.data && selected.data.category === 'PropertyNode') {
				selectedNode.value = selected.data;
				nodeText.value = selected.data.properties || 'PropertyNode节点';
				nodeColor.value = selected.data.color || '#FFFF99';

				console.log('选中PropertyNode节点:', selected.data);
				emit('nodeSelected', selected.data);
				return;
			}

			// 普通节点被选中，显示其端口
			showPorts(selected, true);

			selectedNode.value = selected.data;
			nodeText.value = selected.data.name || '';
			nodeColor.value = selected.data.color || '#1c57ea';

			console.log('选中节点:', {
				key: selected.data.key,
				name: selected.data.name,
				type: selected.data.type,
				angle: selected.data.angle,
				pos: selected.data.pos,
				properties: selected.data.properties,
			});
			emit('nodeSelected', selected.data);
		} else {
			// 没有节点被选中
			selectedNode.value = null;
			console.log('取消选中节点');
			emit('nodeSelected', null);
		}
	});

	// === 实现智慧跟随逻辑 ===
	// 参考案例的SelectionMoved事件实现
	myDiagram.addDiagramListener('SelectionMoved', (e) => {
		if (!myDiagram) return;

		myDiagram.startTransaction('update follower offsets');

		e.diagram.selection.each((part) => {
			if (!(part instanceof go.Node)) return;
			const node = part;

			// 只处理当标签或属性框被独立移动时的情况
			if (node.data.category === 'LabelNode' || node.data.category === 'PropertyNode') {
				// 推断出它所依附的核心图形的key
				const mainNodeKey = node.data.key.replace('-label', '').replace('-property', '');
				const mainNode = myDiagram?.findNodeForKey(mainNodeKey);

				if (mainNode && myDiagram) {
					// 计算并保存新的相对偏移量
					const newOffsetX = node.location.x - mainNode.location.x;
					const newOffsetY = node.location.y - mainNode.location.y;

					// 保存偏移量到节点数据中
					myDiagram.model.setDataProperty(node.data, 'offsetX', newOffsetX);
					myDiagram.model.setDataProperty(node.data, 'offsetY', newOffsetY);

					console.log(`更新 ${node.data.category} 偏移量: (${newOffsetX}, ${newOffsetY})`);
				}
			}

			// 更新连线路径
			node.findLinksConnected().each((link) => {
				if (link instanceof go.Link) {
					link.updateRoute();
				}
			});
		});

		if (myDiagram) {
			myDiagram.commitTransaction('update follower offsets');
		}
	});

	// 监听模型变化
	myDiagram.addModelChangedListener((e) => {
		if (e.isTransactionFinished && myDiagram) {
			// 更新视图数据
			const nodes = myDiagram.model.nodeDataArray.slice();
			nodeDataArray.value = nodes as SingleLineNode[];

			if (myDiagram.model instanceof go.GraphLinksModel) {
				const links = myDiagram.model.linkDataArray.slice();
				linkDataArray.value = links as SingleLineLink[];
			}
			console.log('模型已更新');
		}
	});

	// 设置连接验证 - 强制连线必须连接到端口，保护PropertyNode节点
	const validateConnection = function (fromNode: go.Node, fromPort: go.GraphObject, toNode: go.Node, toPort: go.GraphObject): boolean {
		// 禁止任何涉及PropertyNode节点的手动连线操作
		if ((fromNode.data && fromNode.data.category === 'PropertyNode') || (toNode.data && toNode.data.category === 'PropertyNode')) {
			console.log('连接被拒绝：禁止手动连接PropertyNode节点');
			return false;
		}

		// 普通连线必须有端口才能连接
		if (!fromPort || !toPort) {
			console.log('连接被拒绝：缺少端口信息', { fromPort: !!fromPort, toPort: !!toPort });
			return false;
		}

		// 验证端口ID - 修复：允许空字符串portId（母线的标准做法）
		const fromPortObj = fromPort as go.Shape;
		const toPortObj = toPort as go.Shape;

		// 修复：portId可以是空字符串（如母线），但不能是null或undefined
		if (fromPortObj.portId === null || fromPortObj.portId === undefined || toPortObj.portId === null || toPortObj.portId === undefined) {
			console.log('连接被拒绝：端口ID无效', { fromPortId: fromPortObj.portId, toPortId: toPortObj.portId });
			return false;
		}

		// 检查端口的连接能力 - 参考GoJS示例的toLinkable和fromLinkable属性
		if (fromPortObj.fromLinkable === false) {
			console.log('连接被拒绝：起点端口不允许作为连线起点', { fromPortId: fromPortObj.portId });
			return false;
		}

		if (toPortObj.toLinkable === false) {
			console.log('连接被拒绝：终点端口不允许作为连线终点', { toPortId: toPortObj.portId });
			return false;
		}

		// 检查fromPort的最大出连接数量限制
		const fromMaxLinks = fromPortObj.fromMaxLinks || Infinity;
		if (fromMaxLinks !== Infinity) {
			// 计算当前从该端口出发的连接数量
			const fromLinks = fromNode.findLinksOutOf(fromPortObj.portId).count;
			if (fromLinks >= fromMaxLinks) {
				console.log('连接被拒绝：起点端口连接数已达上限', { fromLinks, fromMaxLinks });
				return false;
			}
		}

		// 检查toPort的最大入连接数量限制 - 参考GoJS示例的toMaxLinks属性
		const toMaxLinks = toPortObj.toMaxLinks || Infinity;
		if (toMaxLinks !== Infinity) {
			// 计算当前到该端口的连接数量
			const toLinks = toNode.findLinksInto(toPortObj.portId).count;
			if (toLinks >= toMaxLinks) {
				console.log('连接被拒绝：终点端口连接数已达上限', { toLinks, toMaxLinks });
				return false;
			}
		}

		// 防止自连接（节点连接到自己）
		if (fromNode === toNode) {
			console.log('连接被拒绝：不允许节点连接到自己');
			return false;
		}

		console.log('连接验证通过', {
			fromNodeId: fromNode.data.key,
			fromPortId: fromPortObj.portId,
			toNodeId: toNode.data.key,
			toPortId: toPortObj.portId,
		});
		return true;
	};

	// 强制将验证函数赋值给工具 - 参考GoJS示例确保严格验证
	myDiagram.toolManager.linkingTool.linkValidation = validateConnection;
	myDiagram.toolManager.relinkingTool.linkValidation = validateConnection;

	// 参考GoJS示例：额外确保连线工具的正确配置
	myDiagram.toolManager.linkingTool.isUnconnectedLinkValid = false;
	myDiagram.toolManager.relinkingTool.isUnconnectedLinkValid = false;

	// 确保只能从端口开始连线
	myDiagram.toolManager.linkingTool.portGravity = 20; // 增加端口吸引力
	myDiagram.toolManager.relinkingTool.portGravity = 20;

	// 添加连线创建后的事件监听器（替代无效的linkDrawn属性）
	myDiagram.addDiagramListener('LinkDrawn', (e: go.DiagramEvent) => {
		const link = e.subject as go.Link;
		if (link) {
			console.log(`连线已绘制: ${link.data.key} (从 ${link.data.from} 到 ${link.data.to})`);
			// 确保连线立即可见和路径正确
			link.updateRoute();
		}
	});

	// 添加连线重连后的事件监听器
	myDiagram.addDiagramListener('LinkRelinked', (e: go.DiagramEvent) => {
		const link = e.subject as go.Link;
		if (link) {
			console.log(`连线已重连: ${link.data.key}`);
			// 确保重连后的连线路径正确
			link.updateRoute();
		}
	});

	// 设置响应式引用和就绪状态
	diagramInstance.value = myDiagram;
};

/**
 * 更新节点颜色
 * 当用户修改颜色选择器时，更新选中节点的颜色
 * 同时更新节点的数据模型以保持状态一致
 */
const updateNodeColor = () => {
	if (!myDiagram || !selectedNode.value) return;

	myDiagram.startTransaction('change color');
	const nodeData = myDiagram.model.findNodeDataForKey(selectedNode.value.key);
	if (nodeData) {
		myDiagram.model.setDataProperty(nodeData, 'color', nodeColor.value);
	}
	myDiagram.commitTransaction('change color');
};

/**
 * 处理从组件库拖拽到画布的节点
 * 当用户从左侧组件库拖拽节点到画布时触发
 * 创建新节点并添加到图中
 */
const handleExternalObjectsDropped = (e: go.DiagramEvent) => {
	const diagram = e.diagram;
	const selection = diagram.selection;

	// 获取鼠标当前位置
	const documentPoint = diagram.lastInput.documentPoint;

	// 检查是否有节点被拖入
	selection.each((part) => {
		if (part instanceof go.Node) {
			// 开始事务，确保原子操作
			diagram.startTransaction('update dropped node');

			// 获取节点数据
			const nodeData = part.data;

			// 获取节点位置 - 使用确切的拖放位置
			const newPos = [documentPoint.x, documentPoint.y];
			diagram.model.setDataProperty(nodeData, 'pos', newPos);

			// 移除isTemplate标记
			diagram.model.setDataProperty(nodeData, 'isTemplate', false);

			// === 为拖放的节点添加标签位置配置 ===
			if (!nodeData.labelAlignment) {
				diagram.model.setDataProperty(nodeData, 'labelAlignment', go.Spot.stringify(new go.Spot(0.5, 1, 0, 10)));
			}

			// 确保使用原始组件的宽度和高度
			const iconInfo = icons[nodeData.type]; // 直接从icons中获取
			if (iconInfo && nodeData.type === 'BusbarSection') {
				// 为母线设置正确的宽度
				const defaultBusWidth = 150;
				// 确保width属性是一个数字并且是有效的
				const width = typeof nodeData.width === 'number' && nodeData.width > 0 ? nodeData.width : defaultBusWidth;
				diagram.model.setDataProperty(nodeData, 'width', width);
				console.log('拖入母线节点，设置宽度:', width, '节点ID:', nodeData.key);
			}

			// 确保使用正确的颜色
			if (iconInfo) {
				diagram.model.setDataProperty(nodeData, 'color', iconInfo.defaultColor);
			}

			// 确保节点数据包含一个唯一的key，而不是Palette中的临时key
			const nodeKey = nodeData.key;
			let finalNodeKey = nodeKey;
			if (typeof nodeKey === 'string' && nodeKey.startsWith('palette-')) {
				const uniqueId = `node-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
				diagram.model.setDataProperty(nodeData, 'key', uniqueId);
				finalNodeKey = uniqueId;
			}

			// 提交事务
			diagram.commitTransaction('update dropped node');

			// === 为拖拽的节点创建LabelNode ===
			setTimeout(() => {
				if (diagram.model instanceof go.GraphLinksModel) {
					// 创建LabelNode数据
					const labelKey = finalNodeKey + '-label';
					const labelOffsetX = 0;
					const labelOffsetY = 30; // 标签在主节点下方
					const labelPos = new go.Point(newPos[0] + labelOffsetX, newPos[1] + labelOffsetY);

					const labelNodeData = {
						key: labelKey,
						text: nodeData.name || nodeData.type || '未命名节点',
						loc: go.Point.stringify(labelPos),
						category: 'LabelNode',
						visible: true,
						parentNodeId: finalNodeKey,
						color: iconInfo?.defaultColor || '#000000',
						offsetX: labelOffsetX,
						offsetY: labelOffsetY,
					};

					// 添加LabelNode到模型
					diagram.model.addNodeData(labelNodeData);

					console.log('为拖拽节点创建LabelNode:', labelNodeData);
				}
			}, 50); // 稍微延迟，确保主节点完全创建

			// 添加自定义处理逻辑
			console.log('节点已拖入位置:', newPos, `已添加组件: ${nodeData.name}`, '类型:', nodeData.type);
		}
	});
};

/**
 * 更新节点文本
 * 支持LabelNode、PropertyNode和普通节点的文本更新
 */
const updateNodeText = () => {
	if (!myDiagram || !selectedNode.value) return;

	myDiagram.startTransaction('change text');
	const nodeData = myDiagram.model.findNodeDataForKey(selectedNode.value.key);
	if (nodeData) {
		// LabelNode节点使用text属性
		if (nodeData.category === 'LabelNode') {
			myDiagram.model.setDataProperty(nodeData, 'text', nodeText.value);
		}
		// PropertyNode节点使用properties属性
		else if (nodeData.category === 'PropertyNode') {
			myDiagram.model.setDataProperty(nodeData, 'properties', nodeText.value);
		}
		// 普通节点使用name属性
		else {
			myDiagram.model.setDataProperty(nodeData, 'name', nodeText.value);
		}
	}
	myDiagram.commitTransaction('change text');
};

/**
 * 设置连线模板 - 使用BusLink实现智能母线连接
 *
 * @param diagram - GoJS图表实例
 */
const setupLinkTemplate = (diagram: go.Diagram) => {
	const $ = go.GraphObject.make;

	diagram.linkTemplate = $(
		BusLink, // 使用自定义BusLink类
		{
			routing: go.Link.Orthogonal, // 正交路由，与示例保持一致
			relinkableFrom: true, // 允许重新连接起点
			relinkableTo: true, // 允许重新连接终点
			adjusting: go.Link.None,
			corner: 1, // 适当的转角圆润度
			curve: go.Link.None, // 连线交叉时跳跃
			reshapable: true, // 允许调整连线形状
		},
		$(go.Shape, {
			name: 'SHAPE',
			strokeWidth: 2, // 与示例保持一致的线宽
		}).bind('stroke', 'color')
	);
};

/**
 * 组件初始化函数 - 在组件挂载时调用，初始化图表和组件库
 *
 * 初始化顺序很重要:
 * 1. 先初始化图表和模板
 * 2. 再初始化组件库
 * 3. 最后加载数据和连线
 */
onMounted(() => {
	// === 1. 初始化主图表 ===
	initDiagram();

	// === 2. 初始化组件库 ===
	initPalette();

	// === 3. 加载图表数据 ===
	get_query_single_line_graph();

	// 确保初始时所有节点的端口都是隐藏状态 (参考示例)
	setTimeout(() => {
		if (myDiagram) {
			hideAllPorts(myDiagram);
		}
	}, 200);
});

/**
 * 保存模型到localStorage
 * 将当前图表状态序列化为JSON并保存到浏览器本地存储
 */
function saveModel() {
	if (!myDiagram) return;

	const modelJson = myDiagram.model.toJson();
	console.log('modelJson', modelJson);
	localStorage.setItem('gojs-demo3-model', modelJson);

	ElMessage.success('模型已保存');
}

/**
 * 从localStorage加载模型
 * 从浏览器本地存储读取保存的图表状态并恢复
 */
function loadModel() {
	if (!myDiagram) return;

	const savedModel = localStorage.getItem('gojs-demo3-model');
	if (savedModel) {
		try {
			// 保存当前选中状态
			const savedSelection = myDiagram.selection.toArray();
			const diagram = myDiagram; // 创建一个局部引用，避免空检查问题

			// 加载模型
			diagram.model = go.Model.fromJson(savedModel);

			// 确保所有母线节点都有正确的宽度
			diagram.nodes.each((node: go.Node) => {
				if (node.data.type === 'BusbarSection') {
					// 检查宽度是否存在且有效
					if (typeof node.data.width !== 'number' || node.data.width <= 0) {
						console.log(`修复母线节点 ${node.data.key} 的宽度，设置为默认值150`);
						diagram.model.startTransaction('fix BusbarSection width');
						diagram.model.setDataProperty(node.data, 'width', 150);
						diagram.model.commitTransaction('fix BusbarSection width');
					} else {
						console.log(`加载母线节点 ${node.data.key}，宽度为 ${node.data.width}`);
					}

					// 确保视图更新
					node.updateTargetBindings();
				}
			});

			// 触发一次调整工具初始化
			diagram.toolManager.resizingTool = new BusResizeMultipleTool();
			// 确保调整大小工具生效
			diagram.toolManager.resizingTool.isEnabled = true;
			console.log('加载模型 - 重新注册ResizingTool并启用');

			// 恢复选中状态（如果节点仍存在）
			savedSelection.forEach((obj: go.Part) => {
				const node = diagram.findNodeForKey(obj.data.key);
				if (node) node.isSelected = true;
			});

			ElMessage.success('模型已加载');

			// 强制布局更新，确保所有元素正确显示
			diagram.layoutDiagram(true);
		} catch (error) {
			console.error('加载模型时发生错误:', error);
			ElMessage.error('加载模型失败');
		}
	} else {
		ElMessage.warning('没有找到保存的模型');
	}
}

defineExpose({
	diagram: diagramInstance, // 暴露响应式的 diagram 引用
});
</script>

<style scoped>
/* 自定义滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
	width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
	background: #f1f1f1;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
	background: #ccc;
	border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
	background: #999;
}
</style>
