# 站内接线图组件

优化后的站内接线图组件，采用模块化架构设计，具备高性能和良好的可维护性。

## 特性

- 🚀 **高性能**: 采用增量更新策略，支持大量节点的高效渲染
- 🔧 **模块化**: 基于 Hook 架构，功能模块清晰分离
- 🎨 **可定制**: 支持组件搜索、分类、属性编辑等功能
- 📊 **性能监控**: 内置性能监控和内存管理功能
- 🔄 **智能更新**: 自动检测数据变化，仅更新必要的部分

## 架构设计

### Hook 模块

| Hook                   | 功能描述                        |
| ---------------------- | ------------------------------- |
| `useDataFetching`      | 数据获取、处理、缓存和错误处理  |
| `useDiagramManagement` | GoJS 图表初始化、配置、模板设置 |
| `useNodeManagement`    | 节点选择、属性更新、端口管理    |
| `useLinkManagement`    | 连线创建、验证、路径更新        |
| `usePropertyDisplay`   | 属性节点显示、隐藏、位置管理    |
| `usePalette`           | 组件库管理、搜索、分类功能      |
| `useIncrementalUpdate` | 增量更新、性能优化、内存管理    |

## 使用方法

### 基础用法

```vue
<template>
	<SingleLineComponent
		ref="singleLineRef"
		:showToolbar="true"
		:showPropertyPanel="true"
		:autoInitialize="true"
		@nodeSelected="handleNodeSelected"
		@modelChanged="handleModelChanged"
	/>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import SingleLineComponent from '@/views/components/gojs/single-line/index.vue';

const singleLineRef = ref();

// 模拟数据
const nodeData = [
	{
		key: 'node1',
		category: 'BusbarSection',
		type: 'BusbarSection',
		name: '母线1',
		color: '#1c57ea',
		pos: [100, 100],
		voltage: '220',
		properties: { vn_kv: 220.5 },
	},
	{
		key: 'node2',
		category: 'Load',
		type: 'Load',
		name: '负荷1',
		color: '#ff6b6b',
		pos: [300, 200],
		voltage: '110',
		properties: { p_mw: 100.5, q_mvar: 50.2 },
	},
];

const linkData = [
	{
		key: 'link1',
		from: 'node1',
		to: 'node2',
		fromPort: 'port1',
		toPort: 'port2',
		voltage: '220',
		color: '#1c57ea',
	},
];

onMounted(() => {
	// 加载数据到图表
	singleLineRef.value.loadGraphData(nodeData, linkData, {
		autoCenter: true,
		showProperties: false,
	});
});

const handleNodeSelected = (node) => {
	console.log('选中节点:', node);
};

const handleModelChanged = (data) => {
	console.log('模型变化:', data);
};
</script>
```

### 高级用法

```vue
<template>
	<div>
		<SingleLineComponent ref="singleLineRef" :nodeInfo="substationInfo" @nodeSelected="handleNodeSelected" />

		<div class="controls">
			<button @click="showPerformanceReport">性能报告</button>
			<button @click="cleanupMemory">清理内存</button>
			<button @click="saveModel">保存模型</button>
			<button @click="loadModel">加载模型</button>
		</div>
	</div>
</template>

<script setup>
import { ref } from 'vue';
import SingleLineComponent from '@/views/components/gojs/single-line/index.vue';

const singleLineRef = ref();
const substationInfo = ref({
	key: 'substation-001',
	name: '变电站A',
});

const handleNodeSelected = (node) => {
	console.log('选中节点:', node);
};

const showPerformanceReport = () => {
	const report = singleLineRef.value.getPerformanceReport();
	console.log('性能报告:', report);
};

const cleanupMemory = () => {
	singleLineRef.value.cleanupMemory();
};

const saveModel = () => {
	singleLineRef.value.saveModel();
};

const loadModel = () => {
	singleLineRef.value.loadModel();
};
</script>
```

## API 参考

### Props

| 属性                   | 类型      | 默认值  | 描述               |
| ---------------------- | --------- | ------- | ------------------ |
| `showToolbar`          | `Boolean` | `true`  | 是否显示工具栏     |
| `showOverview`         | `Boolean` | `false` | 是否显示缩略图     |
| `showPropertyPanel`    | `Boolean` | `true`  | 是否显示属性面板   |
| `autoInitialize`       | `Boolean` | `true`  | 是否自动初始化图表 |
| `enableSearch`         | `Boolean` | `true`  | 是否启用组件库搜索 |
| `enableCategorization` | `Boolean` | `true`  | 是否启用组件库分类 |

### Events

| 事件名         | 参数                                   | 描述                                      |
| -------------- | -------------------------------------- | ----------------------------------------- |
| `nodeSelected` | `node: any \| null`                    | 节点选择事件，参数为选中的节点数据或 null |
| `linkSelected` | `link: any \| null`                    | 连线选择事件，参数为选中的连线数据或 null |
| `modelChanged` | `data: { nodes: any[], links: any[] }` | 模型变化事件，参数为当前的节点和连线数据  |

### 暴露的方法

#### 核心数据操作方法（参考地理接线图模式）

- `initBlankGraph()`: 场景 1 - 初始化空白图表实例
- `loadGraphData(nodeData, linkData, options)`: 场景 2 - 加载数据到已初始化的图表中
- `updateGraphData(nodeData, linkData, options)`: 场景 3 - 完整更新图表数据（支持增量更新）

#### 状态相关

- `isLoading`: 计算属性，表示是否正在加载
- `isInitialized`: 计算属性，表示图表是否已初始化
- `hasData`: 计算属性，表示是否有数据

#### 数据相关

- `getOriginalData()`: 获取原始数据 `{ nodes, links }`
- `clearData()`: 清空图表数据

#### 节点相关

- `selectedNode`: 计算属性，当前选中的节点
- `updateNodeText()`: 更新选中节点的文本
- `updateNodeColor()`: 更新选中节点的颜色

#### 属性显示

- `showResult`: 计算属性，属性显示状态
- `toggleProperties(show: boolean)`: 切换属性显示状态

#### 模型操作

- `saveModel()`: 保存模型到 localStorage
- `loadModel()`: 从 localStorage 加载模型

#### 性能相关

- `getPerformanceReport()`: 获取性能报告
- `cleanupMemory()`: 清理内存
- `isUpdating`: 计算属性，是否正在更新
- `performanceMetrics`: 计算属性，性能指标

## 性能优化

### 增量更新

组件采用智能增量更新策略：

1. **首次加载**: 使用`loadGraphData`进行全量加载
2. **后续更新**: 使用`updateGraphData`进行增量更新
3. **变化检测**: 自动检测节点和连线的增删改变化
4. **最小更新**: 仅更新发生变化的部分

### 性能监控

```javascript
// 获取性能报告
const report = singleLineRef.value.getPerformanceReport();
console.log(report);
// 输出:
// {
//   totalNodes: 150,
//   totalLinks: 200,
//   lastUpdateDuration: 45.2,
//   averageUpdateDuration: 38.7,
//   updateCount: 5,
//   cacheSize: { nodes: 150, links: 200 },
//   memoryUsage: { estimatedNodeMemory: 153600, estimatedLinkMemory: 102400 },
//   lastUpdateTime: 1703123456789
// }
```

### 内存管理

```javascript
// 清理内存
singleLineRef.value.cleanupMemory();
```

## 组件库功能

### 搜索和分类

- **搜索**: 支持按组件名称和类型搜索
- **分类**: 按设备类型自动分类
- **拖拽**: 支持从组件库拖拽到画布

### 自定义分类

```javascript
const customCategories = {
	电源设备: ['Synchronousmachine'],
	母线设备: ['BusbarSection'],
	变压器: ['Trafo', 'Trafo3w'],
	负荷设备: ['Load'],
	开关设备: ['Switch'],
	补偿设备: ['ShuntCompensator'],
	线路设备: ['ACLineDot', 'Acline'],
};
```

## 数据格式

### 节点数据格式

```typescript
interface SingleLineNode {
	key: string; // 唯一标识
	category: string; // 节点类型
	type: string; // 设备类型
	name: string; // 显示名称
	color: string; // 颜色
	pos: [number, number]; // 位置坐标
	angle?: number; // 旋转角度
	voltage?: string; // 电压等级
	voltage2?: string; // 第二电压等级
	voltage3?: string; // 第三电压等级
	width?: number; // 宽度（母线专用）
	properties?: any; // 属性数据
}
```

### 连线数据格式

```typescript
interface SingleLineLink {
	key: string; // 唯一标识
	from: string; // 起点节点ID
	to: string; // 终点节点ID
	fromPort?: string; // 起点端口ID
	toPort?: string; // 终点端口ID
	properties?: any; // 属性数据
	voltage?: string; // 电压等级
	color?: string; // 颜色
}
```

## 故障排除

### 常见问题

1. **组件无法显示**

   - 检查是否正确传入`nodeInfo`属性
   - 确认`dataPacket`是否正确注入

2. **性能问题**

   - 使用`getPerformanceReport()`检查性能指标
   - 定期调用`cleanupMemory()`清理内存
   - 检查节点数量是否超过建议阈值（500 个）

3. **数据加载失败**
   - 检查 API 接口是否正常
   - 查看浏览器控制台错误信息
   - 确认数据格式是否符合要求

### 调试技巧

```javascript
// 启用调试模式
console.log('组件状态:', {
	isLoading: singleLineRef.value.isLoading,
	hasData: singleLineRef.value.hasData,
	selectedNode: singleLineRef.value.selectedNode,
	performanceMetrics: singleLineRef.value.performanceMetrics,
});
```

## 更新日志

### v2.0.0 (当前版本)

- ✨ 全新的 Hook 架构设计
- 🚀 增量更新性能优化
- 📊 内置性能监控
- 🔍 组件库搜索和分类
- 🎨 改进的 UI 设计
- 🧪 完整的测试覆盖

### v1.x.x (旧版本)

- 基础的 GoJS 图表功能
- 简单的数据加载和显示

## 开发指南

### 添加新的节点类型

1. 在`icons/index.ts`中添加新的图标配置
2. 在`templates/index.ts`中添加对应的模板
3. 在`useDataFetching.ts`的`formatPropertyText`中添加属性格式化逻辑

### 自定义 Hook

```typescript
// 创建自定义Hook
export function useCustomFeature() {
	const state = ref(false);

	const toggleState = () => {
		state.value = !state.value;
	};

	return {
		state: computed(() => state.value),
		toggleState,
	};
}

// 在组件中使用
const customFeature = useCustomFeature();
```

### 扩展组件库

```typescript
// 自定义组件库配置
const palette = usePalette(paletteRef, componentObjects, {
	enableSearch: true,
	enableCategorization: true,
	customCategories: {
		自定义分类: ['CustomType1', 'CustomType2'],
	},
});
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

MIT License
