﻿/*
 *  Copyright 1998-2025 by Northwoods Software Corporation. All Rights Reserved.
 */

/*
 * This is an extension and not part of the main GoJS library.
 * The source code for this is at extensionsJSM/ParallelRouteLink.ts.
 * Note that the API for this class may change with any version, even point releases.
 * If you intend to use an extension in production, you should copy the code to your own source directory.
 * Extensions can be found in the GoJS kit under the extensions or extensionsJSM folders.
 * See the Extensions intro page (https://gojs.net/latest/intro/extensions.html) for more information.
 */

import * as go from 'gojs';

/**
 * This custom {@link go.Link} class customizes its route to go parallel to other links connecting the same ports,
 * if the link is not orthogonal and is not Bezier curved.
 *
 * If you want to experiment with this extension, try the <a href="../../samples/ParallelRoute.html">Parallel Route Links</a> sample.
 * @category Part Extension
 */
export class ParallelRouteLink extends go.Link {
	constructor(init?: Partial<ParallelRouteLink>) {
		super();
		if (init) Object.assign(this, init);
	}

	/**
	 * Constructs the link's route by modifying {@link points}.
	 * @returns true if it computed a route of points
	 */
	override computePoints(): boolean {
		const result = super.computePoints();
		if (!this.isOrthogonal && this.curve !== go.Curve.Bezier && this.hasCurviness()) {
			const curv = this.computeCurviness();
			if (curv !== 0) {
				const num = this.pointsCount;
				let pidx = 0;
				let qidx = num - 1;
				if (num >= 4) {
					pidx++;
					qidx--;
				}

				const frompt = this.getPoint(pidx);
				const topt = this.getPoint(qidx);
				const dx = topt.x - frompt.x;
				const dy = topt.y - frompt.y;

				let mx = frompt.x + (dx * 1) / 8;
				let my = frompt.y + (dy * 1) / 8;
				let px = mx;
				let py = my;
				if (-0.01 < dy && dy < 0.01) {
					if (dx > 0) py -= curv;
					else py += curv;
				} else {
					const slope = -dx / dy;
					let e = Math.sqrt((curv * curv) / (slope * slope + 1));
					if (curv < 0) e = -e;
					px = (dy < 0 ? -1 : 1) * e + mx;
					py = slope * (px - mx) + my;
				}

				mx = frompt.x + (dx * 7) / 8;
				my = frompt.y + (dy * 7) / 8;
				let qx = mx;
				let qy = my;
				if (-0.01 < dy && dy < 0.01) {
					if (dx > 0) qy -= curv;
					else qy += curv;
				} else {
					const slope = -dx / dy;
					let e = Math.sqrt((curv * curv) / (slope * slope + 1));
					if (curv < 0) e = -e;
					qx = (dy < 0 ? -1 : 1) * e + mx;
					qy = slope * (qx - mx) + my;
				}

				this.insertPointAt(pidx + 1, px, py);
				this.insertPointAt(qidx + 1, qx, qy);
			}
		}
		return result;
	}

	/**
	 * 计算并行线路的曲率值以控制间距
	 * 根据线路数量和当前线路索引计算适当的曲率偏移
	 * @returns 计算后的曲率值
	 */
	override computeCurviness(): number {
		const linkdata = this.data;
		if (linkdata) {
			// 线路总数，默认为1
			const lineCount = linkdata.lineCount || 1;
			// 当前线条索引，默认为0（第一条线）
			const lineIndex = linkdata.lineIndex || 0;

			// 基础间距值 - 调整这个值可以控制线路间距大小
			// 值越大，线路间距越大
			const BASE_SPACING = 20;

			// 根据线条总数和当前索引计算曲率偏移量
			if (lineCount === 1) {
				// 单线情况，居中显示
				return 0;
			} else if (lineCount === 2) {
				// 双线情况：每条线偏移 BASE_SPACING/2
				// 这样两条线之间的距离就是 BASE_SPACING
				return lineIndex === 0 ? -BASE_SPACING / 2 : BASE_SPACING / 2;
			} else if (lineCount === 3) {
				// 三线情况：一条居中，另外两条向两侧偏移
				if (lineIndex === 0) return -BASE_SPACING;
				if (lineIndex === 1) return 0;
				if (lineIndex === 2) return BASE_SPACING;
			} else if (lineCount === 4) {
				// 四线情况：均匀分布
				if (lineIndex === 0) return (-BASE_SPACING * 3) / 2;
				if (lineIndex === 1) return -BASE_SPACING / 2;
				if (lineIndex === 2) return BASE_SPACING / 2;
				if (lineIndex === 3) return (BASE_SPACING * 3) / 2;
			} else {
				// 更多线路的情况，使用通用公式计算
				// 将 lineIndex 从 0-(lineCount-1) 映射到 -0.5-0.5 的范围
				const normalizedIndex = lineIndex / (lineCount - 1) - 0.5;
				// 计算曲率，最大可达到 ±BASE_SPACING
				return normalizedIndex * BASE_SPACING * 2;
			}
		}

		// 默认返回0曲率（直线）
		return 0;
	}
}
