<template>
	<div
		v-show="visible"
		class="absolute bottom-5 right-5 z-20 bg-white border border-gray-300 rounded-lg shadow-lg"
		style="width: 360px; max-height: 600px"
	>
		<!-- 面板头部 -->
		<div class="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50 rounded-t-lg">
			<h4 class="text-sm font-medium text-gray-900 flex items-center">
				<el-icon class="mr-2"><Filter /></el-icon>
				数据筛选
			</h4>
			<div class="flex items-center space-x-2 gap-2">
				<el-button size="small" type="primary" link @click="confirmFilter">确定</el-button>
				<el-button size="small" type="info" link @click="resetFilter">重置</el-button>
				<el-button size="small" link @click="$emit('close')">
					<el-icon><Hide /></el-icon>
				</el-button>
			</div>
		</div>

		<!-- 面板内容 -->
		<div class="p-3 space-y-4 overflow-y-auto">
			<!-- 电压等级过滤 -->
			<div v-if="groupedData.voltages.length > 0">
				<div class="flex items-center justify-between mb-2">
					<label class="text-sm font-medium text-gray-700">电压等级</label>
					<div class="flex space-x-1 gap-2">
						<el-button size="small" type="primary" link @click="filterHandle('voltages', 'all')"> 全选 </el-button>
						<el-button size="small" type="info" link @click="filterHandle('voltages', 'clear')"> 清空 </el-button>
					</div>
				</div>
				<el-checkbox-group v-model="filterConditions.voltages">
					<div class="gap-1 max-h-50 overflow-y-auto grid grid-cols-3 p-1">
						<el-checkbox
							v-for="voltage in groupedData.voltages"
							:key="voltage.value"
							:label="voltage.value"
							:value="voltage.value"
							class="!mr-0 w-full"
							style="--el-checkbox-height: 14px"
						>
							<span class="flex justify-between items-center w-full">
								<span class="">{{ voltage.label }}</span>
								<span class="text-xs text-gray-500 bg-gray-100 px-1 mx-2 rounded">{{ voltage.count }}</span>
							</span>
						</el-checkbox>
					</div>
				</el-checkbox-group>
			</div>

			<!-- 地区过滤 -->
			<div v-if="groupedData.zones.length > 0">
				<div class="flex items-center justify-between mb-2">
					<label class="text-sm font-medium text-gray-700">地区</label>
					<div class="flex space-x-1 gap-2">
						<el-button size="small" link type="primary" @click="filterHandle('zones', 'all')"> 全选 </el-button>
						<el-button size="small" link type="info" @click="filterHandle('zones', 'clear')"> 清空 </el-button>
					</div>
				</div>
				<el-checkbox-group v-model="filterConditions.zones">
					<div class="gap-1 max-h-50 overflow-y-auto grid grid-cols-3 p-1">
						<el-checkbox
							v-for="zone in groupedData.zones"
							:key="zone.value"
							:label="zone.value"
							:value="zone.value"
							class="!mr-0 w-full"
							style="--el-checkbox-height: 14px"
						>
							<span class="flex justify-between items-center w-full">
								<span class="">{{ zone.label }}</span>
								<span class="text-xs text-gray-500 bg-gray-100 px-1 mx-2 rounded">{{ zone.count }}</span>
							</span>
						</el-checkbox>
					</div>
				</el-checkbox-group>
			</div>

			<!-- 统计信息 -->
			<div v-if="showStats" class="pt-2 border-t border-gray-200">
				<div class="text-xs text-gray-600 space-y-1">
					<div class="flex justify-between">
						<span>选中节点:</span>
						<span>{{ filterStats.selectedNodes }} / {{ filterStats.totalNodes }}</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Filter, Hide } from '@element-plus/icons-vue';

// ===== 组件属性 =====
const props = withDefaults(
	defineProps<{
		visible?: boolean;
		originalNodeData?: SubstationNode[];
		showStats?: boolean;
	}>(),
	{
		visible: false,
		originalNodeData: () => [],
		showStats: false,
	}
);

// ===== 组件事件 =====
const emit = defineEmits<{
	close: [];
	filter: [filteredNodes: SubstationNode[]];
}>();

// ===== 过滤条件状态 =====
const filterConditions = defineModel<{ voltages: string[]; zones: string[] }>('filterConditions', {
	default: () => ({
		voltages: [],
		zones: [],
	}),
});

// ===== 分组数据计算 =====
const groupedData = computed<{
	voltages: { value: string; label: string; count: number; nodes: SubstationNode[] }[];
	zones: { value: string; label: string; count: number; nodes: SubstationNode[] }[];
}>(() => {
	const voltageMap = new Map<string, SubstationNode[]>();
	const zoneMap = new Map<string, SubstationNode[]>();

	props.originalNodeData.forEach((node) => {
		// 按电压等级分组
		const voltage = node.voltage || '未知电压';
		if (!voltageMap.has(voltage)) {
			voltageMap.set(voltage, []);
		}
		voltageMap.get(voltage)!.push(node);

		// 按地区分组
		const zoneName = (node as any).zone || '未知地区';
		if (!zoneMap.has(zoneName)) {
			zoneMap.set(zoneName, []);
		}
		zoneMap.get(zoneName)!.push(node);
	});

	const voltages = Array.from(voltageMap.entries())
		.map(([voltage, nodeList]) => ({
			value: voltage,
			label: voltage,
			count: nodeList.length,
			nodes: [...nodeList],
		}))
		.sort((a, b) => {
			// 按电压等级排序
			const getVoltageNum = (v: string) => {
				const match = v.match(/(\d+)/);
				return match ? parseInt(match[1]) : 0;
			};
			return getVoltageNum(b.value) - getVoltageNum(a.value);
		});

	const zones = Array.from(zoneMap.entries())
		.map(([zone, nodeList]) => ({
			value: zone,
			label: zone,
			count: nodeList.length,
			nodes: [...nodeList],
		}))
		.sort((a, b) => a.label.localeCompare(b.label, 'zh'));

	// 初始化过滤条件为全选状态
	const allVoltages = voltages.map((v) => v.value);
	const allZones = zones.map((z) => z.value);
	if (filterConditions.value.voltages.length === 0 && filterConditions.value.zones.length === 0) {
		filterConditions.value.voltages = [...allVoltages];
		filterConditions.value.zones = [...allZones];
	}

	return {
		voltages,
		zones,
	};
});

// ===== 过滤后的数据计算 =====
const filteredNodeData = computed<SubstationNode[]>(() => {
	if (!props.originalNodeData.length) {
		return [];
	}

	const conditions = filterConditions.value;

	// 如果没有选择任何条件，返回空数组
	if (conditions.voltages.length === 0 && conditions.zones.length === 0) {
		return [];
	}

	// 基于原始数据进行过滤
	return props.originalNodeData.filter((node) => {
		// 电压过滤：如果没有选择任何电压，跳过电压过滤；否则只显示选中的
		const voltageMatch = conditions.voltages.length === 0 || conditions.voltages.includes(node.voltage || '未知电压');

		// 地区过滤：如果没有选择任何地区，跳过地区过滤；否则只显示选中的
		const zoneName = (node as any).zone || '未知地区';
		const zoneMatch = conditions.zones.length === 0 || conditions.zones.includes(zoneName);

		return voltageMatch && zoneMatch;
	});
});

// ===== 过滤统计信息 =====
const filterStats = computed(() => {
	return {
		selectedNodes: filteredNodeData.value.length,
		totalNodes: props.originalNodeData.length,
	};
});

// ===== 过滤方法 =====

/**
 * 处理全选/清空操作
 */
const filterHandle = (type: 'voltages' | 'zones', action: 'all' | 'clear') => {
	if (type === 'voltages') {
		if (action === 'all') {
			filterConditions.value.voltages = groupedData.value.voltages.map((v) => v.value);
		} else if (action === 'clear') {
			filterConditions.value.voltages = [];
		}
	} else if (type === 'zones') {
		if (action === 'all') {
			filterConditions.value.zones = groupedData.value.zones.map((z) => z.value);
		} else if (action === 'clear') {
			filterConditions.value.zones = [];
		}
	}
};

/**
 * 确认过滤：应用过滤条件
 */
const confirmFilter = () => {
	const filtered = filteredNodeData.value;
	console.log('应用过滤条件:', {
		条件: filterConditions.value,
		过滤前节点数: props.originalNodeData.length,
		过滤后节点数: filtered.length,
	});

	// 触发过滤事件，传递过滤后的节点数据
	emit('filter', filtered);
};

/**
 * 重置过滤条件到全选状态
 */
const resetFilter = (): void => {
	filterConditions.value.voltages = groupedData.value.voltages.map((v) => v.value);
	filterConditions.value.zones = groupedData.value.zones.map((z) => z.value);
};

// ===== 监听原始数据变化，重置过滤条件 =====
watch(
	() => props.originalNodeData,
	(newData) => {
		if (newData.length > 0) {
			// 当原始数据变化时，重置为全选状态
			setTimeout(() => {
				confirmFilter();
			}, 0);
		}
	},
	{ immediate: true }
);

// ===== 组件接口 =====
defineExpose({
	filterConditions,
	groupedData,
	filteredNodeData,
	filterStats,
	confirmFilter,
	resetFilter,
	filterHandle,
});
</script>
