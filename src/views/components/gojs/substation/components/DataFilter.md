# DataFilter 数据过滤组件

## 概述

`DataFilter` 是一个专门为厂站图设计的数据过滤组件，提供按电压等级和地区进行节点过滤的功能。组件采用独立设计，可以轻松集成到任何需要数据过滤功能的页面中。

## 特性

- 🎯 **智能分组**：自动按电压等级和地区对节点数据进行分组
- 📊 **实时统计**：显示每个分组的节点数量统计
- 🔍 **多条件过滤**：支持电压等级和地区的多选过滤
- ⚡ **快捷操作**：提供全选、清空、重置等快捷操作
- 🎨 **美观界面**：使用 Element Plus 设计，界面简洁美观
- 🔄 **事件驱动**：通过事件机制与父组件通信

## 组件接口

### Props

| 属性名             | 类型               | 默认值  | 说明                    |
| ------------------ | ------------------ | ------- | ----------------------- |
| `visible`          | `boolean`          | `false` | 控制过滤面板的显示/隐藏 |
| `originalNodeData` | `SubstationNode[]` | `[]`    | 原始节点数据            |
| `showStats`        | `boolean`          | `false` | 是否显示统计信息        |

### Events

| 事件名   | 参数                                | 说明                                 |
| -------- | ----------------------------------- | ------------------------------------ |
| `close`  | `[]`                                | 关闭过滤面板时触发                   |
| `filter` | `[filteredNodes: SubstationNode[]]` | 确认过滤时触发，传递过滤后的节点数组 |

### Expose 方法

| 方法名          | 参数                                                    | 返回值 | 说明                   |
| --------------- | ------------------------------------------------------- | ------ | ---------------------- |
| `confirmFilter` | -                                                       | `void` | 手动触发确认过滤       |
| `resetFilter`   | -                                                       | `void` | 重置过滤条件为全选状态 |
| `filterHandle`  | `type: 'voltages' \| 'zones', action: 'all' \| 'clear'` | `void` | 处理全选/清空操作      |

## 基本使用

### 1. 导入组件

```vue
<script setup lang="ts">
import DataFilter from './components/DataFilter.vue';
</script>
```

### 2. 模板使用

```vue
<template>
	<!-- 显示过滤按钮 -->
	<el-button @click="showFilter = true">显示过滤面板</el-button>

	<!-- 数据过滤组件 -->
	<DataFilter :visible="showFilter" :original-node-data="nodeData" :show-stats="true" @close="showFilter = false" @filter="handleFilter" />
</template>
```

### 3. 响应式数据

```vue
<script setup lang="ts">
const showFilter = ref(false);
const nodeData = ref<SubstationNode[]>([
	{
		key: 'node1',
		name: '变电站A',
		type: 'substation',
		category: 'station',
		voltage: '220kV',
		color: '#1890ff',
		zone: '北京',
	},
	// ... 更多节点数据
]);

// 处理过滤结果
const handleFilter = (filteredNodes: SubstationNode[]) => {
	console.log('过滤后的节点:', filteredNodes);
	// 在这里应用过滤结果，例如更新图表显示
};
</script>
```

## 高级用法

### 1. 与厂站图组件集成

```vue
<template>
	<div class="substation-container">
		<!-- 厂站图组件 -->
		<SubstationGraph ref="substationRef" :node-data="allNodeData" :link-data="linkData" />

		<!-- 过滤组件 -->
		<DataFilter :visible="showFilterPanel" :original-node-data="allNodeData" @close="showFilterPanel = false" @filter="applyGraphFilter" />
	</div>
</template>

<script setup lang="ts">
const substationRef = ref();
const showFilterPanel = ref(false);
const allNodeData = ref<SubstationNode[]>([]);

// 应用过滤到图表
const applyGraphFilter = (filteredNodes: SubstationNode[]) => {
	// 获取过滤后节点的 keys
	const visibleNodeKeys = filteredNodes.map((node) => node.key);

	// 调用厂站图组件的显示/隐藏方法
	substationRef.value?.showHideNodes(allNodeData.value, visibleNodeKeys, convertToLinkData, linkData.value);

	console.log(`已应用过滤: 显示 ${filteredNodes.length}/${allNodeData.value.length} 个节点`);
};
</script>
```

### 2. 自定义过滤逻辑

```vue
<script setup lang="ts">
const filterRef = ref();

// 手动触发过滤
const customFilter = () => {
	// 可以通过 ref 调用组件的方法
	filterRef.value?.confirmFilter();
};

// 重置过滤
const resetFilter = () => {
	filterRef.value?.resetFilter();
};

// 全选某个分组
const selectAllVoltages = () => {
	filterRef.value?.filterHandle('voltages', 'all');
};
</script>

<template>
	<DataFilter ref="filterRef" :visible="showFilter" :original-node-data="nodeData" @filter="handleFilter" />
</template>
```

## 数据格式要求

### SubstationNode 类型

组件期望的节点数据格式：

```typescript
interface SubstationNode {
	key: string; // 节点唯一标识
	name: string; // 节点名称
	type: string; // 节点类型
	category: 'station' | 'plant'; // 节点分类
	voltage: string; // 电压等级 (用于分组)
	color: string; // 节点颜色
	zone?: string; // 地区信息 (用于分组)
	x?: number; // X 坐标
	y?: number; // Y 坐标
	properties?: {
		// 其他属性
		U?: number;
		'U(M)'?: number;
	};
}
```

### 关键字段说明

- **voltage**: 用于电压等级分组，建议格式如 `"220kV"`、`"110kV"` 等
- **zone**: 用于地区分组，如 `"北京"`、`"上海"` 等
- 如果节点数据中缺少这些字段，组件会自动使用 `"未知电压"` 或 `"未知地区"` 作为分组标签

## 样式自定义

组件使用 Tailwind CSS 样式，可以通过以下方式自定义：

```vue
<style scoped>
/* 自定义面板宽度 */
:deep(.data-filter-panel) {
	width: 400px;
}

/* 自定义最大高度 */
:deep(.data-filter-content) {
	max-height: 500px;
}
</style>
```

## 事件处理示例

```vue
<script setup lang="ts">
// 处理过滤事件
const handleFilter = (filteredNodes: SubstationNode[]) => {
	console.log('过滤条件变化:', {
		过滤前: nodeData.value.length,
		过滤后: filteredNodes.length,
		过滤比例: `${((filteredNodes.length / nodeData.value.length) * 100).toFixed(1)}%`,
	});

	// 更新图表显示
	updateGraphDisplay(filteredNodes);

	// 更新其他相关状态
	filteredCount.value = filteredNodes.length;
};

// 处理关闭事件
const handleClose = () => {
	showFilter.value = false;
	console.log('过滤面板已关闭');
};
</script>
```

## 最佳实践

1. **数据预处理**：确保传入的 `originalNodeData` 包含正确的 `voltage` 和 `zone` 字段
2. **性能优化**：对于大量数据，考虑使用虚拟滚动或分页显示
3. **状态管理**：将过滤状态存储在 Pinia 或其他状态管理库中，便于跨组件共享
4. **用户体验**：提供加载状态和错误处理，确保良好的用户体验

## 完整示例

参考 `DataFilterExample.vue` 文件查看完整的使用示例，该文件演示了：

- 基本的组件集成
- 事件处理
- 数据统计显示
- 过滤结果预览

## 注意事项

1. 组件会自动监听 `originalNodeData` 的变化并重置过滤条件
2. 过滤是基于内存操作，不会修改原始数据
3. 组件使用 Element Plus，确保项目中已正确安装和配置
4. 组件位置是绝对定位，确保父容器有正确的 `position` 设置
