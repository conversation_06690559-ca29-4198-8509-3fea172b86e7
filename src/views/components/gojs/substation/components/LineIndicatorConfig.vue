<template>
	<!-- 触发按钮 -->
	<el-tooltip content="配置数据列" placement="top">
		<el-button class="!m-0" size="small" :icon="Setting" type="default" circle @click="openDialog" />
	</el-tooltip>

	<!-- 配置弹框 -->
	<el-dialog v-model="showDialog" title="配置数据列" width="600px" destroy-on-close>
		<div class="space-y-6">
			<!-- 线路指标配置 -->
			<div>
				<h4 class="text-base font-medium mb-3 flex items-center">
					<el-icon class="mr-2"><Setting /></el-icon>
					线路指标
				</h4>
				<div class="grid grid-cols-2 gap-4">
					<el-checkbox v-model="localConfig.showLineName" @change="(bool:boolean) => handleCheckboxChange('showLineName', bool)"
						>线路名称</el-checkbox
					>
					<el-checkbox v-model="localConfig.showLoading" @change="(bool:boolean) => handleCheckboxChange('showLoading', bool)">loading%</el-checkbox>
					<el-checkbox v-model="localConfig.showP" @change="(bool:boolean) => handleCheckboxChange('showP', bool)">P</el-checkbox>
					<el-checkbox v-model="localConfig.showPQ" @change="(bool:boolean) => handleCheckboxChange('showPQ', bool)">P+jQ</el-checkbox>
					<el-checkbox v-model="localConfig.showPM" @change="(bool:boolean) => handleCheckboxChange('showPM', bool)">P(M)</el-checkbox>
					<el-checkbox v-model="localConfig.showPQM" @change="(bool:boolean) => handleCheckboxChange('showPQM', bool)">P+jQ(M)</el-checkbox>
					<el-checkbox v-model="localConfig.showPDelta" @change="(bool:boolean) => handleCheckboxChange('showPDelta', bool)">P(△)</el-checkbox>
					<el-checkbox v-model="localConfig.showPQDelta" @change="(bool:boolean) => handleCheckboxChange('showPQDelta', bool)">P+jQ(△)</el-checkbox>
				</div>
				<!-- <p class="text-xs text-gray-500 mt-2">灰色选项表示数据暂未提供，后续将会支持</p> -->
			</div>
		</div>

		<template #footer>
			<el-space justify="end">
				<el-button @click="cancelDialog">取消</el-button>
				<el-button type="primary" @click="applyConfig">应用配置</el-button>
			</el-space>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';
import { Setting } from '@element-plus/icons-vue';

// 组件事件定义
const emit = defineEmits<{
	'config-applied': [config: LineIndicatorConfig];
}>();

// 内部状态
const showDialog = ref<boolean>(false);
const localConfig = defineModel<LineIndicatorConfig>('config', {
	default: () => ({
		showLineName: true,
		showLoading: true,
		showP: false,
		showPQ: false,
		showPM: false,
		showPQM: false,
		showPDelta: false,
		showPQDelta: false,
	}),
});

const handleCheckboxChange = (key: keyof LineIndicatorConfig, bool: boolean) => {
	if (!bool) return;
	const mutuallyExclusivePairs: [keyof LineIndicatorConfig, keyof LineIndicatorConfig][] = [
		['showP', 'showPQ'],
		['showPM', 'showPQM'],
		['showPDelta', 'showPQDelta'],
	];
	mutuallyExclusivePairs.forEach(([pairKey1, pairKey2]) => {
		if (key === pairKey1) {
			localConfig.value[pairKey1] = bool;
			localConfig.value[pairKey2] = !bool;
		} else if (key === pairKey2) {
			localConfig.value[pairKey1] = !bool;
			localConfig.value[pairKey2] = bool;
		}
	});
};

// 弹框控制方法
const openDialog = () => {
	showDialog.value = true;
};

const cancelDialog = () => {
	showDialog.value = false;
};

const applyConfig = () => {
	// 应用配置并关闭弹框
	showDialog.value = false;

	// 通知父组件配置应用完成
	emit('config-applied', localConfig.value);
};
</script>

<style scoped>
/* 组件特定样式可以在这里添加 */
</style>
