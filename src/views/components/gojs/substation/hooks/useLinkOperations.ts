import * as go from 'gojs';
import { COLORS } from '/@/config/GraphConfig';

export function useLinkOperations() {
	/**
	 * 更新线路的所有视觉颜色元素（线路、箭头、文字）
	 * @param link GoJS Link对象
	 * @param color 新颜色值
	 */
	const updateLinkVisualColor = (link: go.Link, color: string): void => {
		// 更新主线路颜色
		const shape = link.findObject('SHAPE') as go.Shape;
		if (shape) {
			shape.stroke = color;
		}

		// 遍历Link的所有元素，更新箭头和文字颜色
		const iterator = link.elements;
		while (iterator.next()) {
			const obj = iterator.value;
			if (obj instanceof go.Shape) {
				// 检查是否是箭头（有toArrow或fromArrow属性）
				if (obj.toArrow !== 'None' || obj.fromArrow !== 'None') {
					obj.stroke = color;
					obj.fill = color;
				}
			} else if (obj instanceof go.TextBlock) {
				// 更新文字颜色
				obj.stroke = color;
			}
		}
	};

	/**
	 * 通用的线路属性更新方法 - 更新单个线路的单个属性
	 * @param myDiagram GoJS图表实例
	 * @param linkKey 线路的唯一标识
	 * @param propertyName 属性名称
	 * @param propertyValue 属性值
	 * @param transactionName 事务名称（可选）
	 * @param updateDataModel 是否更新数据模型（默认false，仅更新视觉属性）
	 * @returns 是否修改成功
	 */
	const updateLinkProperty = (
		myDiagram: go.Diagram | null,
		linkKey: string,
		propertyName: string,
		propertyValue: any,
		transactionName?: string,
		updateDataModel: boolean = false
	): boolean => {
		if (!myDiagram) {
			console.warn('图表实例未初始化，无法修改线路属性');
			return false;
		}

		// 定义视觉属性列表（这些属性的修改不需要重新布局）
		const visualProperties = ['color', 'strokeWidth', 'opacity', 'stroke', 'strokeDashArray', 'fill'];
		const isVisualProperty = visualProperties.includes(propertyName);

		try {
			// 开始事务
			const txName = transactionName || `update link ${propertyName}`;
			myDiagram.startTransaction(txName);

			// 查找对应的线路
			let linkFound = false;
			myDiagram.links.each((link) => {
				const linkData = link.data;
				// 支持多种方式匹配线路
				const matches =
					(linkData as any).key === linkKey ||
					`${linkData.from}-${linkData.to}` === linkKey ||
					linkData.name === linkKey ||
					(linkData as any).id === linkKey;

				if (matches) {
					if (isVisualProperty && !updateDataModel) {
						// 直接修改GoJS Link对象的视觉属性，避免触发数据模型变化
						const shape = link.findObject('SHAPE') as go.Shape;

						if (propertyName === 'color') {
							// 特殊处理颜色：需要同时更新线路、箭头、文字的颜色
							updateLinkVisualColor(link, propertyValue);
						} else if (shape) {
							switch (propertyName) {
								case 'stroke':
									shape.stroke = propertyValue;
									break;
								case 'strokeWidth':
									shape.strokeWidth = propertyValue;
									break;
								case 'opacity':
									shape.opacity = propertyValue;
									break;
								case 'strokeDashArray':
									shape.strokeDashArray = propertyValue;
									break;
								case 'fill':
									shape.fill = propertyValue;
									break;
							}
						}
					} else {
						// 更新数据模型中的属性（可能触发重新布局）
						myDiagram!.model.setDataProperty(linkData, propertyName, propertyValue);
					}
					linkFound = true;
				}
			});

			// 提交事务
			myDiagram.commitTransaction(txName);

			if (linkFound) {
				console.log(`线路 ${linkKey} 的 ${propertyName} 已更新为`, propertyValue, isVisualProperty ? '(仅视觉更新)' : '(数据模型更新)');
				return true;
			} else {
				console.warn(`未找到线路: ${linkKey}`);
				return false;
			}
		} catch (error) {
			// 回滚事务
			myDiagram.rollbackTransaction();
			console.error(`修改线路 ${propertyName} 失败:`, error);
			return false;
		}
	};

	/**
	 * 通用的线路属性更新方法 - 更新单个线路的多个属性
	 * @param myDiagram GoJS图表实例
	 * @param linkKey 线路的唯一标识
	 * @param properties 属性对象 {propertyName: propertyValue}
	 * @param transactionName 事务名称（可选）
	 * @param updateDataModel 是否更新数据模型（默认false，仅更新视觉属性）
	 * @returns 是否修改成功
	 */
	const updateLinkProperties = (
		myDiagram: go.Diagram | null,
		linkKey: string,
		properties: Record<string, any>,
		transactionName?: string,
		updateDataModel: boolean = false
	): boolean => {
		if (!myDiagram) {
			console.warn('图表实例未初始化，无法修改线路属性');
			return false;
		}

		if (!properties || Object.keys(properties).length === 0) {
			return true;
		}

		// 定义视觉属性列表
		const visualProperties = ['color', 'strokeWidth', 'opacity', 'stroke', 'strokeDashArray', 'fill'];

		try {
			// 开始事务
			const txName = transactionName || 'update link properties';
			myDiagram.startTransaction(txName);

			// 查找对应的线路
			let linkFound = false;
			myDiagram.links.each((link) => {
				const linkData = link.data;
				// 支持多种方式匹配线路
				const matches =
					(linkData as any).key === linkKey ||
					`${linkData.from}-${linkData.to}` === linkKey ||
					linkData.name === linkKey ||
					(linkData as any).id === linkKey;

				if (matches) {
					const shape = link.findObject('SHAPE') as go.Shape;

					// 批量更新属性
					Object.entries(properties).forEach(([propertyName, propertyValue]) => {
						const isVisualProperty = visualProperties.includes(propertyName);

						if (isVisualProperty && !updateDataModel) {
							if (propertyName === 'color') {
								// 特殊处理颜色：需要同时更新线路、箭头、文字的颜色
								updateLinkVisualColor(link, propertyValue);
							} else if (shape) {
								// 处理其他视觉属性
								switch (propertyName) {
									case 'stroke':
										shape.stroke = propertyValue;
										break;
									case 'strokeWidth':
										shape.strokeWidth = propertyValue;
										break;
									case 'opacity':
										shape.opacity = propertyValue;
										break;
									case 'strokeDashArray':
										shape.strokeDashArray = propertyValue;
										break;
									case 'fill':
										shape.fill = propertyValue;
										break;
								}
							}
						} else {
							// 更新数据模型
							myDiagram!.model.setDataProperty(linkData, propertyName, propertyValue);
						}
					});
					linkFound = true;
				}
			});

			// 提交事务
			myDiagram.commitTransaction(txName);

			if (linkFound) {
				const visualPropsCount = Object.keys(properties).filter((key) => visualProperties.includes(key)).length;
				const dataPropsCount = Object.keys(properties).length - visualPropsCount;
				console.log(`线路 ${linkKey} 的属性已更新:`, properties, `(视觉: ${visualPropsCount}, 数据: ${dataPropsCount})`);
				return true;
			} else {
				console.warn(`未找到线路: ${linkKey}`);
				return false;
			}
		} catch (error) {
			// 回滚事务
			myDiagram.rollbackTransaction();
			console.error('修改线路属性失败:', error);
			return false;
		}
	};

	/**
	 * 通用的批量线路属性更新方法
	 * @param myDiagram GoJS图表实例
	 * @param updates 更新配置数组 [{linkKey: string, properties: Record<string, any>}]
	 * @param transactionName 事务名称（可选）
	 * @param updateDataModel 是否更新数据模型（默认false，仅更新视觉属性）
	 * @returns 成功更新的数量
	 */
	const updateMultipleLinksProperties = (
		myDiagram: go.Diagram | null,
		updates: { linkKey: string; properties: Record<string, any> }[],
		transactionName?: string,
		updateDataModel: boolean = false
	): number => {
		if (!myDiagram) {
			console.warn('图表实例未初始化，无法修改线路属性');
			return 0;
		}

		if (!updates || updates.length === 0) {
			return 0;
		}

		// 定义视觉属性列表
		const visualProperties = ['color', 'strokeWidth', 'opacity', 'stroke', 'strokeDashArray', 'fill'];

		try {
			// 开始事务
			const txName = transactionName || 'update multiple links properties';
			myDiagram.startTransaction(txName);

			let successCount = 0;

			// 遍历所有线路
			myDiagram.links.each((link) => {
				const linkData = link.data;

				// 查找是否有匹配的更新配置
				const updateConfig = updates.find(
					(update) =>
						(linkData as any).key === update.linkKey ||
						`${linkData.from}-${linkData.to}` === update.linkKey ||
						linkData.name === update.linkKey ||
						(linkData as any).id === update.linkKey
				);

				if (updateConfig && updateConfig.properties) {
					const shape = link.findObject('SHAPE') as go.Shape;

					// 批量更新属性
					Object.entries(updateConfig.properties).forEach(([propertyName, propertyValue]) => {
						const isVisualProperty = visualProperties.includes(propertyName);

						if (isVisualProperty && !updateDataModel) {
							if (propertyName === 'color') {
								// 特殊处理颜色：需要同时更新线路、箭头、文字的颜色
								updateLinkVisualColor(link, propertyValue);
							} else if (shape) {
								// 处理其他视觉属性
								switch (propertyName) {
									case 'stroke':
										shape.stroke = propertyValue;
										break;
									case 'strokeWidth':
										shape.strokeWidth = propertyValue;
										break;
									case 'opacity':
										shape.opacity = propertyValue;
										break;
									case 'strokeDashArray':
										shape.strokeDashArray = propertyValue;
										break;
									case 'fill':
										shape.fill = propertyValue;
										break;
								}
							}
						} else {
							// 更新数据模型
							myDiagram!.model.setDataProperty(linkData, propertyName, propertyValue);
						}
					});
					successCount++;
				}
			});

			// 提交事务
			myDiagram.commitTransaction(txName);

			console.log(`成功更新 ${successCount} 条线路的属性 ${updateDataModel ? '(包含数据模型)' : '(仅视觉属性)'}`);
			return successCount;
		} catch (error) {
			// 回滚事务
			myDiagram.rollbackTransaction();
			console.error('批量修改线路属性失败:', error);
			return 0;
		}
	};

	/**
	 * 通用的条件性线路属性更新方法
	 * @param myDiagram GoJS图表实例
	 * @param condition 筛选条件函数
	 * @param properties 要更新的属性对象
	 * @param transactionName 事务名称（可选）
	 * @param updateDataModel 是否更新数据模型（默认false，仅更新视觉属性）
	 * @returns 成功更新的数量
	 */
	const updateLinkPropertiesByCondition = (
		myDiagram: go.Diagram | null,
		condition: (linkData: any) => boolean,
		properties: Record<string, any>,
		transactionName?: string,
		updateDataModel: boolean = false
	): number => {
		if (!myDiagram) {
			console.warn('图表实例未初始化，无法修改线路属性');
			return 0;
		}

		if (!properties || Object.keys(properties).length === 0) {
			return 0;
		}

		// 定义视觉属性列表
		const visualProperties = ['color', 'strokeWidth', 'opacity', 'stroke', 'strokeDashArray', 'fill'];

		try {
			// 开始事务
			const txName = transactionName || 'update links properties by condition';
			myDiagram.startTransaction(txName);

			let successCount = 0;

			// 遍历所有线路
			myDiagram.links.each((link) => {
				const linkData = link.data;

				// 检查是否满足条件
				if (condition(linkData)) {
					const shape = link.findObject('SHAPE') as go.Shape;

					// 批量更新属性
					Object.entries(properties).forEach(([propertyName, propertyValue]) => {
						const isVisualProperty = visualProperties.includes(propertyName);

						if (isVisualProperty && !updateDataModel) {
							if (propertyName === 'color') {
								// 特殊处理颜色：需要同时更新线路、箭头、文字的颜色
								updateLinkVisualColor(link, propertyValue);
							} else if (shape) {
								// 处理其他视觉属性
								switch (propertyName) {
									case 'stroke':
										shape.stroke = propertyValue;
										break;
									case 'strokeWidth':
										shape.strokeWidth = propertyValue;
										break;
									case 'opacity':
										shape.opacity = propertyValue;
										break;
									case 'strokeDashArray':
										shape.strokeDashArray = propertyValue;
										break;
									case 'fill':
										shape.fill = propertyValue;
										break;
								}
							}
						} else {
							// 更新数据模型
							myDiagram!.model.setDataProperty(linkData, propertyName, propertyValue);
						}
					});
					successCount++;
				}
			});

			// 提交事务
			myDiagram.commitTransaction(txName);

			console.log(`成功更新 ${successCount} 条线路的属性 ${updateDataModel ? '(包含数据模型)' : '(仅视觉属性)'}`);
			return successCount;
		} catch (error) {
			// 回滚事务
			myDiagram.rollbackTransaction();
			console.error('按条件修改线路属性失败:', error);
			return 0;
		}
	};

	/**
	 * 获取线路信息
	 * @param myDiagram GoJS图表实例
	 * @param linkKey 线路标识
	 * @returns 线路数据或null
	 */
	const getLinkData = (myDiagram: go.Diagram | null, linkKey: string): any | null => {
		if (!myDiagram) {
			return null;
		}

		let foundLinkData: any | null = null;

		myDiagram.links.each((link) => {
			const linkData = link.data;
			const matches =
				(linkData as any).key === linkKey ||
				`${linkData.from}-${linkData.to}` === linkKey ||
				linkData.name === linkKey ||
				(linkData as any).id === linkKey;

			if (matches) {
				foundLinkData = linkData;
			}
		});

		return foundLinkData;
	};

	/**
	 * 获取所有线路信息
	 * @param myDiagram GoJS图表实例
	 * @returns 所有线路数据数组
	 */
	const getAllLinkData = (myDiagram: go.Diagram | null): any[] => {
		if (!myDiagram) {
			return [];
		}

		const allLinks: any[] = [];
		myDiagram.links.each((link) => {
			allLinks.push(link.data);
		});

		return allLinks;
	};

	// ===== 基于通用方法的便捷颜色操作 =====

	/**
	 * 修改单个线路的颜色（基于通用方法）
	 * @param myDiagram GoJS图表实例
	 * @param linkKey 线路的唯一标识
	 * @param color 新的颜色值
	 * @returns 是否修改成功
	 */
	const updateLinkColor = (myDiagram: go.Diagram | null, linkKey: string, color: string): boolean => {
		return updateLinkProperty(myDiagram, linkKey, 'color', color, 'update link color');
	};

	/**
	 * 批量修改多个线路的颜色（基于通用方法）
	 * @param myDiagram GoJS图表实例
	 * @param updates 更新配置数组 [{linkKey: string, color: string}]
	 * @returns 成功更新的数量
	 */
	const updateMultipleLinkColors = (myDiagram: go.Diagram | null, updates: { linkKey: string; color: string }[]): number => {
		const propertiesUpdates = updates.map((update) => ({
			linkKey: update.linkKey,
			properties: { color: update.color },
		}));
		return updateMultipleLinksProperties(myDiagram, propertiesUpdates, 'update multiple link colors');
	};

	/**
	 * 根据条件修改线路颜色（基于通用方法）
	 * @param myDiagram GoJS图表实例
	 * @param condition 筛选条件函数
	 * @param color 新的颜色值
	 * @returns 成功更新的数量
	 */
	const updateLinkColorByCondition = (myDiagram: go.Diagram | null, condition: (linkData: any) => boolean, color: string): number => {
		return updateLinkPropertiesByCondition(myDiagram, condition, { color }, 'update link colors by condition');
	};

	/**
	 * 重置所有线路颜色到默认值（基于通用方法）
	 * @param myDiagram GoJS图表实例
	 * @param defaultColor 默认颜色，如果不提供则使用配置文件中的颜色
	 * @returns 重置的线路数量
	 */
	const resetAllLinkColors = (myDiagram: go.Diagram | null, defaultColor?: string): number => {
		const resetColor = defaultColor || COLORS.KV_500;
		return updateLinkPropertiesByCondition(myDiagram, () => true, { color: resetColor }, 'reset all link colors');
	};

	/**
	 * 高亮指定的线路（基于通用方法）
	 * @param myDiagram GoJS图表实例
	 * @param linkKeys 要高亮的线路标识数组
	 * @param highlightColor 高亮颜色
	 * @param restoreOthers 是否将其他线路恢复到默认样式
	 * @returns 成功高亮的线路数量
	 */
	const highlightLinks = (
		myDiagram: go.Diagram | null,
		linkKeys: string[],
		highlightColor: string = '#ff0000',
		restoreOthers: boolean = true
	): number => {
		if (!myDiagram) {
			console.warn('图表实例未初始化，无法高亮线路');
			return 0;
		}

		const allLinks = getAllLinkData(myDiagram);
		const updates: { linkKey: string; properties: Record<string, any> }[] = [];

		allLinks.forEach((linkData) => {
			const linkKey = linkData.name || `${linkData.from}-${linkData.to}`;
			const shouldHighlight = linkKeys.some(
				(key) => (linkData as any).key === key || `${linkData.from}-${linkData.to}` === key || linkData.name === key || (linkData as any).id === key
			);

			if (shouldHighlight) {
				updates.push({ linkKey, properties: { color: highlightColor } });
			} else if (restoreOthers) {
				updates.push({ linkKey, properties: { color: COLORS.KV_500 } });
			}
		});

		return updateMultipleLinksProperties(myDiagram, updates, 'highlight links');
	};

	// ===== 更多便捷操作方法示例 =====

	/**
	 * 修改线路线宽
	 * @param myDiagram GoJS图表实例
	 * @param linkKey 线路标识
	 * @param strokeWidth 线宽值
	 * @returns 是否修改成功
	 */
	const updateLinkStrokeWidth = (myDiagram: go.Diagram | null, linkKey: string, strokeWidth: number): boolean => {
		return updateLinkProperty(myDiagram, linkKey, 'strokeWidth', strokeWidth, 'update link stroke width');
	};

	/**
	 * 修改线路透明度
	 * @param myDiagram GoJS图表实例
	 * @param linkKey 线路标识
	 * @param opacity 透明度值 (0-1)
	 * @returns 是否修改成功
	 */
	const updateLinkOpacity = (myDiagram: go.Diagram | null, linkKey: string, opacity: number): boolean => {
		return updateLinkProperty(myDiagram, linkKey, 'opacity', opacity, 'update link opacity');
	};

	/**
	 * 批量设置线路样式
	 * @param myDiagram GoJS图表实例
	 * @param linkKey 线路标识
	 * @param style 样式对象 {color?, strokeWidth?, opacity?, dashArray?}
	 * @returns 是否修改成功
	 */
	const updateLinkStyle = (
		myDiagram: go.Diagram | null,
		linkKey: string,
		style: { color?: string; strokeWidth?: number; opacity?: number; dashArray?: number[] }
	): boolean => {
		return updateLinkProperties(myDiagram, linkKey, style, 'update link style');
	};

	return {
		// 通用属性更新方法
		updateLinkProperty,
		updateLinkProperties,
		updateMultipleLinksProperties,
		updateLinkPropertiesByCondition,

		// 便捷颜色控制方法
		updateLinkColor,
		updateMultipleLinkColors,
		updateLinkColorByCondition,
		resetAllLinkColors,
		highlightLinks,

		// 更多便捷方法
		updateLinkStrokeWidth,
		updateLinkOpacity,
		updateLinkStyle,

		// 线路数据获取方法
		getLinkData,
		getAllLinkData,

		// 内部方法
		updateLinkVisualColor,
	};
}
