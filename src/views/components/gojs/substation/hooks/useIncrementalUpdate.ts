import { ref } from 'vue';
import * as go from 'gojs';

export function useIncrementalUpdate() {
	// ===== 缓存状态 =====
	const cachedNodeData = ref<any[]>([]);
	const cachedLinkData = ref<any[]>([]);

	// ===== 核心增量更新方法 =====

	/**
	 * 高频场景：批量更新节点属性
	 * 用途：实时数据更新、状态变化、颜色切换等
	 * 性能：只更新属性，不触发布局重新计算
	 */
	const updateNodeProperties = (myDiagram: go.Diagram | null, updates: { nodeKey: string; properties: Record<string, any> }[]): boolean => {
		if (!myDiagram) {
			console.warn('图表实例未初始化，无法更新节点属性');
			return false;
		}

		try {
			myDiagram.startTransaction('update node properties');

			updates.forEach(({ nodeKey, properties }) => {
				const node = myDiagram!.findNodeForKey(nodeKey);
				if (node) {
					Object.keys(properties).forEach((propKey) => {
						myDiagram!.model.setDataProperty(node.data, propKey, properties[propKey]);
					});
				} else {
					console.warn(`未找到节点: ${nodeKey}`);
				}
			});

			myDiagram.commitTransaction('update node properties');
			console.log(`成功更新 ${updates.length} 个节点的属性`);
			return true;
		} catch (error) {
			myDiagram.rollbackTransaction();
			console.error('更新节点属性失败:', error);
			return false;
		}
	};

	/**
	 * 高频场景：批量更新连接线属性
	 * 用途：线路状态变化、负载变化、颜色切换等
	 * 性能：只更新属性，不触发布局重新计算
	 */
	const updateLinkProperties = (myDiagram: go.Diagram | null, updates: { linkKey: string; properties: Record<string, any> }[]): boolean => {
		if (!myDiagram) {
			console.warn('图表实例未初始化，无法更新连接线属性');
			return false;
		}

		try {
			myDiagram.startTransaction('update link properties');

			updates.forEach(({ linkKey, properties }) => {
				let linkFound = false;
				myDiagram!.links.each((link) => {
					const currentLinkKey = getLinkKey(link.data);
					if (currentLinkKey === linkKey) {
						Object.keys(properties).forEach((propKey) => {
							myDiagram!.model.setDataProperty(link.data, propKey, properties[propKey]);
						});
						linkFound = true;
					}
				});

				if (!linkFound) {
					console.warn(`未找到连接线: ${linkKey}`);
				}
			});

			myDiagram.commitTransaction('update link properties');
			console.log(`成功更新 ${updates.length} 个连接线的属性`);
			return true;
		} catch (error) {
			myDiagram.rollbackTransaction();
			console.error('更新连接线属性失败:', error);
			return false;
		}
	};

	/**
	 * 中频场景：显示/隐藏节点（过滤场景）
	 * 用途：数据过滤、分组显示、视图模式切换
	 * 性能：保持视图位置，优化用户体验
	 */
	const showHideNodes = (
		myDiagram: go.Diagram | null,
		allNodeData: any[],
		visibleNodeKeys: string[],
		convertToLinkData: (links: any[]) => any[],
		allLinkData: any[]
	): boolean => {
		if (!myDiagram) {
			console.warn('图表实例未初始化，无法显示/隐藏节点');
			return false;
		}

		try {
			// 保存当前视图状态
			const currentScale = myDiagram.scale;
			const currentPosition = myDiagram.position.copy();

			// 过滤可见节点
			const visibleNodes = allNodeData.filter((node) => {
				const nodeKey = getNodeKey(node);
				return visibleNodeKeys.includes(nodeKey);
			});

			// 更新模型
			const links = convertToLinkData(allLinkData);
			const newModel = new go.GraphLinksModel(visibleNodes, links);
			myDiagram.model = newModel;

			// 恢复视图状态
			setTimeout(() => {
				if (myDiagram) {
					myDiagram.scale = currentScale;
					myDiagram.position = currentPosition;
					const docBounds = myDiagram.documentBounds;
					if (docBounds.width > 0 && docBounds.height > 0) {
						myDiagram.centerRect(docBounds);
					} else {
						myDiagram.scrollToRect(docBounds);
					}
				}
			}, 0);

			console.log(`过滤显示完成: ${visibleNodes.length}/${allNodeData.length} 个节点`);
			return true;
		} catch (error) {
			console.error('显示/隐藏节点失败:', error);
			return false;
		}
	};

	/**
	 * 增加/删除节点（低频使用）
	 * 用于真正的节点增删，会触发布局重新计算
	 */
	const addRemoveNodes = (
		myDiagram: go.Diagram | null,
		nodeData: any[],
		linkData: any[],
		convertToLinkData: (links: any[]) => any[],
		options: {
			forceLayout?: boolean;
			preserveViewport?: boolean;
		} = {}
	): boolean => {
		if (!myDiagram) {
			console.warn('图表实例未初始化，无法增加/删除节点');
			return false;
		}

		const { forceLayout = true, preserveViewport = false } = options;

		try {
			// 保存当前视图状态
			const currentScale = preserveViewport ? myDiagram.scale : null;
			const currentPosition = preserveViewport ? myDiagram.position.copy() : null;

			// 转换连接线数据
			const links = convertToLinkData(linkData);

			// 创建新的数据模型
			const newModel = new go.GraphLinksModel(nodeData, links);
			myDiagram.model = newModel;

			// 根据选项决定是否重新布局
			if (forceLayout) {
				myDiagram.layoutDiagram(true);
			}

			// 恢复视图状态
			if (preserveViewport && currentScale !== null && currentPosition !== null) {
				setTimeout(() => {
					if (myDiagram) {
						myDiagram.scale = currentScale;
						myDiagram.position = currentPosition;
					}
				}, 0);
			}

			// 更新缓存
			cachedNodeData.value = [...nodeData];
			cachedLinkData.value = [...linkData];

			console.log(`增加/删除节点完成: ${nodeData.length} 个节点, ${links.length} 个连接线`);
			return true;
		} catch (error) {
			console.error('增加/删除节点失败:', error);
			return false;
		}
	};

	// ===== 工具方法 =====

	/**
	 * 获取节点唯一标识
	 */
	const getNodeKey = (node: any): string => {
		return (node as any).key || (node as any).id || `${node.name}-${node.x}-${node.y}`;
	};

	/**
	 * 获取连接线唯一标识
	 */
	const getLinkKey = (link: any): string => {
		return (link as any).key || link.name || `${link.from}-${link.to}-${link.lineIndex || 0}`;
	};

	/**
	 * 清理缓存
	 */
	const clearCache = () => {
		cachedNodeData.value = [];
		cachedLinkData.value = [];
	};

	// ===== 便捷方法（简化接口，减少重复代码） =====

	/**
	 * 统一更新方法：支持单个或批量更新节点属性
	 * @param nodeUpdates 单个对象或数组
	 */
	const updateNodes = (
		myDiagram: go.Diagram | null,
		nodeUpdates: { nodeKey: string; properties: Record<string, any> } | { nodeKey: string; properties: Record<string, any> }[]
	): boolean => {
		const updates = Array.isArray(nodeUpdates) ? nodeUpdates : [nodeUpdates];
		return updateNodeProperties(myDiagram, updates);
	};

	/**
	 * 统一更新方法：支持单个或批量更新连接线属性
	 * @param linkUpdates 单个对象或数组
	 */
	const updateLinks = (
		myDiagram: go.Diagram | null,
		linkUpdates: { linkKey: string; properties: Record<string, any> } | { linkKey: string; properties: Record<string, any> }[]
	): boolean => {
		const updates = Array.isArray(linkUpdates) ? linkUpdates : [linkUpdates];
		return updateLinkProperties(myDiagram, updates);
	};

	/**
	 * 条件更新：通过条件函数批量更新节点属性
	 */
	const updateNodesByCondition = (myDiagram: go.Diagram | null, condition: (nodeData: any) => boolean, properties: Record<string, any>): boolean => {
		if (!myDiagram) {
			console.warn('图表实例未初始化');
			return false;
		}

		const updates: { nodeKey: string; properties: Record<string, any> }[] = [];
		myDiagram.nodes.each((node) => {
			if (condition(node.data)) {
				const nodeKey = getNodeKey(node.data);
				updates.push({ nodeKey, properties });
			}
		});

		if (updates.length === 0) {
			console.log('没有节点符合更新条件');
			return true;
		}

		return updateNodeProperties(myDiagram, updates);
	};

	/**
	 * 条件更新：通过条件函数批量更新连接线属性
	 */
	const updateLinksByCondition = (myDiagram: go.Diagram | null, condition: (linkData: any) => boolean, properties: Record<string, any>): boolean => {
		if (!myDiagram) {
			console.warn('图表实例未初始化');
			return false;
		}

		const updates: { linkKey: string; properties: Record<string, any> }[] = [];
		myDiagram.links.each((link) => {
			if (condition(link.data)) {
				const linkKey = getLinkKey(link.data);
				updates.push({ linkKey, properties });
			}
		});

		if (updates.length === 0) {
			console.log('没有连接线符合更新条件');
			return true;
		}

		return updateLinkProperties(myDiagram, updates);
	};

	return {
		// 状态
		cachedNodeData,
		cachedLinkData,

		// 核心方法（按使用频率排序）
		updateNodeProperties, // 高频：批量更新节点属性
		updateLinkProperties, // 高频：批量更新连接线属性
		showHideNodes, // 中频：过滤显示节点
		addRemoveNodes, // 低频：增删节点（完整重建）

		// 便捷方法（统一接口）
		updateNodes, // 统一节点更新接口
		updateLinks, // 统一连接线更新接口
		updateNodesByCondition, // 条件节点更新
		updateLinksByCondition, // 条件连接线更新

		// 工具方法
		getNodeKey,
		getLinkKey,
		clearCache,

		// 向后兼容（已废弃，建议使用新方法）
		updateSingleNodeProperties: (diagram: go.Diagram | null, nodeKey: string, properties: Record<string, any>) =>
			updateNodes(diagram, { nodeKey, properties }),
		updateSingleLinkProperties: (diagram: go.Diagram | null, linkKey: string, properties: Record<string, any>) =>
			updateLinks(diagram, { linkKey, properties }),
		updateNodePropertiesByCondition: updateNodesByCondition,
		updateLinkPropertiesByCondition: updateLinksByCondition,
	};
}
