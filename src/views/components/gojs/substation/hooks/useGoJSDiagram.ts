import { ref, shallowRef, computed } from 'vue';
import { useDebounceFn } from '@vueuse/core';
import * as go from 'gojs';
import { ParallelRouteLink } from '/@/views/components/gojs/substation/ParallelRouteLink';
import { COLORS, ZOOM, getLineSegmentFraction, getInnerCircles } from '/@/config/GraphConfig';

export function useGoJSDiagram() {
	// ===== 状态管理 =====
	const isInitialized = ref<boolean>(false);
	const diagramInstance = shallowRef<go.Diagram | null>(null);
	const isInitialLayoutCompleted = ref<boolean>(false);

	// GoJS实例
	let myDiagram: go.Diagram | null = null;
	let myOverview: go.Overview | null = null;
	let $: any = null;

	// ===== 节点信息 =====
	const selectedInfo = ref<{ node: SubstationNode | null; link: SubstationLink | null }>({ node: null, link: null });

	// ===== 文本缩放控制 =====
	const enableConstantTextSize = ref<boolean>(true);
	const currentDiagramScale = ref<number>(1.0);
	const textScaleRange = { min: 0.5, max: 2.0 };

	// ===== 线路指标配置 =====
	const lineIndicatorConfig = ref<LineIndicatorConfig>({
		showLineName: true,
		showP: false,
		showPQ: false,
		showLoading: true,
		showPM: false,
		showPQM: false,
		showPDelta: false,
		showPQDelta: false,
	});

	// ===== 工具函数 =====

	// ===== 格式化函数 =====

	/**
	 * 格式化节点信息显示
	 */
	const formatNodeInfo = (data: any): string => {
		const parts: string[] = [];
		const props: any = data.properties || {};

		// 节点名称（必须显示）
		if (data.name) {
			parts.push(data.name);
		}

		// U值显示
		const uValue = props['U'];
		if (uValue !== undefined && uValue !== null && String(uValue).trim()) {
			parts.push(`U: ${uValue}`);
		}

		// U(M)值显示
		const umValue = props['U(M)'];
		if (umValue !== undefined && umValue !== null && String(umValue).trim()) {
			parts.push(`U(M): ${umValue}`);
		}

		return parts.join('\n');
	};

	/**
	 * 计算文本的反向缩放值以保持恒定的视觉大小
	 */
	const calculateInverseScale = (diagramScale: number): number => {
		// <--- 接收 diagramScale 参数
		if (!enableConstantTextSize.value || diagramScale <= 0) {
			return 1.0;
		}

		const inverseScale = 1.0 / diagramScale;

		// 限制缩放范围以避免文本过小或过大
		return Math.max(textScaleRange.min, Math.min(textScaleRange.max, inverseScale));
	};

	/**
	 * 批量更新所有节点的文本缩放（使用事务优化性能）
	 */
	const updateAllNodeTextScale = (diagram: go.Diagram, scale: number) => {
		if (!enableConstantTextSize.value || !diagram) return;

		// 使用事务批量更新，避免多次重绘
		diagram.startTransaction('update text scale');
		try {
			const inverseScale = calculateInverseScale(scale);

			// 遍历所有节点，直接设置TextBlock的scale属性
			diagram.nodes.each((node: any) => {
				const text = node.findObject('NODE_LABEL');
				if (text) {
					text.scale = inverseScale;
				}
			});
		} finally {
			diagram.commitTransaction('update text scale');
		}
	};

	// 使用 @vueuse/core 的防抖函数，配置选项
	const debouncedUpdateTextScale = useDebounceFn(updateAllNodeTextScale, 500, {
		maxWait: 1000, // 最大等待时间，确保最终会执行
	});

	/**
	 * 格式化线路数据显示
	 */
	const formatLineDataSingle = (data: any): string => {
		const config = lineIndicatorConfig.value;
		const props: any = data.properties || {};

		// From端数据
		const fromParts: string[] = [];
		if (config.showP) {
			const pFromValue = props.p_from_mw;
			if (pFromValue !== undefined && pFromValue !== null) {
				fromParts.push(`${pFromValue}`);
			}
		}
		if (config.showPQ) {
			const pFromValue = props.p_from_mw;
			const qFromValue = props.q_from_mvar;
			if (pFromValue !== undefined && pFromValue !== null && qFromValue !== undefined && qFromValue !== null) {
				fromParts.push(formatComplexNumber(pFromValue, qFromValue));
			}
		}
		if (config.showPM) {
			const pFromValue = props.dt_p_from_mw;
			if (pFromValue !== undefined && pFromValue !== null) {
				fromParts.push(`${pFromValue}`);
			}
		}
		if (config.showPQM) {
			const pFromValue = props.dt_p_from_mw;
			const qFromValue = props.dt_q_from_mvar;
			if (pFromValue !== undefined && pFromValue !== null && qFromValue !== undefined && qFromValue !== null) {
				fromParts.push(formatComplexNumber(pFromValue, qFromValue));
			}
		}
		// To端数据
		const toParts: string[] = [];
		if (config.showP) {
			const pToValue = props.p_to_mw;
			if (pToValue !== undefined && pToValue !== null) {
				toParts.push(`${pToValue}`);
			}
		}
		if (config.showPQ) {
			const pToValue = props.p_to_mw;
			const qToValue = props.q_to_mvar;
			if (pToValue !== undefined && pToValue !== null && qToValue !== undefined && qToValue !== null) {
				toParts.push(formatComplexNumber(pToValue, qToValue));
			}
		}
		if (config.showPM) {
			const pToValue = props.dt_p_to_mw;
			if (pToValue !== undefined && pToValue !== null) {
				toParts.push(`${pToValue}`);
			}
		}
		if (config.showPQM) {
			const pToValue = props.dt_p_to_mw;
			const qToValue = props.dt_q_to_mvar;
			if (pToValue !== undefined && pToValue !== null && qToValue !== undefined && qToValue !== null) {
				toParts.push(formatComplexNumber(pToValue, qToValue));
			}
		}
		// 中间数据
		const middleParts: string[] = [];
		if (config.showLineName) {
			middleParts.push(data.name || '');
		}
		if (config.showLoading) {
			if (props.loading_percent !== undefined && props.loading_percent !== null) {
				middleParts.push(`${props.loading_percent}`);
			}
		}
		if (config.showPDelta) {
			const pFromValue = props.delta_p;
			if (pFromValue !== undefined && pFromValue !== null) {
				middleParts.push(`${pFromValue}`);
			}
		}
		if (config.showPQDelta) {
			const pFromValue = props.delta_q;
			if (pFromValue !== undefined && pFromValue !== null) {
				middleParts.push(`${pFromValue}`);
			}
		}

		const fromText = fromParts.join(' ');
		const middleText = middleParts.join(' ');
		const toText = toParts.join(' ');

		const displayParts = [fromText, middleText, toText].filter((part) => part.trim().length > 0);
		return displayParts.join('  ');
	};

	/**
	 * 格式化复数显示
	 */
	const formatComplexNumber = (realPart: number, imagPart: number): string => {
		if (imagPart >= 0) {
			return `${realPart}+j${imagPart}`;
		} else {
			return `${realPart}-j${Math.abs(imagPart)}`;
		}
	};

	/**
	 * 转换连接线数据
	 */
	const convertToLinkData = (apiData: any[]): SubstationLink[] => {
		if (!apiData || !Array.isArray(apiData)) {
			console.warn('convertToLinkData: 输入数据无效');
			return [];
		}

		// 收集相同起点和终点的线路
		const routeGroups = new Map<string, any[]>();

		apiData.forEach((item) => {
			try {
				const fromId = String(item.source || '');
				const toId = String(item.target || '');

				if (!fromId || !toId) {
					console.warn('convertToLinkData: 线路缺少起点或终点', item);
					return;
				}

				const [sortedId1, sortedId2] = [fromId, toId].sort();
				const routeKey = `${sortedId1}-${sortedId2}`;

				if (!routeGroups.has(routeKey)) {
					routeGroups.set(routeKey, []);
				}
				routeGroups.get(routeKey)!.push(item);
			} catch (error) {
				console.error('convertToLinkData: 处理线路数据时出错', item, error);
			}
		});

		// 为每组线路分配适当的lineCount和lineIndex
		const result: SubstationLink[] = [];
		routeGroups.forEach((group, routeKey) => {
			const lineCount = group.length;
			const [sortedId1, sortedId2] = routeKey.split('-');

			group.forEach((item, index) => {
				try {
					// 处理路径点
					let points: Point[] = [];
					if (item.lineList) {
						try {
							const lineListData = typeof item.lineList === 'string' ? JSON.parse(item.lineList) : item.lineList;
							if (Array.isArray(lineListData)) {
								points = lineListData.map((point) => ({
									x: parseFloat(point.x || 0),
									y: parseFloat(point.y || 0),
								}));
							}
						} catch (error) {
							console.warn('convertToLinkData: 解析lineList失败', error);
						}
					}

					// 判断是否需要交换节点顺序
					const originalFrom = String(item.source || '');
					const needSwap = originalFrom !== sortedId1;

					const linkData: SubstationLink = {
						name: String(item.name || ''),
						from: sortedId1,
						to: sortedId2,
						source: String(item.source || ''),
						target: String(item.target || ''),
						color: String(item.color || '#00bcd4'),
						voltage: String(item.voltage || ''),
						direction: String(item.direction || 'forward'),
						lineCount: lineCount,
						lineIndex: index,
						points: needSwap ? [...points].reverse() : points,
						properties: item.properties || {},
					};
					result.push(linkData);
				} catch (error) {
					console.error('convertToLinkData: 创建线路数据时出错', item, error);
				}
			});
		});

		console.log(`convertToLinkData: 成功转换 ${result.length} 条线路数据`);
		return result;
	};

	// ===== 模板设置 =====
	/**
	 * 设置节点模板
	 */
	const setupNodeTemplates = (enableLinkContextMenu: boolean = false, emit?: (event: string, ...args: any[]) => void) => {
		if (!myDiagram || !$) return;

		// 变电站节点模板
		const stationTemplate = $(
			go.Node,
			'Spot',
			{
				locationSpot: go.Spot.Center,
				locationObjectName: 'MAIN_PANEL',
				cursor: 'pointer',
				selectionAdorned: false,
				selectionObjectName: 'MAIN_PANEL',
				selectionAdornmentTemplate: $(
					go.Adornment,
					'Spot',
					{
						locationSpot: go.Spot.Center,
					},
					$(go.Shape, 'Circle', {
						fill: null,
						stroke: COLORS.DEFAULT_LINE,
						strokeWidth: 3,
						strokeDashArray: [10, 5],
						width: 26 * ZOOM + 6,
						height: 26 * ZOOM + 6,
					})
				),
			},
			new go.Binding('click', '', (node) => {
				return (e: go.InputEvent, obj: go.GraphObject) => {
					const nodeData = obj.part?.data;
					if (nodeData && emit) {
						selectedInfo.value.node = nodeData;
						emit('node-click', nodeData);
					}
				};
			}),
			new go.Binding('doubleClick', '', (node) => {
				return (e: go.InputEvent, obj: go.GraphObject) => {
					const nodeData = obj.part?.data;
					if (nodeData && emit) {
						selectedInfo.value.node = nodeData;
						emit('node-double-click', nodeData);
					}
				};
			}),
			new go.Binding('location', '', (data: any) => {
				if (data.x !== undefined && data.y !== undefined && !isNaN(Number(data.x)) && !isNaN(Number(data.y))) {
					return new go.Point(Number(data.x), Number(data.y));
				}
				return new go.Point(0, 0);
			}),
			$(
				go.Panel,
				'Spot',
				{
					name: 'MAIN_PANEL',
					isPanelMain: true,
					itemTemplate: $(
						go.Panel,
						'Spot',
						$(
							go.Shape,
							'Circle',
							{
								fill: COLORS.WHITE,
								strokeWidth: 1.5,
							},
							new go.Binding('portId', '', (data, obj) => {
								const panel = obj.panel;
								const index = panel?.itemIndex ?? 0;
								const isOuter = data.isOuter || false;
								return isOuter ? '' : null;
							}),
							new go.Binding('width', 'diameter'),
							new go.Binding('height', 'diameter'),
							new go.Binding('stroke'),
							new go.Binding('strokeWidth')
						)
					),
				},
				new go.Binding('itemArray', '', (data) => getInnerCircles(data.voltage || ''))
			),
			$(
				go.Panel,
				'Vertical',
				{
					alignment: go.Spot.Top,
					alignmentFocus: go.Spot.Bottom,
					margin: new go.Margin(0, 2, -15, 2),
					isPanelMain: false,
					pickable: false,
				},
				$(
					go.TextBlock,
					{
						name: 'NODE_LABEL',
						font: '12px sans-serif',
						stroke: COLORS.DEFAULT,
						textAlign: 'center',
						editable: false,
						wrap: go.TextBlock.WrapFit,
						maxLines: 3,
						spacingBelow: 4,
					},
					new go.Binding('text', '', (data: any) => formatNodeInfo(data)),
					new go.Binding('stroke', 'color')
				)
			)
		);

		// 发电厂模板
		const plantTemplate = $(
			go.Node,
			'Spot',
			{
				locationSpot: go.Spot.Center,
				locationObjectName: 'OUTER_SHAPE',
				cursor: 'pointer',
				selectionAdorned: false,
				selectionObjectName: 'OUTER_SHAPE',
				selectionAdornmentTemplate: $(
					go.Adornment,
					'Spot',
					{
						locationSpot: go.Spot.Center,
					},
					$(go.Shape, 'Circle', {
						fill: null,
						stroke: COLORS.DEFAULT_LINE,
						strokeWidth: 3,
						strokeDashArray: [10, 5],
						width: 26 * ZOOM + 6,
						height: 26 * ZOOM + 6,
					})
				),
			},
			new go.Binding('location', '', (data: any) => {
				if (data.x !== undefined && data.y !== undefined && !isNaN(Number(data.x)) && !isNaN(Number(data.y))) {
					return new go.Point(Number(data.x), Number(data.y));
				}
				return new go.Point(0, 0);
			}),

			new go.Binding('click', '', (node) => {
				return (e: go.InputEvent, obj: go.GraphObject) => {
					const nodeData = obj.part?.data;
					if (nodeData && emit) {
						selectedInfo.value.node = nodeData;
						emit('node-click', nodeData);
					}
				};
			}),
			new go.Binding('doubleClick', '', (node) => {
				return (e: go.InputEvent, obj: go.GraphObject) => {
					const nodeData = obj.part?.data;
					if (nodeData && emit) {
						selectedInfo.value.node = nodeData;
						emit('node-double-click', nodeData);
					}
				};
			}),
			$(go.Shape, 'Circle', {
				name: 'OUTER_SHAPE',
				width: 26 * ZOOM,
				height: 26 * ZOOM,
				fill: COLORS.WHITE,
				strokeWidth: 1.5,
				stroke: COLORS.BLACK,
				portId: '',
			}),
			$(go.Shape, {
				geometryString: 'M -15 0 C -5 -10 5 10 15 0',
				stroke: COLORS.BLACK,
				strokeWidth: 1.5,
				width: 30,
				height: 15,
				fill: null,
			}),
			$(
				go.Panel,
				'Vertical',
				{
					alignment: go.Spot.Top,
					alignmentFocus: go.Spot.Bottom,
					margin: new go.Margin(0, 2, -15, 2),
					isPanelMain: false,
					pickable: false,
				},
				$(
					go.TextBlock,
					{
						name: 'NODE_LABEL',
						font: '12px sans-serif',
						stroke: COLORS.DEFAULT,
						textAlign: 'center',
						editable: false,
						wrap: go.TextBlock.WrapFit,
						maxLines: 3,
						spacingBelow: 4,
					},
					new go.Binding('text', '', (data: any) => formatNodeInfo(data)),
					new go.Binding('stroke', 'color')
				)
			)
		);

		const templmap = new go.Map<string, go.Part>();
		templmap.add('station', stationTemplate);
		templmap.add('plant', plantTemplate);
		myDiagram.nodeTemplateMap = templmap;
	};

	/**
	 * 设置连接线模板
	 */
	const setupLinkTemplate = (enableLinkContextMenu: boolean = false, showLinkContextMenuAt?: (x: number, y: number, linkData: any) => void) => {
		if (!myDiagram || !$) return;

		myDiagram.linkTemplate = new ParallelRouteLink({
			relinkableFrom: true,
			relinkableTo: true,
			reshapable: true,
			curve: go.Link.JumpOver,
			layerName: 'Background',
		})
			.add(
				new go.Shape({
					strokeWidth: 1,
					stroke: COLORS.KV_500,
					name: 'SHAPE',
					strokeDashArray: [0],
				})
					.bind('stroke', 'color')
					.bind('strokeDashArray', '', () => [0])
					.bind('contextClick', '', (data: any) => {
						selectedInfo.value.link = data;
						return enableLinkContextMenu && showLinkContextMenuAt
							? (e: go.InputEvent, obj: go.GraphObject) => {
									// 阻止默认右键菜单
									e.handled = true;
									// 获取鼠标位置
									const mouseEvent = e.event as MouseEvent;
									if (mouseEvent) {
										showLinkContextMenuAt(mouseEvent.clientX, mouseEvent.clientY, data);
									}
							  }
							: null;
					})
			)
			.add(
				new go.Shape({
					toArrow: 'OpenTriangle',
					stroke: COLORS.KV_500,
					fill: COLORS.KV_500,
					scale: 1.5,
					segmentIndex: 0,
					segmentFraction: 0.5,
					segmentOrientation: go.Link.OrientAlong,
				})
					.bind('stroke', 'color')
					.bind('fill', 'color')
					.bind('scale', '', () => 1.5)
					.bind('segmentIndex', '', (data: any) => {
						const lineCount = data.lineCount || 1;
						return lineCount > 1 ? 1 : 0;
					})
					.bind('segmentFraction', '', (data: any) => {
						return getLineSegmentFraction(data.lineCount || 1, data.lineIndex || 0);
					})
					.bind('visible', '', (data: any) => {
						const sourceId = data?.source || '';
						const direction = data?.direction || 'forward';
						const showForwardArrow = (data.from === sourceId && direction === 'forward') || (data.from !== sourceId && direction === 'backward');
						return showForwardArrow;
					})
			)
			.add(
				new go.Shape({
					fromArrow: 'BackwardOpenTriangle',
					stroke: COLORS.KV_500,
					fill: COLORS.KV_500,
					scale: 1.5,
					segmentIndex: 0,
					segmentFraction: 0.5,
					segmentOrientation: go.Link.OrientAlong,
				})
					.bind('stroke', 'color')
					.bind('fill', 'color')
					.bind('scale', '', () => 1.5)
					.bind('segmentIndex', '', (data: any) => {
						const lineCount = data.lineCount || 1;
						return lineCount > 1 ? 1 : 0;
					})
					.bind('segmentFraction', '', (data: any) => {
						return getLineSegmentFraction(data.lineCount || 1, data.lineIndex || 0);
					})
					.bind('visible', '', (data: any) => {
						const sourceId = data?.source || '';
						const direction = data?.direction || 'forward';
						const showBackwardArrow = (data.from === sourceId && direction === 'backward') || (data.from !== sourceId && direction === 'forward');
						return showBackwardArrow;
					})
			)
			.add(
				new go.TextBlock({
					segmentOffset: new go.Point(10, -10),
					font: '12px sans-serif',
					stroke: COLORS.DEFAULT,
					segmentOrientation: go.Link.OrientUpright,
				})
					.bind('text', '', (data: any) => formatLineDataSingle(data))
					.bind('segmentIndex', '', (data: any) => {
						const lineCount = data.lineCount || 1;
						return lineCount > 1 ? 1 : 0;
					})
					.bind('segmentFraction', '', (data: any) => {
						return getLineSegmentFraction(data.lineCount || 1, data.lineIndex || 0);
					})
					.bind('stroke', '', (data: any) => data.color)
			);
	};

	/**
	 * 设置图表布局
	 */
	const setupLayout = () => {
		if (!myDiagram) return;

		const layout = myDiagram.layout as go.ForceDirectedLayout;
		// 增加弹簧长度，让相连的节点距离更远
		layout.defaultSpringLength = 400;

		// 大幅增加电荷，让所有节点间的排斥力更强，这是解决紧凑问题的关键
		layout.defaultElectricalCharge = 600;

		// 如果布局需要更长时间才能稳定下来，可以适当增加迭代次数
		layout.maxIterations = 400;

		myDiagram.layout.isInitial = true;
		myDiagram.layout.isOngoing = true;
	};

	// ===== 图表初始化 =====
	/**
	 * 初始化图表实例（不包含数据）
	 */
	const initDiagram = (
		diagramRef: HTMLElement,
		options: {
			enableLinkContextMenu?: boolean;
			emit?: (event: string, ...args: any[]) => void;
			showLinkContextMenuAt?: (x: number, y: number, linkData: any) => void;
		} = {}
	) => {
		if (!diagramRef || isInitialized.value) return;

		const { enableLinkContextMenu = false, emit, showLinkContextMenuAt } = options;

		try {
			$ = go.GraphObject.make;

			myDiagram = $(go.Diagram, diagramRef, {
				initialContentAlignment: go.Spot.Center,
				'undoManager.isEnabled': true,
				'clickCreatingTool.isEnabled': false,
				'draggingTool.isGridSnapEnabled': true,
				minScale: 0.2, // 最小缩放到20%
				maxScale: 3.0, // 最大缩放到300%
				padding: 50,
				layout: $(go.ForceDirectedLayout, {
					maxIterations: 200,
					defaultSpringLength: 150,
					defaultElectricalCharge: 200,
				}),
			});

			setupNodeTemplates(enableLinkContextMenu, emit);
			setupLinkTemplate(enableLinkContextMenu, showLinkContextMenuAt);
			setupLayout();

			// 添加初始布局完成监听器
			if (myDiagram) {
				myDiagram.addDiagramListener('InitialLayoutCompleted', (e: any) => {
					isInitialLayoutCompleted.value = true;
				});

				// 添加缩放变化监听器  暂时注释缩放处理
				// 	myDiagram.addDiagramListener('ViewportBoundsChanged', (e: any) => {
				// 		const diagram = e.diagram;
				// 		const newScale = diagram.scale;

				// 		// 更新Vue ref，供UI显示（如果需要）
				// 		if (newScale !== currentDiagramScale.value) {
				// 			currentDiagramScale.value = newScale;
				// 		}

				// 		// 使用防抖更新文本缩放：连续缩放时会自动取消之前的调用，只在停下来后执行
				// 		if (enableConstantTextSize.value) {
				// 			debouncedUpdateTextScale(diagram, newScale);
				// 		}
				// 	});
			}

			diagramInstance.value = myDiagram;
			isInitialized.value = true;

			console.log('图表实例初始化完成');
		} catch (error) {
			console.error('初始化图表实例失败:', error);
			isInitialized.value = false;
		}
	};

	/**
	 * 初始化缩略图
	 */
	const initOverview = (overviewRef: HTMLElement) => {
		if (!overviewRef || !myDiagram || !$) return;

		myOverview = $(go.Overview, overviewRef, {
			observed: myDiagram,
			contentAlignment: go.Spot.Center,
			box: $(
				go.Part,
				$(go.Shape, {
					fill: 'rgba(100, 149, 237, 0.2)',
					stroke: 'cornflowerblue',
					strokeWidth: 2,
				})
			),
		});
	};

	/**
	 * 销毁图表实例并清理资源
	 */
	const destroyDiagram = () => {
		try {
			// 清理缩略图
			if (myOverview) {
				myOverview.div = null;
				myOverview = null;
			}

			// 清理主图表
			if (myDiagram) {
				myDiagram.div = null;
				myDiagram = null;
			}

			// 重置状态
			diagramInstance.value = null;
			isInitialized.value = false;
			isInitialLayoutCompleted.value = false;
			$ = null;

			console.log('图表实例已清理');
		} catch (error) {
			console.error('清理图表实例时发生错误:', error);
		}
	};

	// ===== 视图控制 =====
	const zoomIn = () => {
		if (!myDiagram) return;
		myDiagram.commandHandler.increaseZoom();
	};

	const zoomOut = () => {
		if (!myDiagram) return;
		myDiagram.commandHandler.decreaseZoom();
	};

	const resetZoom = () => {
		if (!myDiagram) return;
		myDiagram.zoomToFit();
		myDiagram.contentAlignment = go.Spot.Center;
	};

	/**
	 * 应用配置变更
	 */
	const applyConfig = () => {
		if (!myDiagram) return;

		myDiagram.links.each((link) => {
			link.updateTargetBindings();
		});
	};

	return {
		// 状态
		isInitialized: computed(() => isInitialized.value),
		diagramInstance: computed(() => diagramInstance.value),
		isInitialLayoutCompleted: computed(() => isInitialLayoutCompleted.value),
		selectedInfo: computed(() => selectedInfo.value),
		lineIndicatorConfig,
		// 文本缩放控制
		enableConstantTextSize,
		currentDiagramScale: computed(() => currentDiagramScale.value),
		// 方法
		initDiagram,
		initOverview,
		destroyDiagram,
		zoomIn,
		zoomOut,
		resetZoom,
		applyConfig,
		formatComplexNumber,
		formatNodeInfo,
		formatLineDataSingle,
		convertToLinkData,
		calculateInverseScale,

		// 内部访问（仅供其他hook使用）
		_getDiagram: () => myDiagram,
		_getGoJS: () => $,
	};
}
