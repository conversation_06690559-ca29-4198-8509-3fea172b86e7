/**
 * GoJS 图表性能监控工具
 * 用于检测和分析图表操作的性能瓶颈
 */

export interface PerformanceMetric {
	name: string;
	startTime: number;
	endTime?: number;
	duration?: number;
	metadata?: Record<string, any>;
}

export interface PerformanceReport {
	totalDuration: number;
	operations: PerformanceMetric[];
	slowestOperations: PerformanceMetric[];
	averageDuration: number;
	operationCounts: Record<string, number>;
	memoryUsage?: {
		usedJSHeapSize: number;
		totalJSHeapSize: number;
		jsHeapSizeLimit: number;
	};
}

class PerformanceMonitor {
	private metrics: PerformanceMetric[] = [];
	private activeTimers: Map<string, PerformanceMetric> = new Map();
	private enabled: boolean = true;
	private maxMetrics: number = 1000; // 最大保存的指标数量

	/**
	 * 开始计时
	 */
	start(name: string, metadata?: Record<string, any>): void {
		if (!this.enabled) return;

		const metric: PerformanceMetric = {
			name,
			startTime: performance.now(),
			metadata,
		};

		this.activeTimers.set(name, metric);
	}

	/**
	 * 结束计时
	 */
	end(name: string, additionalMetadata?: Record<string, any>): number | null {
		if (!this.enabled) return null;

		const metric = this.activeTimers.get(name);
		if (!metric) {
			console.warn(`Performance timer '${name}' not found`);
			return null;
		}

		metric.endTime = performance.now();
		metric.duration = metric.endTime - metric.startTime;

		if (additionalMetadata) {
			metric.metadata = { ...metric.metadata, ...additionalMetadata };
		}

		this.activeTimers.delete(name);
		this.addMetric(metric);

		return metric.duration;
	}

	/**
	 * 测量函数执行时间
	 */
	measure<T>(name: string, fn: () => T, metadata?: Record<string, any>): T {
		this.start(name, metadata);
		try {
			const result = fn();
			this.end(name);
			return result;
		} catch (error) {
			this.end(name, { error: error.message });
			throw error;
		}
	}

	/**
	 * 测量异步函数执行时间
	 */
	async measureAsync<T>(name: string, fn: () => Promise<T>, metadata?: Record<string, any>): Promise<T> {
		this.start(name, metadata);
		try {
			const result = await fn();
			this.end(name);
			return result;
		} catch (error) {
			this.end(name, { error: error.message });
			throw error;
		}
	}

	/**
	 * 添加指标
	 */
	private addMetric(metric: PerformanceMetric): void {
		this.metrics.push(metric);

		// 限制指标数量，避免内存泄漏
		if (this.metrics.length > this.maxMetrics) {
			this.metrics = this.metrics.slice(-this.maxMetrics);
		}
	}

	/**
	 * 获取性能报告
	 */
	getReport(filterByName?: string): PerformanceReport {
		let filteredMetrics = this.metrics.filter((m) => m.duration !== undefined);

		if (filterByName) {
			filteredMetrics = filteredMetrics.filter((m) => m.name.includes(filterByName));
		}

		const totalDuration = filteredMetrics.reduce((sum, m) => sum + (m.duration || 0), 0);
		const averageDuration = filteredMetrics.length > 0 ? totalDuration / filteredMetrics.length : 0;

		// 统计操作次数
		const operationCounts: Record<string, number> = {};
		filteredMetrics.forEach((m) => {
			operationCounts[m.name] = (operationCounts[m.name] || 0) + 1;
		});

		// 找出最慢的操作
		const slowestOperations = [...filteredMetrics].sort((a, b) => (b.duration || 0) - (a.duration || 0)).slice(0, 10);

		// 获取内存使用情况
		let memoryUsage;
		if ('memory' in performance) {
			const memory = (performance as any).memory;
			memoryUsage = {
				usedJSHeapSize: memory.usedJSHeapSize,
				totalJSHeapSize: memory.totalJSHeapSize,
				jsHeapSizeLimit: memory.jsHeapSizeLimit,
			};
		}

		return {
			totalDuration,
			operations: filteredMetrics,
			slowestOperations,
			averageDuration,
			operationCounts,
			memoryUsage,
		};
	}

	/**
	 * 获取操作统计
	 */
	getOperationStats(): Record<string, { count: number; totalTime: number; avgTime: number; maxTime: number }> {
		const stats: Record<string, { count: number; totalTime: number; avgTime: number; maxTime: number }> = {};

		this.metrics.forEach((metric) => {
			if (!metric.duration) return;

			if (!stats[metric.name]) {
				stats[metric.name] = {
					count: 0,
					totalTime: 0,
					avgTime: 0,
					maxTime: 0,
				};
			}

			const stat = stats[metric.name];
			stat.count++;
			stat.totalTime += metric.duration;
			stat.maxTime = Math.max(stat.maxTime, metric.duration);
			stat.avgTime = stat.totalTime / stat.count;
		});

		return stats;
	}

	/**
	 * 清除所有指标
	 */
	clear(): void {
		this.metrics = [];
		this.activeTimers.clear();
	}

	/**
	 * 启用/禁用监控
	 */
	setEnabled(enabled: boolean): void {
		this.enabled = enabled;
	}

	/**
	 * 导出性能数据为 JSON
	 */
	exportData(): string {
		return JSON.stringify(
			{
				metrics: this.metrics,
				report: this.getReport(),
				stats: this.getOperationStats(),
				timestamp: new Date().toISOString(),
			},
			null,
			2
		);
	}

	/**
	 * 打印性能报告到控制台
	 */
	printReport(filterByName?: string): void {
		const report = this.getReport(filterByName);
		const stats = this.getOperationStats();

		console.group('🚀 GoJS 性能监控报告');

		console.log('📊 总体统计:');
		console.table({
			总操作数: report.operations.length,
			'总耗时(ms)': report.totalDuration.toFixed(2),
			'平均耗时(ms)': report.averageDuration.toFixed(2),
		});

		console.log('🐌 最慢的操作:');
		console.table(
			report.slowestOperations.slice(0, 5).map((op) => ({
				操作名称: op.name,
				耗时: `${op.duration?.toFixed(2)}ms`,
				元数据: JSON.stringify(op.metadata || {}),
			}))
		);

		console.log('📈 操作统计:');
		const sortedStats = Object.entries(stats)
			.sort(([, a], [, b]) => b.totalTime - a.totalTime)
			.slice(0, 10);

		console.table(
			Object.fromEntries(
				sortedStats.map(([name, stat]) => [
					name,
					{
						调用次数: stat.count,
						总耗时: `${stat.totalTime.toFixed(2)}ms`,
						平均耗时: `${stat.avgTime.toFixed(2)}ms`,
						最大耗时: `${stat.maxTime.toFixed(2)}ms`,
					},
				])
			)
		);

		if (report.memoryUsage) {
			console.log('💾 内存使用:');
			console.table({
				已使用堆内存: `${(report.memoryUsage.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
				总堆内存: `${(report.memoryUsage.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
				堆内存限制: `${(report.memoryUsage.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`,
			});
		}

		console.groupEnd();
	}

	/**
	 * 监控 GoJS 图表的特定操作
	 */
	monitorDiagramOperation(diagram: any, operationName: string, operation: () => void): void {
		this.start(`diagram-${operationName}`, {
			nodeCount: diagram?.nodes?.count || 0,
			linkCount: diagram?.links?.count || 0,
		});

		try {
			operation();
		} finally {
			this.end(`diagram-${operationName}`);
		}
	}

	/**
	 * 监控数据加载性能
	 */
	monitorDataLoad(nodeCount: number, linkCount: number, operation: () => void): void {
		this.start('data-load', { nodeCount, linkCount });

		try {
			operation();
		} finally {
			this.end('data-load');
		}
	}
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor();

// 便捷的装饰器函数
export function measurePerformance(name: string, metadata?: Record<string, any>) {
	return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
		const originalMethod = descriptor.value;

		descriptor.value = function (...args: any[]) {
			return performanceMonitor.measure(`${target.constructor.name}.${propertyKey}`, () => originalMethod.apply(this, args), {
				...metadata,
				args: args.length,
			});
		};

		return descriptor;
	};
}

// 便捷的性能测试函数
export function withPerformanceMonitoring<T extends (...args: any[]) => any>(fn: T, name: string, metadata?: Record<string, any>): T {
	return ((...args: any[]) => {
		return performanceMonitor.measure(name, () => fn(...args), metadata);
	}) as T;
}

export default performanceMonitor;
