<template>
  <div class="h-screen flex flex-col">
    <!-- 性能测试控制面板 -->
    <div class="bg-gray-50 p-4 border-b">
      <h2 class="text-lg font-bold mb-4">变电站组件性能测试</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <!-- 数据生成控制 -->
        <div class="bg-white p-3 rounded border">
          <h3 class="font-medium mb-2">数据生成</h3>
          <div class="space-y-2">
            <div>
              <label class="text-sm">节点数量:</label>
              <el-input-number v-model="nodeCount" :min="10" :max="2000" :step="50" size="small" />
            </div>
            <div>
              <label class="text-sm">电压等级数:</label>
              <el-input-number v-model="voltageCount" :min="2" :max="10" :step="1" size="small" />
            </div>
            <el-button @click="generateTestData" type="primary" size="small" :loading="generating">
              生成测试数据
            </el-button>
          </div>
        </div>

        <!-- 性能测试控制 -->
        <div class="bg-white p-3 rounded border">
          <h3 class="font-medium mb-2">性能测试</h3>
          <div class="space-y-2">
            <el-button @click="testPreFilterLoad" type="success" size="small" :loading="testing">
              测试预过滤加载
            </el-button>
            <el-button @click="testFullLoad" type="warning" size="small" :loading="testing">
              测试全量加载
            </el-button>
            <el-button @click="testFilterSwitch" type="info" size="small" :loading="testing">
              测试过滤切换
            </el-button>
          </div>
        </div>

        <!-- 性能结果显示 -->
        <div class="bg-white p-3 rounded border">
          <h3 class="font-medium mb-2">性能结果</h3>
          <div class="text-sm space-y-1">
            <div v-if="performanceResults.preFilter">
              <span class="text-green-600">预过滤:</span> {{ performanceResults.preFilter }}ms
            </div>
            <div v-if="performanceResults.fullLoad">
              <span class="text-orange-600">全量加载:</span> {{ performanceResults.fullLoad }}ms
            </div>
            <div v-if="performanceResults.filterSwitch">
              <span class="text-blue-600">过滤切换:</span> {{ performanceResults.filterSwitch }}ms
            </div>
            <div v-if="performanceResults.improvement" class="font-medium text-green-700">
              性能提升: {{ performanceResults.improvement }}%
            </div>
          </div>
        </div>
      </div>

      <!-- 当前状态显示 -->
      <div class="flex items-center space-x-4 text-sm text-gray-600">
        <span>总节点: {{ totalNodes }}</span>
        <span>渲染节点: {{ renderedNodes }}</span>
        <span>总连线: {{ totalLinks }}</span>
        <span>渲染连线: {{ renderedLinks }}</span>
        <span>过滤比例: {{ filterRatio }}%</span>
      </div>
    </div>

    <!-- 图表容器 -->
    <div class="flex-1">
      <SubstationGraph
        ref="substationRef"
        :auto-initialize="true"
        :show-overview="false"
        v-model:loading="loading"
        @node-click="handleNodeClick"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import SubstationGraph from '../index.vue';
import { getVoltageColor } from '/@/config/GraphConfig';

// ===== 状态管理 =====
const substationRef = ref<InstanceType<typeof SubstationGraph> | null>(null);
const loading = ref(false);
const generating = ref(false);
const testing = ref(false);

// ===== 测试参数 =====
const nodeCount = ref(500);
const voltageCount = ref(4);

// ===== 测试数据 =====
const testNodeData = ref<SubstationNode[]>([]);
const testLinkData = ref<SubstationLink[]>([]);

// ===== 性能结果 =====
const performanceResults = ref<{
  preFilter?: number;
  fullLoad?: number;
  filterSwitch?: number;
  improvement?: number;
}>({});

// ===== 计算属性 =====
const totalNodes = computed(() => testNodeData.value.length);
const totalLinks = computed(() => testLinkData.value.length);
const renderedNodes = computed(() => {
  // 这里可以从组件获取实际渲染的节点数
  return totalNodes.value;
});
const renderedLinks = computed(() => {
  // 这里可以从组件获取实际渲染的连线数
  return totalLinks.value;
});
const filterRatio = computed(() => {
  if (totalNodes.value === 0) return 0;
  return Math.round((renderedNodes.value / totalNodes.value) * 100);
});

// ===== 数据生成方法 =====

/**
 * 生成测试数据
 */
const generateTestData = async () => {
  generating.value = true;
  
  try {
    // 生成电压等级
    const voltages = Array.from({ length: voltageCount.value }, (_, i) => {
      const voltage = [500, 220, 110, 35, 10][i] || (500 - i * 50);
      return `${voltage}kV`;
    });

    // 生成地区
    const zones = ['华北', '华东', '华中', '东北', '西北', '西南'];

    // 生成节点数据
    const nodes: SubstationNode[] = [];
    for (let i = 0; i < nodeCount.value; i++) {
      const voltage = voltages[i % voltages.length];
      const zone = zones[i % zones.length];
      
      nodes.push({
        key: `node_${i}`,
        name: `变电站_${i}`,
        type: i % 2 === 0 ? 'station' : 'plant',
        category: i % 2 === 0 ? 'station' : 'plant',
        voltage,
        color: getVoltageColor(voltage),
        zone,
        x: Math.random() * 1000,
        y: Math.random() * 800,
        properties: {
          U: Math.random() * 100,
          'U(M)': Math.random() * 120,
        }
      });
    }

    // 生成连线数据（每个节点连接1-3个其他节点）
    const links: SubstationLink[] = [];
    for (let i = 0; i < nodes.length; i++) {
      const connectionCount = Math.floor(Math.random() * 3) + 1;
      
      for (let j = 0; j < connectionCount; j++) {
        const targetIndex = Math.floor(Math.random() * nodes.length);
        if (targetIndex !== i) {
          const sourceNode = nodes[i];
          const targetNode = nodes[targetIndex];
          
          links.push({
            name: `线路_${i}_${targetIndex}`,
            source: sourceNode.key,
            target: targetNode.key,
            from: sourceNode.key,
            to: targetNode.key,
            color: getVoltageColor(sourceNode.voltage),
            voltage: sourceNode.voltage,
            properties: {
              max_i_ka: Math.random() * 1000,
              p_from_mw: Math.random() * 500,
              q_from_mvar: Math.random() * 200,
              p_to_mw: Math.random() * 500,
              q_to_mva: Math.random() * 200,
              pl_mw: Math.random() * 10,
              ql_mvar: Math.random() * 5,
              i_from_ka: Math.random() * 800,
              i_to_ka: Math.random() * 800,
              i_ka: Math.random() * 800,
              vm_from_pu: 0.95 + Math.random() * 0.1,
              va_from_degree: Math.random() * 360,
              vm_to_pu: 0.95 + Math.random() * 0.1,
              va_to_degree: Math.random() * 360,
              loading_percent: Math.random() * 100,
            }
          });
        }
      }
    }

    testNodeData.value = nodes;
    testLinkData.value = links;

    ElMessage.success(`生成测试数据完成: ${nodes.length} 个节点, ${links.length} 条连线`);
  } catch (error) {
    console.error('生成测试数据失败:', error);
    ElMessage.error('生成测试数据失败');
  } finally {
    generating.value = false;
  }
};

// ===== 性能测试方法 =====

/**
 * 测试预过滤加载性能
 */
const testPreFilterLoad = async () => {
  if (!substationRef.value || testNodeData.value.length === 0) {
    ElMessage.warning('请先生成测试数据');
    return;
  }

  testing.value = true;
  
  try {
    const startTime = performance.now();
    
    // 使用预过滤加载（默认只加载前两个电压等级）
    await substationRef.value.loadGraphData(testNodeData.value, testLinkData.value, {
      applyFilter: true,
      autoCenter: true
    });
    
    const endTime = performance.now();
    const duration = Math.round(endTime - startTime);
    
    performanceResults.value.preFilter = duration;
    
    ElMessage.success(`预过滤加载完成: ${duration}ms`);
  } catch (error) {
    console.error('预过滤加载测试失败:', error);
    ElMessage.error('预过滤加载测试失败');
  } finally {
    testing.value = false;
  }
};

/**
 * 测试全量加载性能
 */
const testFullLoad = async () => {
  if (!substationRef.value || testNodeData.value.length === 0) {
    ElMessage.warning('请先生成测试数据');
    return;
  }

  testing.value = true;
  
  try {
    const startTime = performance.now();
    
    // 使用全量加载（不应用过滤）
    await substationRef.value.loadGraphData(testNodeData.value, testLinkData.value, {
      applyFilter: false,
      autoCenter: true
    });
    
    const endTime = performance.now();
    const duration = Math.round(endTime - startTime);
    
    performanceResults.value.fullLoad = duration;
    
    // 计算性能提升
    if (performanceResults.value.preFilter) {
      const improvement = Math.round(
        ((performanceResults.value.fullLoad - performanceResults.value.preFilter) / performanceResults.value.fullLoad) * 100
      );
      performanceResults.value.improvement = improvement;
    }
    
    ElMessage.success(`全量加载完成: ${duration}ms`);
  } catch (error) {
    console.error('全量加载测试失败:', error);
    ElMessage.error('全量加载测试失败');
  } finally {
    testing.value = false;
  }
};

/**
 * 测试过滤切换性能
 */
const testFilterSwitch = async () => {
  if (!substationRef.value || testNodeData.value.length === 0) {
    ElMessage.warning('请先生成测试数据');
    return;
  }

  testing.value = true;
  
  try {
    const startTime = performance.now();
    
    // 模拟过滤切换（选择不同的电压等级）
    const filteredNodes = testNodeData.value.filter(node => 
      node.voltage === '500kV' || node.voltage === '220kV'
    );
    
    substationRef.value.applyFilter(filteredNodes);
    
    const endTime = performance.now();
    const duration = Math.round(endTime - startTime);
    
    performanceResults.value.filterSwitch = duration;
    
    ElMessage.success(`过滤切换完成: ${duration}ms`);
  } catch (error) {
    console.error('过滤切换测试失败:', error);
    ElMessage.error('过滤切换测试失败');
  } finally {
    testing.value = false;
  }
};

// ===== 事件处理 =====
const handleNodeClick = (node: SubstationNode) => {
  console.log('节点点击:', node);
};

// ===== 初始化 =====
// 页面加载时生成默认测试数据
generateTestData();
</script>

<style scoped>
.el-input-number {
  width: 100%;
}
</style>
