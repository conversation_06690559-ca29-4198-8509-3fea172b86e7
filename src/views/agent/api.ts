import { request } from '/@/utils/service';
import { AddReq, EditReq, InfoReq } from '@fast-crud/fast-crud';
/**
 * AgentCreateUpdate
 */
export interface Agent {
	/**
	 * 代码路径
	 */
	code_path: string;
	/**
	 * Create datetime
	 */
	create_datetime?: Date;
	/**
	 * 创建人，创建人
	 */
	creator?: number | null;
	/**
	 * Creator name
	 */
	creator_name?: string;
	/**
	 * 数据归属部门，数据归属部门
	 */
	dept_belong_id?: null | string;
	/**
	 * 描述
	 */
	description?: string;
	/**
	 * Id，Id
	 */
	id: number | string;
	/**
	 * 修改人，修改人
	 */
	modifier?: null | string;
	/**
	 * Modifier name
	 */
	modifier_name?: string;
	/**
	 * 名称
	 */
	name: string;
	/**
	 * 参数
	 */
	parameters: { [key: string]: any };
	/**
	 * 类型
	 */
	type: number;
	/**
	 * Update datetime
	 */
	update_datetime?: Date;
	[property: string]: any;
}

export const typeOptions = [
	{ label: '火电', value: 1 },
	{ label: '风电', value: 2 },
	{ label: '水电', value: 3 },
	{ label: '光伏', value: 4 },
	{ label: '负荷', value: 5 },
];

export const apiPrefix = '/api/pm/agent/';
export function GetList() {
	return request({
		url: apiPrefix,
		method: 'get',
	});
}
export function GetAgent(id: number | string) {
	return request({
		url: apiPrefix + id,
		method: 'get',
	});
}

export function AddAgent(Agent: AddReq) {
	return request({
		url: apiPrefix,
		method: 'post',
		data: Agent,
	});
}

export function UpdateAgent(Agent: EditReq) {
	return request({
		url: apiPrefix + Agent.id + '/',
		method: 'put',
		data: Agent,
	});
}

export function DelAgent(id: number | string) {
	return request({
		url: apiPrefix + id + '/',
		method: 'delete',
		data: { id },
	});
}
