<template>
    <div class="FaultSetContent" v-loading="loading">
        <div class="Tools">
            <el-button type="primary" icon="Plus" @click="handleAdd">添加</el-button>
        </div>
        <div class="tableContent" ref="tableContentRef">
            <table-common :tableData="listItems" :tableColumn="tableColumn"
                :operationTable="operationTable"></table-common>
        </div>
        <div class="pagination">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :pager-count="5"
                :page-sizes="[10, 20, 30, 50, 80, 100]" :total="total" layout="total, sizes, prev, pager, next, jumper"
                background @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange">
            </el-pagination>
        </div>
        <div class="Popup">
            <addPopup ref="addPopupRef" @save-success="handleSaveSuccess"></addPopup>
        </div>
    </div>
</template>

<script setup lang="ts">
import * as api from "./api";
import { reactive, ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus'
import addPopup from "./Popup/Add/index.vue";
import tableCommon from "/@/components/table-common/index.vue";
const addPopupRef = ref()
const loading = ref(false)
const tableContentRef = ref()
// 重新定义 listItems 的类型
const listItems = ref([]);
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(25)
const tableColumn = [
    {
        label: "名称",
        prop: "case_name",
        minWidth: "150",
        fixed: true,
    }
];
const operationTable = {
    label: "操作",
    width: "100",
    fixed: true,
    buttons: [
        {
            label: '编辑',
            type: 'primary',
            clickFunction: (item: any) => {
                editItem(item)
            }
        },
        {
            label: '删除',
            type: 'danger',
            clickFunction: (item: any) => {
                delsaCaseList(item)
            }
        }
    ]
}
const getList = async (page = currentPage.value, size = pageSize.value) => {
    loading.value = true
    const params = { page, limit: size, ordering: "create_datetime" };
    const res = await api.GetList(params);
    if (res.code === 2000) {
        listItems.value = res.data;
        total.value = res.total;
    } else {
        ElMessage.error("获取列表失败");
    }
    loading.value = false
};
// 分页改变
const onHandleSizeChange = (val: number) => {
    pageSize.value = val;
    getList(currentPage.value, pageSize.value)
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    currentPage.value = val;
    getList(currentPage.value, pageSize.value)
};
const handleAdd = () => {
    addPopupRef.value.init("add");
}
const editItem = (item: object) => {
    addPopupRef.value.init("edit", item);
}
const handleSaveSuccess = () => {
    getList(currentPage.value, pageSize.value)
}
const delsaCaseList =  (item: any) => {
    ElMessageBox.confirm(
        '确定要删除吗?',
        'Warning',
        {
            confirmButtonText: '删除',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
        .then(async() => {
            const res = await api.DelObj(item.id,)
            if (res.code === 2000) {
                ElMessage.success("删除成功!");
                // 假设 item 是一个包含 id 属性的对象，并且列表项也有 id 属性
                listItems.value = listItems.value.filter((i: { id: any }) => i.id !== item.id);
                if (listItems.value.length === 0 && currentPage.value > 1) {
                    currentPage.value -= 1;
                }
                getList(currentPage.value, pageSize.value)
            } else {
                ElMessage.error("删除失败");
            }
        })
        .catch(() => {

        })
}
// 页面加载时
onMounted(() => {
    const elementHeight = window.innerHeight - 188;
    pageSize.value = elementHeight ? Math.floor(elementHeight / 30) : 25
    getList(currentPage.value, pageSize.value)
});
</script>

<style scoped lang="scss">
.FaultSetContent {
    padding: 10px;
    width: 100%;
    height: 100%;

    .tableContent {
        width: 100%;
        padding: 10px 0;
        height: calc(100% - 64px);
    }
}
</style>
./api