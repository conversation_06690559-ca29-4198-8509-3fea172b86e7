import { request } from '/@/utils/service';

export const urlPrefix = '/api/pm/sa_case/';

// 定义通用的请求参数类型，可根据实际情况修改
type RequestQuery = Record<string, any>;

// 定义 request 函数返回的 Promise 类型，可根据实际情况修改
type RequestResponse = Promise<any>;

export function GetList(query: RequestQuery): RequestResponse {
  return request({
    url: urlPrefix,
    method: 'get',
    params: query
  });
}

export function createObj(obj: any): RequestResponse {
  return request({
    url: urlPrefix,
    method: 'post',
    data: obj
  });
}

export function UpdateObj(obj: any): RequestResponse {
  return request({
    url: urlPrefix + obj.mRID + '/',
    method: 'put',
    data: obj
  });
}

export function DelObj(id: string | number): RequestResponse {
  return request({
    url: urlPrefix + id + '/',
    method: 'delete',
    data: { id }
  });
}