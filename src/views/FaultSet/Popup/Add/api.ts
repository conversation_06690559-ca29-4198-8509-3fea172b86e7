import { request } from '/@/utils/service';

// 静态安全分析结果查看-故障集列表
export function get_fault_sets(query: any): Promise<any> {
  return request({
    url: "/api/pm/sa_case/get_fault_sets",
    method: "get",
    params: query,
  });
}

// 静态安全分析概要信息统计
export function get_sa_static(query: any): Promise<any> {
  return request({
    url: "/api/pm/sa_case/get_sa_static",
    method: "get",
    params: query,
  });
}
// 静态安全分析保存
export function post_saCase(obj: any): Promise<any> {
  return request({
    url: "/api/pm/sa_case/",
    method: "post",
    data: obj,
  });
}
// 故障集新增批量保存
export function post_bulk_create(obj: any): Promise<any> {
  return request({
    url: "/api/pm/fault_group/bulk_create/",
    method: "post",
    data: obj,
  });
}

// 计算
export function post_sa_dispatch(obj: any): Promise<any> {
  return request({
    url: "/api/pm/pf/run_sa_asynch/",
    method: "post",
    data: obj,
  });
}

// id获取模型包详情
export function get_bbCase_id(id: number): Promise<any> {
  return request({
    url: `/api/pm/bb_case/${id}/`,
    method: "get",
  });
}

// 查询自定义故障集
export function get_bulk_query(params: string): Promise<any> {
  return request({
    url: `/api/pm/fault_group/bulk_query?${params}`,
    method: "get",
  });
}

// 保存
export function put_update_SaCase_info(data: any): Promise<any> {
  return request({
    url: `/api/pm/sa_case/update_SaCase_info`,
    method: "put",
    data,
  });
}

// 全网n-1切除保存
export function n1ExcisionSave(data: any): Promise<any> {
  return request({
    url: '/api/pm/sa_cutoff/n1_excision',
    method: 'post',
    data: data
  })
}

// 全网n-2切除保存
export function n2ExcisionSave(data: { sa_case_id?: any }): Promise<any> {
  return request({
    url:'/api/pm/sa_cutoff/n2_excision',
    method: 'post',
    data: data
  })
}
