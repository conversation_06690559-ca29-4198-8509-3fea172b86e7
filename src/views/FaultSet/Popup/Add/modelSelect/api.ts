import { request } from '/@/utils/service';

// 定义查询参数的类型，这里假设为任意对象，实际可按需修改
type QueryParams = Record<string, any>;

// 查询模型包日期列表
export function get_query_dates (query: QueryParams): Promise<any> {
  return request({
    url: '/api/pm/bb_case/query_dates/' ,
    method: 'get',
    params: query
  });
}

// 查询指定日期的 96 段模型包数据
export function post_query_model_date_info(obj: any): Promise<any> {
  return request({
    url: '/api/pm/bb_case/query_model_date_info/',
    method: 'post',
    data: obj
  });
}