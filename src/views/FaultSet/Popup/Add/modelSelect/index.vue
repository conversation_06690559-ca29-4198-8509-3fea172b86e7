<template>
  <el-dialog v-model="localVisible" :show-close="false" width="70%" append-to-body>
    <template #title>
      <div style="
          background: #4095e5;
          text-align: center;
          color: #fff;
          padding: 10px 0px;
        ">
        模型包选择
      </div>
    </template>

    <!-- 使用 el-row 和 el-col 创建两列布局 -->
    <el-row :gutter="20" style="padding: 10px; height: 420px">
      <!-- 左侧：日期选择部分 -->
      <el-col :span="9" style="height: 100%">
        <div class="left-panel" style="
            border: 1px solid #d9d9d9;
            height: 100%;
            display: flex;
            flex-direction: column;
          ">
          <div style="
              border-bottom: 1px solid #d9d9d9;
              display: flex;
              align-items: center;
              padding: 10px;
            ">
            <el-input placeholder="请输入搜索日期" v-model="searchDate" clearable
              style="margin-bottom: 10px; flex: 1"></el-input>
            <i class="el-icon-search" style="margin-left: 5px"></i>
            <!-- 放大镜图标 -->
          </div>
          <div style="overflow: auto; flex: 1">
            <el-table ref="dateTable" :data="
                dateTableData.filter(
                  (data) => !searchDate || data.date.includes(searchDate)
                )
              " style="width: 100%" @current-change="handleRowClick" height="335" highlight-current-row>
              <!-- <el-table-column type="selection" width="55"></el-table-column> -->
              <!-- 多选列 -->
              <el-table-column prop="date" label="日期"></el-table-column>
            </el-table>
          </div>
        </div>
      </el-col>

      <!-- 右侧：模型包选择部分 -->
      <el-col :span="15" style="height: 100%">
        <div class="right-panel" style="
            border: 1px solid #d9d9d9;
            height: 100%;
            display: flex;
            flex-direction: column;
          ">
          <div style="border-bottom: 1px solid #d9d9d9; padding: 10px">
            <el-input placeholder="请输入搜索模型包名称" v-model="searchName" clearable style="margin-bottom: 10px"></el-input>
          </div>
          <div style="flex: 1;">
            <el-table ref="modelPackageTable" :data="
                tableData.filter(
                  (data) => !searchName || data.case_name.includes(searchName)
                )
              " @current-change="handleSelectionChange" highlight-current-row min-width="628px" height="335">
              <!-- <el-table-column fixed type="selection" width="55"></el-table-column> -->
              <!-- 多选列 -->
              <el-table-column fixed prop="case_name" label="模型包名称" min-width="240"></el-table-column>
              <el-table-column prop="bg_sgen_p_mw" label="电源有功总加" width="180"></el-table-column>
              <el-table-column prop="bg_load_p_mw" label="负荷有功总加" width="180"></el-table-column>
              <el-table-column prop="bg_sgen_q_mvar" label="电源无功总加" width="180"></el-table-column>
              <el-table-column prop="bg_load_p_mvar" label="负荷无功总加" width="180"></el-table-column>
            </el-table>
          </div>
        </div>
      </el-col>
    </el-row>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="confirmSelection">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, defineProps, defineEmits } from 'vue';
import * as api from "./api";
import {ElMessage} from "element-plus";

// 定义 props 类型
const props = defineProps<{
  dialogVisible: boolean;
}>();

// 定义 emits 类型
const emit = defineEmits<{
  (e: 'update:dialogVisible', value: boolean): void;
  (e: 'onConfirm', selectedRow: any): void;
}>();

// 用本地变量接收初始值
const localVisible = ref(props.dialogVisible);
const searchDate = ref('');
const searchName = ref('');
const selectedRows = ref<{ [key: string]: any }>({});
const tableData = ref<any[]>([]);
const dateTableData = ref<any[]>([]);

// 定义表格引用
const dateTable = ref<any>(null);
const modelPackageTable = ref<any>(null);

// 监听父组件传递的 dialogVisible 变化，同步到本地
watch(() => props.dialogVisible, (newVal) => {
  localVisible.value = newVal;
});

// 监听本地变量变化，触发父组件更新
watch(localVisible, (newVal) => {
  emit('update:dialogVisible', newVal);
});

// 定义异步方法
const post_query_model_date_info = async (date: string) => {
  try {
    const response = await api.post_query_model_date_info({ date });
    if (response.code === 2000) {
      tableData.value = response.data;
    } else {
      // console.error("错误信息:", response.msg);
    }
  } catch (error) {
    // console.error("客户端错误:", error);
  }
};

const getData = async () => {
  try {
    const response = await api.get_query_dates();
    if (response.code === 2000) {
      dateTableData.value = response.data.map((dateStr: string) => ({
        date: dateStr,
      }));
    } else {
      // console.error("错误信息:", response.msg);
    }
  } catch (error) {
    // console.error("客户端错误:", error);
  }
};

const handleRowClick = (row: any) => {
  if (dateTable.value) {
    dateTable.value.toggleRowSelection(row); // 切换当前行的选择状态
  }
  post_query_model_date_info(row.date); // 根据选中的日期获取相应的模型包数据
};

const handleSelectionChange = (val: any) => {
  selectedRows.value = val; // 更新选中的行列表
};

const handleCancel = () => {
  localVisible.value = false;
};

const confirmSelection = () => {
  // 触发事件并传递选中的行数据
  if (selectedRows.value && selectedRows.value.model_id) {
    emit('onConfirm', selectedRows.value);
     localVisible.value = false;
  } else {
    ElMessage.warning('请选择一个模型包');
  }
};

// 当组件挂载时获取日期数据
onMounted(() => {
  getData();
});
</script>

<style scoped lang="scss">
.left-panel,
.right-panel {
  height: 300px;
  overflow-y: auto;
}
/* 新增样式：取消 el-dialog 的内边距 */
::v-deep .el-dialog__body {
  padding: 0 !important;
}
/* 新增：移除标题区域的内边距 */
::v-deep .el-dialog__header {
  padding: 0 !important; /* 移除默认的 padding */
}
</style>
