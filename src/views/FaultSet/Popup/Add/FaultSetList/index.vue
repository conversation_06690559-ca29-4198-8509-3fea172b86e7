<template>
  <div class="fault-group-table">
     <div class="button-group" style="display: flex;align-items: end;">
      <teg content="故障组"></teg>
    </div>
    <!-- 按钮组 -->
    <div class="button-group">
      <el-button @click="addFaultGroup" color="#22cf7b">添加故障组</el-button>
      <el-button @click="deleteFaultGroup" color="#ff4040">删除故障组</el-button>
      <el-button @click="customizeFaultGroup" color="#2f78fb">自定义故障组</el-button>
      <el-button @click="scanRange" color="#ff952e">扫描范围</el-button>
      <el-button @click="parameterConfig" color="#159eff">参数配置</el-button>
    </div>
    <!-- 表格 -->
    <el-table :data="tableData" border :row-class-name="tableRowClassName" @selection-change="handleSelectionChange"
      height="200">
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column type="index" label="序号" width="80"></el-table-column>
      <el-table-column prop="fault_group_name" label="故障组名称"></el-table-column>
      <el-table-column prop="equip_name" label="故障组设备"></el-table-column>
    </el-table>

    <equip-selector-dialog ref="equipSelectorDialogRef" v-model:visible="showAddDialog" :loading="isLoading"
      @addSelectEquip="handleAddFaultSetFromAddDialog"></equip-selector-dialog>

    <!-- 使用自定义故障集选择组件 -->
    <fault-set-selector v-model:visible="showCustomDialog" :loading="isLoading" :id="props.bb_case_id || ''"
      @addSelectEquip="handleAddFaultSetFromCustomDialog"></fault-set-selector>

    <!-- 扫描范围弹窗 -->
    <scan-range-dialog v-if="isChildDialogVisible" v-model:visible="isChildDialogVisible" :bb_case_id="props.bb_case_id || ''"
      v-model:n1="parentN1" v-model:n2="parentN2"></scan-range-dialog>

    <parameter-config-dialog v-model:visible="parameterShow" v-model:v_min="v_min" v-model:v_max="v_max"
      v-model:overload-threshold="overloadThreshold"></parameter-config-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits, watch } from 'vue';
import equipSelectorDialog from "../equipSelectorSetNameDialog.vue";
import FaultSetSelector from "../faultSelectorDialog/index.vue";
import ScanRangeDialog from "./components/ScanRange/index.vue";
import ParameterConfigDialog from "./components/ParameterConfig/index.vue";
import { ElMessage } from "element-plus";
import teg from '/@/components/teg/teg/index.vue';
// 定义 props
const props = defineProps<{
  bb_case_id: string | null;
}>();

// 定义响应式数据
const v_min = ref<string>("0.9");
const v_max = ref<string>("1.1");
const overloadThreshold = ref<string>("100");
const parentN1 = ref<{ zone: any[]; voltagelevel: any[]; euqiptype: any[] }>({ zone: [], voltagelevel: [], euqiptype: [] });
const parentN2 = ref<{ zone: any[]; voltagelevel: any[]; euqiptype: any[] }>({ zone: [], voltagelevel: [], euqiptype: [] });
const isChildDialogVisible = ref<boolean>(false);
const parameterShow = ref<boolean>(false);
const showCustomDialog = ref<boolean>(false);
const showAddDialog = ref<boolean>(false);
const selectedRows = ref<any[]>([]);
const tableData = ref<any[]>([]);
const isLoading = ref<boolean>(false);
const equipSelectorDialogRef = ref<any>(null);

// 检查 bb_case_id 是否有效
const checkBbCaseId = () => {
  if (!props.bb_case_id) {
    ElMessage.warning("请先选择模型");
    return false;
  }
  return true;
};

const getAllData = () => {
  // 返回所有数据
  // 原代码存在属性名拼写错误，修正为正确的属性名 voltagelevel
  // 此处代码可能是冗余的，因为赋值两边属性名相同
  // parentN1.value["voltagelevel"] = parentN1.value.voltagelevel;
  // 若不需要此赋值操作，可直接删除该行代码
  // parentN2.value["voltagelevel"] = parentN2.value.voltagelevel;
  return {
    v_min: v_min.value,
    v_max: v_max.value,
    max_ll: overloadThreshold.value,
    tableData: tableData.value,
    cut_condition: {
      condition1: parentN1.value,
      condition2: parentN2.value,
    },
  };
};

const addFaultGroup = () => {
  if (!checkBbCaseId()) return;
  equipSelectorDialogRef.value?.init(props.bb_case_id, []);
};

const deleteFaultGroup = () => {
  if (selectedRows.value.length > 0) {
    const idsToDelete = selectedRows.value.map((row) => row.id);
    tableData.value = tableData.value.filter((item) => !idsToDelete.includes(item.id));
    // 假设这里有一个 table 引用，需要根据实际情况调整
    // tableRef.value?.clearSelection();
  } else {
    alert("请选择要删除的行");
  }
};

const customizeFaultGroup = () => {
  if (!checkBbCaseId()) return;
  showCustomDialog.value = true; // 打开自定义故障集选择对话框
  // console.log("自定义故障组");
};

const scanRange = () => {
  if (!checkBbCaseId()) return;
  isChildDialogVisible.value = true;
  // console.log("扫描范围");
};

const parameterConfig = () => {
  if (!checkBbCaseId()) return;
  parameterShow.value = true;
  // console.log("参数配置");
};

const tableRowClassName = ({ row, rowIndex }: { row: any; rowIndex: number }) => {
  return rowIndex % 2 === 0 ? "even-row" : "odd-row";
};

const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection;
};

const handleAddFaultSetFromCustomDialog = (res: any) => {
  // console.log(res, "返回");
  if (!res.length) {
    ElMessage.warning("请选择故障组设备");
    return;
  }
  res.forEach((item: any) => {
    const data = {
      ...item,
      selectList: item,
    };
    // console.log(data);
    tableData.value.push(data);
  });
  ElMessage.success("选择成功");
};

const handleAddFaultSetFromAddDialog = (res: any) => {
  if (!res.selectionList.length) {
    ElMessage.warning("请选择故障组设备");
    return;
  }

  const fault_group_name =
    res.selectionList.map((d: any) => d.equip_name).join(",") || "未选择设备";
  const equip_id =
    res.selectionList.map((d: any) => d.equip_id).join(",") || "未选择设备";

  const itemData = {
    fault_group_name: res.fault_group_name,
    equip_name: fault_group_name,
    equip_id: equip_id,
    selectList: res.selectionList,
  };
  tableData.value.push(itemData);
  ElMessage.success("添加成功");
};

const updateTableData = (newData: any[]) => {
  tableData.value = newData;
};
defineExpose({ updateTableData,getAllData });
</script>

<style scoped lang="scss">
.fault-group-table {
  width: 50%;
  border: 1px solid #ddd;
  padding: 20px;
  .button-group {
    margin-bottom: 10px;
    display: flex;
    justify-content: start;
    button{
      color: #fff;
    }
  }

  .el-table {
    width: 100%;
  }

  .even-row {
    background-color: #f9f9f9;
  }

  .odd-row {
    background-color: #fff;
  }
}
</style>
