<template>
  <el-dialog v-model="localVisible" width="50%" append-to-body title="参数配置">
    <div class="dialog-content">
      <teg content="越限阈值设置"></teg>
      <el-form ref="formRef" label-width="120px">
        <el-form-item label="电压幅值">
          <el-input v-model="localVoltageRange.min" placeholder="最小值"></el-input>
          <span style="margin: 0 10px">-</span>
          <el-input v-model="localVoltageRange.max" placeholder="最大值"></el-input>
        </el-form-item>
        <el-form-item label="过载阈值">
          <el-input v-model="localOverloadThreshold" placeholder="请输入阈值"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="confirmSelection">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import teg from "/@/components/teg/teg/index.vue";

// 定义 props 类型
const props = defineProps<{
  visible: boolean;
  v_min: string;
  v_max: string;
  overloadThreshold: string;
}>();

// 定义 emits 类型
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'update:v_min', value: string): void;
  (e: 'update:v_max', value: string): void;
  (e: 'update:overloadThreshold', value: string): void;
}>();

// 表单引用
const formRef = ref(null);

// 本地变量
const localVisible = ref(props.visible);
const localVoltageRange = ref<{ min: string; max: string }>({
  min: props.v_min,
  max: props.v_max,
});
const localOverloadThreshold = ref(props.overloadThreshold);

// 监听 props 变化
watch(() => props.v_min, (newVal) => {
  if (localVoltageRange.value.min !== newVal) {
    localVoltageRange.value.min = newVal;
  }
});

watch(() => props.v_max, (newVal) => {
  if (localVoltageRange.value.max !== newVal) {
    localVoltageRange.value.max = newVal;
  }
});

watch(() => props.overloadThreshold, (newVal) => {
  if (localOverloadThreshold.value !== newVal) {
    localOverloadThreshold.value = newVal;
  }
});

watch(() => props.visible, (newVal) => {
  localVisible.value = newVal;
});

// 监听本地变量变化并触发更新
watch(localVisible, (newVal) => {
  emit('update:visible', newVal);
});

// 方法定义
const handleCancel = () => {
  localVisible.value = false;
  syncWithParent();
};

const confirmSelection = () => {
  localVisible.value = false;
  emit('update:v_min', localVoltageRange.value.min);
  emit('update:v_max', localVoltageRange.value.max);
  emit('update:overloadThreshold', localOverloadThreshold.value);
};

const syncWithParent = () => {
  emit('update:v_min', props.v_min);
  emit('update:v_max', props.v_max);
  emit('update:overloadThreshold', props.overloadThreshold);
};
</script>

<style scoped lang="scss">
.dialog-content {
  padding: 20px;
}
.form-group {
  border: 1px solid #ddd;
  padding: 20px;
  margin-bottom: 20px;
}
</style>
