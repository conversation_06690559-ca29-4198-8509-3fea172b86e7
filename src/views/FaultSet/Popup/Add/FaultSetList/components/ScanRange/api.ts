import { request } from '/@/utils/service';

// 定义 request 函数返回的 Promise 类型，这里用 any 作为示例，实际应根据接口返回数据定义具体类型
type RequestResponse = Promise<any>;

// 电压等级列表获取
export function findBasevoltageBycaseid(query: Record<string, any>): RequestResponse {
  return request({
    url: "/api/pm/bb_case/find_basevoltage_bycaseid",
    method: "get",
    params: query,
  });
}

// 待选分区列表获取
export function findControlareaBycaseid(query: Record<string, any>): RequestResponse {
  return request({
    url: "/api/pm/bb_case/find_controlarea_bycaseid",
    method: "get",
    params: query,
  });
}