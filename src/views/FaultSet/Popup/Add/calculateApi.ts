import { request } from '/@/utils/service';

// 定义通用的请求参数和响应类型
type RequestParams = Record<string, any>;
type RequestData = Record<string, any>;
type RequestResponse = Promise<any>;

// 定义接口前缀
export const urlPrefix = '/api/pm/';

// 潮流计算列表
export function GetbbCaseList(query: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'bbCase/BbCase_without_net/',
    method: 'get',
    params: query
  });
}

// 潮流计算新增
export function createbbCaseList(obj: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'bbCase/',
    method: 'post',
    data: obj
  });
}

// 潮流计算删除
export function delbbCaseList(obj: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'bbCase/delete_bb/',
    method: 'delete',
    data: obj
  });
}

// 潮流计算修改
export function UpdatebbCase(obj: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'bbCase/update_BbCase_info/',
    method: 'put',
    data: obj
  });
}

// 获取方案名
export function getSchemeList(query: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'cimeModel/get_model_info/',
    method: 'get',
    params: query
  });
}

// 潮流计算模型数据检查
export function checkModelData(data: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'bbBus/load_flow/',
    method: 'post',
    data: data
  });
}

// 潮流作业计算
export function calculationFlow(query: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'bbCase/matlab/',
    method: 'get',
    params: query
  });
}

// 拓扑图潮流作业计算
export function calculationFlowCanvas(query: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'dispatcherFlow/cal_dispatcher_flow/',
    method: 'get',
    params: query
  });
}

// 调度员潮流界面线路停运，保存
export function saveDispatchFlow(data: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'dispatchFlow/save_dispatch_flow/',
    method: 'post',
    data: data
  });
}

// 静态安全分析计算列表
export function GetsaCaseList(query: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'saCase/',
    method: 'get',
    params: query
  });
}

// 静态安全分析计算新增
export function createsaCaseList(obj: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'saCase/',
    method: 'post',
    data: obj
  });
}

// 静态安全分析计算删除
export function delsaCaseList(obj: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'saCase/del_sa_case/',
    method: 'post',
    data: obj
  });
}

// 静态安全分析计算修改
export function updatasaCaseList(obj: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'saCase/update_SaCase_info/',
    method: 'put',
    data: obj
  });
}

// 交流线列表
export function getlineCutOffList(query: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'bbLine/line_cut_off/',
    method: 'get',
    params: query
  });
}

// 变压器二绕列表
export function gettrafoCutOffList(query: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'bbTrafo/trafo_cut_off/',
    method: 'get',
    params: query
  });
}

// 变压器三绕列表
export function gettrafo3wCutOffList(query: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'bbTrafo3W/trafo3w_cut_off/',
    method: 'get',
    params: query
  });
}

// 发电机列表
export function getsgenCutOffList(query: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'bbSgen/sgen_cut_off/',
    method: 'get',
    params: query
  });
}

// 负荷列表
export function getloadCutOffList(query: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'bbLoad/load_cut_off/',
    method: 'get',
    params: query
  });
}

// 保存切除方案
export function saveExcisionScheme(data: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'saCutoff/add_cutoff_list/',
    method: 'post',
    data: data
  });
}

// 指定切除方案计算
export function analysisComputing(data: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'saCase/net_ss_calc/',
    method: 'post',
    data: data
  });
}

// n-1方案计算
export function nSubtractOne(data: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'saCutoff/n_subtract_one/',
    method: 'post',
    data: data
  });
}

// 区域选择切除方案
export function areaExcision(data: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'saCutoff/area_excision/',
    method: 'post',
    data: data
  });
}

// 故障集切除方案
export function saveFaultSetExcision(data: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'saCutoff/faultSet_excision/',
    method: 'post',
    data: data
  });
}

// 获取方案号列表
export function getCutNameList(query: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'saCase/get_cut_name/',
    method: 'get',
    params: query
  });
}

// 获取静态安全分析作业下切除方案运行统计结果
export function getSaStatic(query: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'saCase/get_sa_static/',
    method: 'get',
    params: query
  });
}

// 新增方案名
export function createCutName(obj: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'saCutoff/add_cut_name/',
    method: 'post',
    data: obj
  });
}

// 删除方案名
export function delCutName(data: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'saCutoff/delete_cut_name/',
    method: 'delete',
    data: data
  });
}

// 区域列表
export function cimeControlarea(query: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'cimeControlarea/',
    method: 'get',
    params: query
  });
}

// 获取方案号下数据
export function getCutoffList(query: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'saCutoff/get_cutoff/',
    method: 'get',
    params: query
  });
}

// 区域保存
export function addControlareaInfo(obj: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'saCutoffControlarea/add_controlarea_info/',
    method: 'post',
    data: obj
  });
}

// 区域已保存查询
export function saCutoffControlarea(query: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'saCutoffControlarea/',
    method: 'get',
    params: query
  });
}

// 电压等级列表
export function cimeBasevoltage(query: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'cimeBasevoltage/',
    method: 'get',
    params: query
  });
}

// 电压等级保存
export function addBasevoltageInfo(obj: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'saCutoffBasevoltage/add_basevoltage_info/',
    method: 'post',
    data: obj
  });
}

// 电压等级已保存列表查询
export function saCutoffBasevoltage(query: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'saCutoffBasevoltage/',
    method: 'get',
    params: query
  });
}

// 电压选择计算
export function voltageExcision(data: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'saCutoff/voltage_excision/',
    method: 'post',
    data: data
  });
}

// 区域加电压计算
export function areaVoltageExcision(data: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'saCutoff/area_voltage_excision/',
    method: 'post',
    data: data
  });
}

// 切除方案数据删除
export function deleteCutoff(data: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'saCutoff/delete_cutoff/',
    method: 'delete',
    data: data
  });
}

// 复制潮流作业
export function copy_bb(data: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'bbCase/copy_bb/',
    method: 'post',
    data: data
  });
}

// 查询电压等级
export function getBasevoltage(query: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'bb_case/find_basevoltage_bycaseid',
    method: 'get',
    params: query
  });
}

// 查询所属区域
export function getCimeControl(query: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'bb_case/find_controlarea_bycaseid',
    method: 'get',
    params: query
  });
}

// 获取所属厂站
export function getCimeSubstation(query: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'bb_case/find_substation_bycaseid',
    method: 'get',
    params: query
  });
}

// 获取拓扑结构数据数据
export function getGraphPlot(query: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'bbSection/bbSection_network/',
    method: 'get',
    params: query
  });
}

// 静态安全分析计算新 调用异步任务用/api/tasks/sa_dispatch 同步计算测试用urlPrefix + saCase/cal_sacase/
export function saCaseCalculate(data: RequestData): RequestResponse {
  return request({
    url: '/api/tasks/sa_dispatch',
    method: 'post',
    data: data
  });
}

// 静态安全分析计算新 调用异步任务用/api/tasks/sa_dispatch 同步计算测试用urlPrefix + saCase/cal_sacase/
export function parseModelByAsynctask(data: RequestData): RequestResponse {
  return request({
    url: '/api/tasks/parse_dispatch',
    method: 'post',
    data: data
  });
}

// 拓扑结构筛选
export function GetbbSectionList(query: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'bbSection/bbSection_without_graph/',
    method: 'get',
    params: query
  });
}

// 获取静态安全分析作业电压等级列表
export function GetSaCutoffBasevoltage(query: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'saCutoffBasevoltage/SaCutoffBasevoltage_info/',
    method: 'get',
    params: query
  });
}

// 获取静态安全分析作业区域列表
export function GetSaCutoffControlarea(query: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'saCutoffControlarea/SaCutoffControlarea_info/',
    method: 'get',
    params: query
  });
}

// 静态安全分析电压等级保存
export function SaveSaCutoffBasevoltage(data: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'saCutoffBasevoltage/add_basevoltage_info/',
    method: 'post',
    data: data
  });
}

// 静态安全分析分区保存
export function SaveSaCutoffControlarea(data: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'saCutoffControlarea/add_controlarea_info/',
    method: 'post',
    data: data
  });
}

// 静态安全分析复制保存
export function CopySaCase(data: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'saCase/copy_SaCase_info/',
    method: 'post',
    data: data
  });
}

// 全网n-1切除保存
export function n1ExcisionSave(data: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'sa_cutoff/n1_excision',
    method: 'post',
    data: data
  });
}

// 全网n-2切除保存
export function n2ExcisionSave(data: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'sa_cutoff/n2_excision',
    method: 'post',
    data: data
  });
}

// 合环电流计算
export function loopCalculation(data: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'lccc/cal_net_lccc/',
    method: 'post',
    data: data
  });
}

// 合环电流列表
export function loopCalculationList(query: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'lccc/lccc_result_list/',
    method: 'get',
    params: query
  });
}

// 设备修改记录
export function getDispatchFlow(data: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'dispatchFlow/',
    method: 'get',
    params: data
  });
}

// 保存运行方式
export function saveOperationMode(data: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'dispatchFlow/save_operation_mode/',
    method: 'post',
    params: data
  });
}

// 删除运行方式
export function deleteOperationMode(data: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'dispatchFlow/delete_operation_mode/',
    method: 'post',
    data: data
  });
}

export function calCutname(data: RequestData): RequestResponse {
  return request({
    url: urlPrefix + 'saCase/cal_cutname/',
    method: 'post',
    data: data
  });
}

export function getCutoffTranradio(data: RequestParams): RequestResponse {
  return request({
    url: urlPrefix + 'saCase/get_cutoff_tranradio/',
    method: 'get',
    params: data
  });
}

// 批量保存故障集
export function postBulkCreate(data: RequestData): RequestResponse {
  return request({
    url: '/api/poweranalysis/faultSet/bulk_create/',
    method: 'post',
    data
  });
}