<template>
  <el-form :model="form">
    <el-dialog v-model="dialogVisible" :close-on-click-modal="false" :append-to-body="true" :fullscreen="true"
      :before-close="beforeShut" :show-close="false" v-loading="loading" body-class="FaultSetDialogBodyClass">
      <template #title>
        <div style="
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
          ">
          <div style="display: flex; align-items: center">
            <!-- 故障集名称输入框 -->
            <el-form-item prop="case_name">
              <div style="display: flex; margin-right: 10px; align-items: center">
                <div style="flex: 1 0 auto; margin-right: 5px">故障集名称</div>
                <el-input v-model="form.case_name" placeholder="请输入故障集名称"></el-input>
              </div>
            </el-form-item>

            <!-- 单选按钮组 -->
            <el-form-item prop="cutoff_type">
              <el-radio-group v-model="form.cutoff_type" style="flex: 3; display: flex">
                <el-radio-button :label="0">n-1</el-radio-button>
                <el-radio-button :label="7">n-2</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </div>

          <!-- 模型选择、计算和保存按钮 -->
          <div style="display: flex; align-items: center">
            <!-- <div @click="modelClick = true" class="clickable">
              {{ `模型：${form.model.case_name}` }}
            </div> -->
            <el-button type="success" @click="modelClick = true" class="clickable"> {{ `模型：${form.model.case_name || ''}`
              }}</el-button>
            <el-button type="primary" @click="handleCalculate">计算</el-button>
            <el-button type="primary" @click="handleSave">保存</el-button>
            <span class="close-icon" @click="dialogVisible = false">
              <!--<i class="el-icon-close"></i>-->
              <el-icon>
                <Close />
              </el-icon>
            </span>
          </div>
        </div>
      </template>
      <div>
        <div style="display: flex; gap: 10px; width: 100%; margin-top: 10px">
          <FaultSetList ref="faultSetListRef" style="flex: 1" :bb_case_id="String(form.model.model_id)"></FaultSetList>
          <FaultSetList2 ref="faultSetList2Ref" style="flex: 1" :selected-rows="selectedFaults"
            :type="String(form.cutoff_type)"></FaultSetList2>
        </div>
        <div style="margin: 20px 0px">
          <cards :dataFromParent="parentData"></cards>
        </div>
        <div>
          <AnalysisTabs ref="analysisTabsRef" :saCaseId="id"></AnalysisTabs>
        </div>
        <div>
          <OverLimitTabs></OverLimitTabs>
        </div>
      </div>
      <modelSelect @onConfirm="onConfirmModelSelect" v-model:dialog-visible="modelClick"></modelSelect>
    </el-dialog>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import FaultSetList from "./FaultSetList/index.vue";
import FaultSetList2 from "./FaultSetList2/index.vue";
import cards from "./cards/index.vue";
import AnalysisTabs from "./AnalysisTabs/index.vue";
import modelSelect from "./modelSelect/index.vue";
import * as api from "./api";
import { ElMessage } from 'element-plus';

// 定义表单数据类型
interface FormModel {
  case_name: string;
  cutoff_type: 0 | 7;
  model: {
    case_name: string;
    model_id: string | number;
  };
}

interface ParentDataItem {
  number: number | string | null;
  label: string;
}

// 响应式数据
const id = ref<string>('');
const type = ref<string>('add');
const selectedFaults = ref<any[]>([]);
const modelClick = ref<boolean>(false);
const loadingFirst = ref<boolean>(true);
const dialogVisible = ref<boolean>(false);
const loading = ref<boolean>(false);
let timer: ReturnType<typeof setInterval> | null = null;
const emit = defineEmits<{
  (e: 'save-success'): void;
}>();
const parentData = ref<ParentDataItem[]>([
  { number: null, label: "故障组总计" },
  { number: null, label: "当前状态" },
  { number: null, label: "切除方案总计" },
  { number: null, label: "计算完成" },
  { number: null, label: "计算异常/无法求解" },
  { number: null, label: "过载" },
  { number: null, label: "高电压" },
  { number: null, label: "低电压" },
]);
const form = reactive<FormModel>({
  case_name: "",
  cutoff_type: 0,
  model: {
    case_name: "点击选择模型",
    model_id: "",
  },
});

// 定义子组件引用
const faultSetListRef = ref<any>();
const faultSetList2Ref = ref<any>();
const analysisTabsRef = ref<any>();

// 方法定义
const get_bulk_query = async (id: string) => {
  let idList = id.split(",");
  let queryParam = idList.map((id) => `id=${id}`).join("&");
  queryParam = queryParam + "&is_sa_edit=1";
  let res = await api.get_bulk_query(queryParam);
  if (res.code === 2000) {
    faultSetListRef.value?.updateTableData(res.data);
  }
};

const get_bbCase_id = async (id: string | number) => {
  let res = await api.get_bbCase_id(typeof id === 'string' ? parseInt(id) : id);
  if (res.code === 2000) {
    form.model = res.data;
    form.model.model_id = res.data.id;
    form.model.case_name = res.data.name
  }
};

const get_sa_static = async () => {
  let res = await api.get_sa_static({ sa_case_id: id.value });
  if (res.code === 2000) {
    let current_status = "";
    if (!res.data.start_time && !res.data.end_time) {
      current_status = "待计算";
    } else if (res.data.start_time && !res.data.end_time) {
      current_status = "计算中";
    } else if (res.data.end_time) {
      current_status = "计算完成";
    }

    let calculation_complete = res.data.cutoff_total - res.data.undocal;

    parentData.value = [
      { number: res.data.fault_total, label: "故障组总计" },
      { number: current_status, label: "当前状态" },
      { number: res.data.cutoff_total, label: "切除方案总计" },
      { number: calculation_complete, label: "计算完成" },
      { number: res.data.flow_error, label: "计算异常/无法求解" },
      { number: res.data.over_load, label: "过载" },
      { number: res.data.high_voltage, label: "高电压" },
      { number: res.data.low_voltage, label: "低电压" },
    ];
  }
};

// 检查是否选择了模型
const checkModelSelected = () => {
  if (!form.model.model_id) {
    ElMessage.warning("请先选择模型");
    return false;
  }
  return true;
};

const post_sa_dispatch = async () => {
  const res = await api.post_sa_dispatch({
    sa_case_id: id.value,
  });
  if (res.code === 2000) {
    ElMessage({
      message: "开始计算",
      type: "success",
      duration: 3000,
    });
  }
};

const handleCalculate = async () => {
  // 先检查是否选择了模型
  if (!checkModelSelected()) return;
  if (type.value === "add") {
    await handleSave();
  }
  if (type.value === "edit") {
    await post_sa_dispatch();
    await get_sa_static();
  }
};

const post_saCase = async (formData: FormModel) => {
  if (type.value === "add") {
    const res = await api.post_saCase(formData);
    if (res.code === 2000) {
      ElMessage({
        message: res.msg || "操作成功",
        type: "success",
        duration: 3000,
      });
      if (form.cutoff_type === 0) {
        // 没有
        let n1res = await api.n1ExcisionSave({
          sa_case_id: res.data.id,
        });
      } else if (form.cutoff_type === 7) {
        // 没有
        let n2res = await api.n2ExcisionSave({
          sa_case_id: res.data.id,
        });
      }
      // 新增事件触发
      emit('save-success');
      type.value = "edit";
      edit(res.data);
      return;
    }
  }
  if (type.value === "edit") {
    // 没有
    const res = await api.put_update_SaCase_info(formData);
    if (res.code === 2000) {
      ElMessage({
        message: res.msg || "操作成功",
        type: "success",
        duration: 3000,
      });
      if (form.cutoff_type === 0) {
        let n1res = await api.n1ExcisionSave({
          sa_case_id: res.data.id,
        });
      } else if (form.cutoff_type === 7) {
        let n2res = await api.n2ExcisionSave({
          sa_case_id: res.data.id,
        });
      }
    }
  }
};

const post_bulk_create = async (data: any[]) => {
  const res = await api.post_bulk_create(data);
  if (res.code === 2000) {
    return res.data;
  }
};

const arrToString = (arr: any[]) => {
  if (!Array.isArray(arr) || arr.length === 0) return "";
  if (arr.length === 1) {
    return arr[0].id;
  } else {
    return arr.map((item) => item.id).join(",");
  }
};

const generateFaultId = (withIdList: string, postBulkCreateList: string) => {
  let withIdPart = withIdList ? withIdList.trim() : "";
  let postBulkPart = postBulkCreateList ? postBulkCreateList.trim() : "";

  if (!withIdPart && !postBulkPart) {
    return "";
  } else if (withIdPart && postBulkPart) {
    return `${withIdPart},${postBulkPart}`;
  } else {
    return withIdPart || postBulkPart;
  }
};

const grouping = (data: any[]) => {
  let withId: any[] = [];
  let withoutId: any[] = [];

  data.forEach((item) => {
    if (item.hasOwnProperty("id")) {
      withId.push(item);
    } else {
      withoutId.push(item);
    }
  });

  return {
    withId: withId,
    withoutId: withoutId,
  };
};

// 保存
const handleSave = async () => {
  // 先检查是否选择了模型
  if (!checkModelSelected()) return;

  let faultSetListRefData = faultSetListRef.value?.getAllData();
  let { withId, withoutId } = grouping(faultSetListRefData.tableData);
  let post_bulk_create_list = await post_bulk_create(withoutId);
  let withIdListStr = arrToString(withId);
  let post_bulk_create_list_str = arrToString(post_bulk_create_list);
  let fault_id = generateFaultId(
    withIdListStr,
    post_bulk_create_list_str
  );
// 首先需要扩展 formData 的类型定义，添加 id 属性
interface FormData {
  case_name: string;
  bb_case_id: string | number;
  cutoff_type: 0 | 7;
  max_ll: any;
  v_max: any;
  v_min: any;
  cut_condition: {
    condition1: any;
    condition2: any;
    specialfault: any[];
    fault_id: string;
  };
  fault_id: string;
  id: string; // 添加 id 属性
}

// 假设 formData 类型已经正确定义
let formData: FormData = {
  case_name: form.case_name,
  bb_case_id: form.model.model_id,
  cutoff_type: form.cutoff_type,
  max_ll: faultSetListRefData.max_ll,
  v_max: faultSetListRefData.v_max,
  v_min: faultSetListRefData.v_min,
  cut_condition: {
    condition1: faultSetListRefData.cut_condition.condition1,
    condition2: faultSetListRefData.cut_condition.condition2,
    specialfault: selectedFaults.value,
    fault_id: fault_id,
  },
  fault_id: fault_id,
  id: "" // 初始化 id 属性
};

if (id.value) {
  formData.id = id.value;
}
// 合并 formData 和 form 中的 model 属性
const combinedFormData = {
  ...formData,
  model: form.model
};
await post_saCase(combinedFormData);
};
const beforeShut = () => {
  dialogVisible.value = false;
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
};

const init = (newType: string, data = {}) => {
  type.value = newType;
  (newType === 'add' ? add : edit)(data);
};

const add = (data: any = {}) => {
  // 设置为新增模式
  type.value = "add";
  // 重置表单数据为初始状态
  form.case_name = data.case_name || "";
  form.cutoff_type = data.cutoff_type || 0;
  form.model = {
    case_name: data.model?.case_name || "点击选择模型",
    model_id: data.model?.model_id || "",
  };

  // 清空其他关联数据
  id.value = "";
  selectedFaults.value = [];
  parentData.value = parentData.value.map((item) => ({
    ...item,
    number: null,
  }));

  // 清空子组件数据
  if (faultSetListRef.value) {
    faultSetListRef.value.updateTableData([]);
  }
  // 显示对话框
  dialogVisible.value = true;
};

const edit = (data: any) => {
  type.value = "edit";
  dialogVisible.value = true;
  form.case_name = data.case_name;
  form.cutoff_type = data.cutoff_type;
  selectedFaults.value = data.cut_condition && data.cut_condition.specialfault ? data.cut_condition.specialfault : [];
  id.value = data.id;
  get_bbCase_id(data.bb_case_id);
  get_bulk_query(data.fault_id);
  get_sa_static();
  nextTick(() => {
    faultSetList2Ref.value.setDefaultSelection(selectedFaults.value);
    analysisTabsRef.value.get_fault_sets();
  });
};

const onConfirmModelSelect = (res: any) => {
  form.model = res;
};
defineExpose({ init });
</script>

<style scoped lang="scss">
::v-deep .el-form-item {
  margin-bottom: 0px;
}
::v-deep .el-table {
  width: 100%;
  table-layout: auto;
}

::v-deep .el-table__body-wrapper {
  overflow-x: hidden;
}

::v-deep .el-dialog__body {
  padding: 30px 20px;
  padding-top: 0px;
}

.close-icon {
  cursor: pointer;
  margin-left: 10px;
  font-size: 18px;
}

.clickable {
  cursor: pointer;
  margin-right: 10px;
}

#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
}

.box-card {
  margin-right: 10px;
  display: flex;
}

.content {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.desc {
  font-size: 10px;
  text-align: center;
  flex: 1;
}

/* 自定义表格单元格内边距 */
::v-deep .el-table td,
::v-deep .el-table th.is-leaf {
  padding: 5px 0px;
  font-size: 12px;
}
</style>
<style>
.FaultSetDialogBodyClass{
  max-height: inherit !important;
}
</style>
