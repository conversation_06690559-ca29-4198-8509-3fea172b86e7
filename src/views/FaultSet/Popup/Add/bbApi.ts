import { request } from '/@/utils/service';

// 定义请求参数的通用类型，可根据实际情况调整
type RequestQuery = Record<string, any>;

// 定义 url 前缀
export const urlPrefix = '/api/pm/';
// 母线
export function GetbbBusList(query: RequestQuery): Promise<any> {
  return request({
    url: urlPrefix + 'bb_bus/',
    method: 'get',
    params: query
  });
}

// 线路
export function GetbbLineList(query: RequestQuery): Promise<any> {
  return request({
    url: urlPrefix + 'bb_line/',
    method: 'get',
    params: query
  });
}

// 负荷
export function GetbbLoadList(query: RequestQuery): Promise<any> {
  return request({
    url: urlPrefix + 'bb_load/',
    method: 'get',
    params: query
  });
}

// 发电机
export function GetbbSgenList(query: RequestQuery): Promise<any> {
  return request({
    url: urlPrefix + 'bb_sgen/',
    method: 'get',
    params: query
  });
}

// 并联容抗
export function GetbbShuntList(query: RequestQuery): Promise<any> {
  return request({
    url: urlPrefix + 'bbShunt/',
    method: 'get',
    params: query
  });
}

// 两绕变
export function GetbbTrafoList(query: RequestQuery): Promise<any> {
  return request({
    url: urlPrefix + 'bb_trafo/',
    method: 'get',
    params: query
  });
}

// 三绕变
export function GetbbTrafo3WList(query: RequestQuery): Promise<any> {
  return request({
    url: urlPrefix + 'bb_trafo3w/',
    method: 'get',
    params: query
  });
}

// 开关
export function GetbbSwitchList(query: RequestQuery): Promise<any> {
  return request({
    url: urlPrefix + 'bbSwitch/',
    method: 'get',
    params: query
  });
}

// 修改发电机类型
export function changeBbSgenType(query: RequestQuery): Promise<any> {
  return request({
    url: urlPrefix + 'bbSgen/bbSgen_type/',
    method: 'put',
    data: query
  });
}

// 修改有效性母线
export function bbBusUpdateService(data: RequestQuery): Promise<any> {
  return request({
    url: urlPrefix + 'bbBus/bus_update_service/',
    method: 'put',
    data: data
  });
}

// 修改有效性线路
export function bbLineUpdateService(data: RequestQuery): Promise<any> {
  return request({
    url: urlPrefix + 'bbLine/line_update_service/',
    method: 'put',
    data: data
  });
}

// 修改有效性负荷
export function bbLoadUpdateService(data: RequestQuery): Promise<any> {
  return request({
    url: urlPrefix + 'bbLoad/load_update_service/',
    method: 'put',
    data: data
  });
}

// 修改有效性发电机
export function bbSgenUpdateService(data: RequestQuery): Promise<any> {
  return request({
    url: urlPrefix + 'bbSgen/sgen_update_service/',
    method: 'put',
    data: data
  });
}

// 修改有效性并联容抗
export function bbShuntUpdateService(data: RequestQuery): Promise<any> {
  return request({
    url: urlPrefix + 'bbShunt/shunt_update_service/',
    method: 'put',
    data: data
  });
}

// 修改有效性两绕变
export function bbTrafoUpdateService(data: RequestQuery): Promise<any> {
  return request({
    url: urlPrefix + 'bbTrafo/trafo_update_service/',
    method: 'put',
    data: data
  });
}

// 修改有效性三绕变
export function bbTrafo3WUpdateService(data: RequestQuery): Promise<any> {
  return request({
    url: urlPrefix + 'bbTrafo3W/trafo3W_update_service/',
    method: 'put',
    data: data
  });
}

// 调度员潮流界面设备停运，保存
export function saveDispatchFlow(data: RequestQuery): Promise<any> {
  return request({
    url: urlPrefix + 'dispatchFlow/save_dispatch_flow/',
    method: 'post',
    data: data
  });
}

// 修改开关状态
export function bbSwitchUpdateService(data: RequestQuery): Promise<any> {
  return request({
    url: urlPrefix + 'bbSwitch/switch_update_service/',
    method: 'put',
    data: data
  });
}

// 调度员潮流计算结果查看
export function GetppResultBusDispatchList(query: RequestQuery): Promise<any> {
  return request({
    url: urlPrefix + 'dispatcherFlow/cal_result_forbus/',
    method: 'get',
    params: query
  });
}