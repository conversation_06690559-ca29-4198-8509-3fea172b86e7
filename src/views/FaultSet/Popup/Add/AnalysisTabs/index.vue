<template>
  <div class="fault-analysis">
    <el-tabs v-model="activeName" @tab-click="handleTabClick">
      <el-tab-pane label="故障集分析" name="faultSetAnalysis">
        <el-table
          @row-click="tableDataRowClick"
          border
          max-height="400px"
          :data="tableData"
          style="width: 100%"
        >
          <el-table-column
            type="index"
            label="序号"
            width="80"
          ></el-table-column>
          <el-table-column
            prop="fault_set_name"
            label="故障组名称"
          ></el-table-column>
          <el-table-column
            prop="fault_set_equip"
            label="故障组设备"
          ></el-table-column>
          <el-table-column
            prop="cutoff_total"
            label="切除方案数量"
          ></el-table-column>
          <el-table-column prop="convergence" label="计算收敛">
          </el-table-column>
          <el-table-column prop="over_load" label="过载"> </el-table-column>
          <el-table-column prop="high_voltage" label="高电压">
          </el-table-column>
          <el-table-column prop="low_voltage" label="低电压"> </el-table-column>
        </el-table>
        <div>
          <teg content="超限信息" style="margin: 40px 0 20px 0"></teg>
          <el-table
            border
            max-height="400px"
            :data="tableData1"
            style="width: 100%"
          >
            <el-table-column
              type="index"
              label="序号"
              width="80"
            ></el-table-column>
            <el-table-column
              prop="cut_equip_name"
              label="切除方案"
            ></el-table-column>
            <el-table-column
              prop="beyond_type"
              label="类型"
              width="200"
            ></el-table-column>
            <el-table-column
              prop="equip_name"
              label="越限设备名称"
            ></el-table-column>
            <el-table-column
              prop="equip_type"
              label="设备类型"
              width="150"
            ></el-table-column>
            <el-table-column
              prop="substation_name"
              label="所属厂站"
              width="150"
            ></el-table-column>
            <el-table-column
              prop="vm_pu"
              label="电压幅值"
              width="150"
            ></el-table-column>
            <el-table-column
              prop="vn_kv"
              label="基准电压"
              width="150"
            ></el-table-column>
            <el-table-column
              prop="v_max"
              label="电压幅值上限"
              width="150"
            ></el-table-column>
            <el-table-column
              prop="v_min"
              label="电压幅值下限"
              width="150"
            ></el-table-column>
            <el-table-column
              prop="max_ll"
              label="负载率上限"
              width="150"
            ></el-table-column>
            <el-table-column
              prop="loading_percent"
              label="负载率"
              width="150"
            ></el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
      <el-tab-pane label="设备分析" name="deviceAnalysis">
        <div>
          <el-tabs v-model="activeName1" @tab-click="handleTabClick1">
            <el-tab-pane label="主变" name="主变">
              <el-table
                @row-click="tableDataRowClick1"
                border
                max-height="400px"
                :data="tableData2"
                style="width: 100%"
              >
                <el-table-column
                  type="index"
                  label="序号"
                  width="80"
                ></el-table-column>
                <el-table-column
                  prop="equip_id"
                  label="设备id"
                ></el-table-column>
                <el-table-column
                  prop="equip_name"
                  label="线路名称"
                ></el-table-column>
                <el-table-column
                  prop="equip_type"
                  label="设备类型"
                ></el-table-column>
                <el-table-column
                  prop="substation_name"
                  label="变电站"
                ></el-table-column>
                <el-table-column
                  prop="hv_bus_name"
                  label="高压侧母线"
                ></el-table-column>
                <el-table-column
                  prop="lv_bus_name"
                  label="低压侧母线"
                ></el-table-column>
                <el-table-column
                  prop="total"
                  label="越限数量"
                ></el-table-column>
                <el-table-column
                  prop="loading_percent_max"
                  label="最高负载率"
                ></el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="母线" name="母线">
              <el-table
                @row-click="tableDataRowClick1"
                border
                max-height="400px"
                :data="tableData2"
                style="width: 100%"
              >
                <el-table-column
                  type="index"
                  label="序号"
                  width="80"
                ></el-table-column>
                <el-table-column
                  prop="equip_id"
                  label="设备id"
                ></el-table-column>
                <el-table-column
                  prop="equip_name"
                  label="线路名称"
                ></el-table-column>
                <el-table-column
                  prop="equip_type"
                  label="设备类型"
                ></el-table-column>
                <el-table-column
                  prop="substation_name"
                  label="变电站"
                ></el-table-column>
                <el-table-column
                  prop="total"
                  label="越限数量"
                ></el-table-column>
                <el-table-column
                  prop="vm_pu_max"
                  label="最高电压幅值"
                ></el-table-column>
                <el-table-column
                  prop="vm_pu_min"
                  label="最低电压幅值"
                ></el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="线路" name="线路">
              <el-table
                @row-click="tableDataRowClick1"
                border
                max-height="400px"
                :data="tableData2"
                style="width: 100%"
              >
                <el-table-column
                  type="index"
                  label="序号"
                  width="80"
                ></el-table-column>
                <el-table-column
                  prop="equip_id"
                  label="设备id"
                ></el-table-column>
                <el-table-column
                  prop="equip_name"
                  label="线路名称"
                ></el-table-column>
                <el-table-column
                  prop="equip_type"
                  label="设备类型"
                ></el-table-column>
                <el-table-column
                  prop="substation_name"
                  label="变电站"
                ></el-table-column>
                <el-table-column
                  prop="from_bus_name"
                  label="首端母线"
                ></el-table-column>
                <el-table-column
                  prop="to_bus_name"
                  label="末端母线"
                ></el-table-column>
                <el-table-column
                  prop="total"
                  label="越限数量"
                ></el-table-column>
                <el-table-column
                  prop="loading_percent_max"
                  label="最高负载率"
                ></el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </div>

        <div>
          <teg content="超限信息" style="margin: 40px 0 20px 0"></teg>
          <el-table
            border
            max-height="400px"
            :data="tableData3"
            style="width: 100%"
          >
            <el-table-column
              type="index"
              label="序号"
              width="80"
            ></el-table-column>
            <el-table-column
              prop="cut_equip_name"
              label="切除方案"
            ></el-table-column>
            <el-table-column
              prop="fault_group_name"
              label="故障集"
            ></el-table-column>
            <el-table-column
              prop="beyond_type"
              label="超限类型"
            ></el-table-column>
            <el-table-column prop="equip_name" label="超限设备名称">
            </el-table-column>
            <el-table-column prop="equip_type" label="超限设备类型">
            </el-table-column>
            <el-table-column prop="substation_name" label="所属厂站">
            </el-table-column>
            <el-table-column prop="beyond_vaule" label="值"> </el-table-column>
            <el-table-column prop="beyond_limt" label="极限"> </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import * as api from "./api";
import teg from "/@/components/teg/teg/index.vue";

// 定义 props 类型
const props = defineProps<{
  saCaseId: string
}>();

// 定义响应式数据
const activeName = ref<string>('faultSetAnalysis');
const activeName1 = ref<string>('主变');
const tableData = ref<any[]>([]);
const tableData1 = ref<any[]>([]);
const tableData2 = ref<any[]>([]);
const tableData3 = ref<any[]>([]);

// 验证 sa_case_id 是否存在且非空
const validateSaCaseId = (saCaseId: string): boolean => {
  if (!saCaseId || saCaseId.trim() === "") {
    // console.warn("sa_case_id 为空或不存在");
    return false;
  }
  return true;
};

// 将数组中 null 数据替换为 --
const preprocessData = (data: any[]): any[] => {
  return data.map((item) => {
    return Object.keys(item).reduce((acc: Record<string, any> = {}, key) => {
// 定义一个更明确的类型，这里使用 Record<string, any> 表示对象的键是字符串，值是任意类型
    acc[key] = item[key] === null || item[key] === undefined || item[key] === "" ? "--" : item[key];
      return acc;
    }, {});
  });
};

// 获取切除方案
const get_cutoff_by_equip = async (
  equip_name = "",
  equip_type = "",
  substation_name = ""
) => {
  const saCaseId = props.saCaseId;
  if (!validateSaCaseId(saCaseId)) return;

  let res = await api.get_cutoff_by_equip({
    sa_case_id: saCaseId,
    equip_name,
    equip_type,
    substation_name,
  });
  if (res.code === 2000) {
    tableData3.value = res.data;
    tableData3.value = preprocessData(tableData3.value);
  }
};

// 表格行点击事件
const tableDataRowClick1 = (row: any) => {
  get_cutoff_by_equip(
    row.equip_name,
    row.equip_type,
    row.substation_name
  );
};

// 标签页点击事件
const handleTabClick1 = (val: any) => {
  get_beyond_limit_equips_statics(activeName1.value);
};

// 获取越限设备统计信息
const get_beyond_limit_equips_statics = async (query_equip_type = "母线") => {
  const saCaseId = props.saCaseId;
  if (!validateSaCaseId(saCaseId)) return;

  let res = await api.get_beyond_limit_equips_statics({
    sa_case_id: saCaseId,
    query_equip_type,
  });
  if (res.code === 2000) {
    tableData2.value = res.data.data;
    tableData2.value = preprocessData(tableData2.value);
  }
};

// 获取越限设备信息
const get_beyond_limit_equips = async (fault_set_id: string) => {
  const saCaseId = props.saCaseId;
  if (!validateSaCaseId(saCaseId)) return;

  let res = await api.get_beyond_limit_equips({
    sa_case_id: saCaseId,
    fault_set_id,
  });
  if (res.code === 2000) {
    tableData1.value = res.data;
    tableData1.value = preprocessData(tableData1.value);
  }
};

// 故障集表格行点击事件
const tableDataRowClick = (row: any) => {
  get_beyond_limit_equips(row.fault_set_id);
};

// 获取故障集列表
const get_fault_sets = async () => {
  const saCaseId = props.saCaseId;
  if (!validateSaCaseId(saCaseId)) return;

  let res = await api.get_fault_sets({
    sa_case_id: saCaseId,
  });
  if (res.code === 2000) {
    tableData.value = res.data;
    tableData.value = preprocessData(tableData.value);
  }
};

// 标签页点击事件
const handleTabClick = (tab: any, event: any) => {
  if (activeName.value === "deviceAnalysis") {
    get_beyond_limit_equips_statics(activeName1.value);
  }
};

// 组件挂载时获取故障集列表
onMounted(() => {
  get_fault_sets();
});
defineExpose({ get_fault_sets });
</script>

<style scoped>
.fault-analysis {
  display: flex;
  flex-direction: column;
}

.el-tabs__nav-scroll {
  overflow: hidden;
}

.el-tabs__header.is-top {
  margin: 0;
}

.el-collapse-item__header {
  cursor: pointer;
}
</style>
