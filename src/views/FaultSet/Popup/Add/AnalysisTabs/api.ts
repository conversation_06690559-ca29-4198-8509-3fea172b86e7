import { request } from '/@/utils/service';

// 由于不清楚 query 的具体类型，暂时使用 any，实际开发建议定义具体类型
type QueryType = any; 

// 静态安全分析结果查看-故障集列表
export function get_fault_sets (query: QueryType): Promise<any> {
  return request({
    url: '/api/pm/sa_case/get_fault_sets' ,
    method: 'get',
    params: query
  });
}

// 静态安全分析结果查看-故障集下越限设备信息查看
export function get_beyond_limit_equips (query: QueryType): Promise<any> {
  return request({
    url: '/api/pm/sa_case/get_beyond_limit_equips' ,
    method: 'get',
    params: query
  });
}

// 静态安全分析结果查看-越限设备统计
export function get_beyond_limit_equips_statics (query: QueryType): Promise<any> {
  return request({
    url: '/api/pm/sa_case/get_beyond_limit_equips_statics' ,
    method: 'get',
    params: query
  });
}

// 静态安全分析结果查看-设备分析-越限信息
export function get_cutoff_by_equip (query: QueryType): Promise<any> {
  return request({
    url: '/api/pm/sa_case/get_cutoff_by_equip' ,
    method: 'get',
    params: query
  });
}