<template>
  <div class="over-limit-info">
    <teg content="超限信息"></teg>
    <el-collapse v-model="activeCollapse" accordion>
      <el-collapse-item title="点击查看超限信息表格" name="1">
        <el-table :data="tableData" style="width: 100%">
          <el-table-column prop="seqNo" label="序号" width="60"></el-table-column>
          <el-table-column
            prop="cutOffScheme"
            label="切除方案"
          ></el-table-column>
          <el-table-column prop="type" label="类型" width="100"></el-table-column>
          <el-table-column
            prop="deviceName"
            label="越限设备名称"
          ></el-table-column>
          <el-table-column
            prop="deviceType"
            label="设备类型"
            width="100"
          ></el-table-column>
          <el-table-column
            prop="station"
            label="所属厂站"
            width="150"
          ></el-table-column>
          <el-table-column
            prop="voltageAmplitude"
            label="电压幅值"
            width="100"
          ></el-table-column>
          <el-table-column
            prop="voltageLimit"
            label="电压极限"
            width="100"
          ></el-table-column>
          <el-table-column
            prop="loadRate"
            label="负载率"
            width="100"
          ></el-table-column>
          <el-table-column
            prop="loadLimit"
            label="负载极限"
            width="100"
          ></el-table-column>
        </el-table>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import teg from "/@/components/teg/teg/index.vue";

// 定义表格数据项的类型
interface TableDataItem {
  seqNo: number;
  cutOffScheme: string;
  type: string;
  deviceName: string;
  deviceType: string;
  station: string;
  voltageAmplitude: string;
  voltageLimit: string;
  loadRate: string;
  loadLimit: string;
}

// 响应式数据
const activeCollapse = ref<string>('1'); // 默认展开第一个折叠项
const tableData = ref<TableDataItem[]>([
  {
    seqNo: 1,
    cutOffScheme: "XX线、XX线、XX1号主变、YY线",
    type: "无法求解",
    deviceName: "",
    deviceType: "",
    station: "",
    voltageAmplitude: "",
    voltageLimit: "",
    loadRate: "",
    loadLimit: "",
  },
  {
    seqNo: 2,
    cutOffScheme: "XX线、XX线、XX1号主变、ZZ线",
    type: "高电压",
    deviceName: "XX母线",
    deviceType: "母线",
    station: "XX站",
    voltageAmplitude: "1.4",
    voltageLimit: "1.1",
    loadRate: "",
    loadLimit: "",
  },
  {
    seqNo: 3,
    cutOffScheme: "XX线、XX线、XX1号主变、ZZ线",
    type: "低电压",
    deviceName: "XX母线",
    deviceType: "母线",
    station: "XX站",
    voltageAmplitude: "0.8",
    voltageLimit: "0.9",
    loadRate: "",
    loadLimit: "",
  },
  {
    seqNo: 4,
    cutOffScheme: "XX线、XX线、XX1号主变、HH线",
    type: "过载",
    deviceName: "XX线路",
    deviceType: "线路",
    station: "XX站、XX站",
    voltageAmplitude: "",
    voltageLimit: "",
    loadRate: "95",
    loadLimit: "90",
  },
]);
</script>

<style scoped>
.over-limit-info {
  margin: 20px 0px;
}
</style>
