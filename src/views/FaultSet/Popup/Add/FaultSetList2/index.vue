<template>
  <div class="fault-group-table">
    <div class="button-group" style="display: flex;align-items: end;">
      <teg content="特殊故障组"></teg>
      <!-- <el-button style="visibility: hidden" >添加故障组</el-button> -->
    </div>
    <el-table
      border
      :data="tableData"
      @selection-change="handleSelectionChange"
      ref="faultTable"
      height="242"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column prop="name" label="特殊故障组">
        <template #default="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue';
import teg from '/@/components/teg/teg/index.vue';

// 定义 props 类型
const props = defineProps<{
  type: string;
  selectedRows: string[];
}>();

// 定义 emits 类型
const emit = defineEmits<{
  (event: 'update:selectedRows', selectedIds: string[]): void;
}>();

// 定义响应式数据
const tableData = ref<{ id: string; name: string }[]>([]);
const selectedFaults = ref<string[]>([]);
const faultTable = ref<any>(null);

// 定义更新表格数据的方法
const updateTableData = (cutoffType: string) => {
  const parsedType = parseInt(cutoffType);
  switch (parsedType) {
    case 0:
      tableData.value = [
        { id: "1", name: "变电站N-1(单一变电站全失)" },
      ];
      break;
    case 7:
      tableData.value = [
        { id: "2", name: "并列线路N-2(平行双回线路同时跳闸)" },
        { id: "3", name: "主变N-2(同一变电站任意两台主变同时跳闸)" },
      ];
      break;
    default:
      tableData.value = [];
      break;
  }
};

// 定义处理选择变化的方法
const handleSelectionChange = (selection: { id: string }[]) => {
  selectedFaults.value = selection.map(row => row.id);
  // console.log(selectedFaults.value, '变更');
  emit('update:selectedRows', selectedFaults.value);
};

// 定义设置默认选择的方法
const setDefaultSelection = (ids: string[]) => {
  if (faultTable.value) {
    const rowsToSelect = tableData.value.filter(faultSet =>
      ids.includes(faultSet.id)
    );
    rowsToSelect.forEach(row => {
      faultTable.value.toggleRowSelection(row, true);
    });
  }
};

// 监听 props 变化
watch(() => props.type, (newVal) => {
  updateTableData(newVal);
  setDefaultSelection(props.selectedRows);
});

watch(() => props.selectedRows, (newVal) => {
  setDefaultSelection(newVal);
});

// 组件挂载时更新表格数据
onMounted(() => {
  updateTableData(props.type);
});
defineExpose({ setDefaultSelection });
</script>

<style scoped>
/* 这里可以添加一些自定义样式 */
.fault-group-table {
  width: 50%;
  border: 1px solid #ddd;
    padding: 20px;
}
.button-group {
  margin-bottom: 10px;
  display: flex;
}
</style>
