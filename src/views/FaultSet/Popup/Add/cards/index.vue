<template>
  <div class="statistic-cards">
    <div v-for="(item, index) in statistics" :key="index" class="statistic-card">
      <img :src="item.icon" alt="" class="card-icon">
      <div class="card-content">
        <span class="card-number">{{ item.number }}</span>
        <span class="card-label">{{ item.label }}</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import statementImg1 from '/@/assets/img/statementImg/statementImg1.png';
import statementImg2 from '/@/assets/img/statementImg/statementImg2.png';
import statementImg3 from '/@/assets/img/statementImg/statementImg3.png';
import statementImg4 from '/@/assets/img/statementImg/statementImg4.png';
import statementImg5 from '/@/assets/img/statementImg/statementImg5.png';
import statementImg6 from '/@/assets/img/statementImg/statementImg6.png';
import statementImg7 from '/@/assets/img/statementImg/statementImg7.png';
import statementImg8 from '/@/assets/img/statementImg/statementImg8.png';

// 定义 props 类型
const props = defineProps<{
  dataFromParent: Array<{ number?: number | string | null }>;
}>();

// 定义数据类型
interface StatisticItem {
  icon: string;
  number: string;
  label: string;
}

// 初始化数据
const statistics = ref<StatisticItem[]>([
  { icon: statementImg1, number: '--', label: '故障组总计' },
  { icon: statementImg2, number: '--', label: '当前状态' },
  { icon: statementImg3, number: '--', label: '切除方案总计' },
  { icon: statementImg4, number: '--', label: '计算完成' },
  { icon: statementImg5, number: '--', label: '计算异常/无法求解' },
  { icon: statementImg6, number: '--', label: '过载' },
  { icon: statementImg7, number: '--', label: '高电压' },
  { icon: statementImg8, number: '--', label: '低电压' },
]);
// 定义方法
const updateStatistics = (data: Array<{ number?: number | string | null }>) => {
  if (!Array.isArray(data)) return;
  data.forEach((item, index) => {
    if (statistics.value[index]) {
      const value = item.hasOwnProperty('number') && item.number !== null && item.number !== undefined
        ? item.number.toString()
        : '--';
      statistics.value[index].number = value;
    }
  });
};
// 监听 props 变化
watch(
  () => props.dataFromParent,
  (newVal) => {
    updateStatistics(newVal);
  },
  {
    deep: true,
    immediate: true,
  }
);
</script>

<style scoped>
.statistic-cards {
  display: flex;
  gap: 10px; /* 卡片之间的间距 */
  flex-wrap: wrap; /* 启用自动换行 */
}

.statistic-card {
display: flex;
  align-items: center;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 12%;
  height: 100px;
  box-sizing: border-box;
  min-width: 200px;
}

.card-icon {
  width: 80px;
  height: 80px;
  margin-right: 10px;
}

.card-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-grow: 1;
}

.card-number {
  font-size: 16px;
  font-weight: bold;
  text-align: center;
}

.card-label {
  font-size: 12px;
  color: #666;
  text-align: center;
}
</style>
