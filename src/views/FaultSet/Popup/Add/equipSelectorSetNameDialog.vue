<template>
  <el-dialog v-model="dialogVisible" :close-on-click-modal="false" :append-to-body="true" :top="'2vh'"
    class="previewDialog" width="1050px" v-loading="loading" title="添加故障组">
    <div class="calculationModelDivClass" v-loading="loading">
      <div class="parameterBox">
        <div style="
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            margin-left: 10px;
          ">
          <div style="margin-right: 10px">故障组名称:</div>
          <el-input placeholder="请设置故障组名称" v-model="faultSetName" size="mini" style="width: 150px" clearable></el-input>
        </div>
        <div class="tools">
          <div class="toolFlex">
            <div class="toolFlexItem">
              <div class="name">名称:</div>
              <el-input placeholder="请输入名称" v-model="searchValue" size="mini" style="width: 100px" clearable></el-input>
            </div>
            <div class="toolFlexItem">
              <div class="name">电压等级:</div>
              <el-select v-model="scheme" multiple collapse-tags filterable placeholder="请选择" size="mini"
                style="width: 160px" clearable>
                <el-option v-for="item in powerList" :key="item.mRID" :label="item.name" :value="item.mRID">
                </el-option>
              </el-select>
            </div>
            <div class="toolFlexItem">
              <div class="name">区域:</div>
              <el-select v-model="partition" multiple collapse-tags filterable placeholder="请选择" size="mini"
                style="width: 155px" clearable>
                <el-option v-for="item in partitionList" :key="item.mRID" :label="item.name" :value="item.mRID">
                </el-option>
              </el-select>
            </div>
            <div class="toolFlexItem">
              <div class="name">厂站:</div>
              <el-select v-model="substation" multiple collapse-tags filterable placeholder="请选择" size="mini"
                style="width: 170px" clearable @visible-change="changeSubstation">
                <el-option v-for="item in substationList" :key="item.mRID" :label="item.name" :value="item.mRID">
                </el-option>
              </el-select>
            </div>
            <el-button class="calculate" type="primary" :icon="Search" size="mini" @click="searchTable">
              查询
            </el-button>
            <el-button class="calculate" :icon="Refresh" size="mini" @click="resetSearch">
              重置
            </el-button>
          </div>
        </div>
        <el-tabs v-model="activeName" @tab-change="handleClick">
          <el-tab-pane label="母线" name="bbBus">
            <div style="overflow-y: auto; box-sizing: border-box" v-if="activeName === 'bbBus'">
              <table-common :tableData="tableData" :height="tableHeight" :tableColumn="bbBusTableColumn"
                :selection="true" :selectionList="selectionSetList" @select="selectionChange"
                @selectAll="selectionChange"></table-common>
            </div>
          </el-tab-pane>
          <el-tab-pane label="线路" name="bbLine">
            <div style="overflow-y: auto; box-sizing: border-box" v-if="activeName === 'bbLine'">
              <table-common :tableData="tableData" :height="tableHeight" :tableColumn="bbLineTableColumn"
                :selection="true" :selectionList="selectionSetList" @select="selectionChange"
                @selectAll="selectionChange"></table-common>
            </div>
          </el-tab-pane>
          <el-tab-pane label="负荷" name="bbLoad">
            <div style="overflow-y: auto; box-sizing: border-box" v-if="activeName === 'bbLoad'">
              <table-common :tableData="tableData" :height="tableHeight" :tableColumn="bbLoadTableColumn"
                :selection="true" :selectionList="selectionSetList" @select="selectionChange"
                @selectAll="selectionChange"></table-common>
            </div>
          </el-tab-pane>
          <el-tab-pane label="发电机" name="bbSgen">
            <div style="overflow-y: auto; box-sizing: border-box" v-if="activeName === 'bbSgen'">
              <table-common :tableData="tableData" :height="tableHeight" :tableColumn="bbSgenTableColumn"
                :selection="true" :selectionList="selectionSetList" @select="selectionChange"
                @selectAll="selectionChange"></table-common>
            </div>
          </el-tab-pane>
          <el-tab-pane label="两绕变" name="bbTrafo">
            <div style="overflow-y: auto; box-sizing: border-box" v-if="activeName === 'bbTrafo'">
              <table-common :tableData="tableData" :height="tableHeight" :tableColumn="bbTrafoTableColumn"
                :selection="true" :selectionList="selectionSetList" @select="selectionChange"
                @selectAll="selectionChange"></table-common>
            </div>
          </el-tab-pane>
          <el-tab-pane label="三绕变" name="bbTrafo3W">
            <div style="overflow-y: auto; box-sizing: border-box" v-if="activeName === 'bbTrafo3W'">
              <table-common :tableData="tableData" :height="tableHeight" :tableColumn="bbTrafo3WTableColumn"
                :selection="true" :selectionList="selectionSetList" @select="selectionChange"
                @selectAll="selectionChange"></table-common>
            </div>
          </el-tab-pane>
        </el-tabs>
        <div class="tools" style="margin-top: 10px;">
          <div class="toolFlex">
            <div class="toolFlexItem">
              <el-pagination v-if="isPagination" background @size-change="handleSizeChange"
                @current-change="handleCurrentChange" :page-sizes="[30, 40, 60, 100]" :page-size="limit"
                layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
            </div>
            <div class="toolFlexItem">
              <el-button type="primary" size="mini" @click="addSelectEquip">
                确定
              </el-button>
              <el-button type="primary" size="mini" @click="dialogVisible = false">
                取消
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import * as calculateApi from "./calculateApi.ts";
import tableCommon from "/@/components/table-common/index.vue";
import * as api from "./bbApi.ts";
import { ElMessage } from "element-plus";
import { Search, Refresh } from '@element-plus/icons-vue'

// 定义响应式数据
const faultSetName = ref<string>('');
const loading = ref<boolean>(false);
const dialogVisible = ref<boolean>(false);
const activeName = ref<string>('bbBus');
const searchValue = ref<string>('');
const tableData = ref<any[]>([]);
const total = ref<number>(0);
const page = ref<number>(1);
const limit = ref<number>(30);
const isPagination = ref<boolean>(true);
const bbCaseId = ref<number>(0);
const isCheck = ref<boolean>(false);
const scheme = ref<string[]>([]);
const powerList = ref<any[]>([]);
const partition = ref<string[]>([]);
const partitionList = ref<any[]>([]);
const substation = ref<string[]>([]);
const substationList = ref<any[]>([]);
const selectionList = ref<any[]>([]);
const selectionSetList = ref<any[]>([]);
const tableHeight = ref<number>(350);
const emit = defineEmits<{
  (e: 'addSelectEquip', payload: { selectionList: any[]; fault_group_name: string }): void;
}>();
// 定义表格列配置
const bbBusTableColumn = [
  {
    label: "母线名称",
    prop: "bus_name",
    minWidth: "150",
    fixed: true,
  },
  {
    label: "所属区域",
    prop: "zone_name",
    minWidth: "100",
  },
  {
    label: "电压等级",
    prop: "basevoltage_name",
    minWidth: "100",
  },
  {
    label: "所属厂站",
    prop: "substation_name",
    minWidth: "100",
  },
];
const bbLineTableColumn = [
  {
    label: "线路名称",
    prop: "line_name",
    minWidth: "100",
    fixed: true,
  },
  {
    label: "首端母线",
    prop: "line_name",
    minWidth: "100",
  },
  {
    label: "首端厂站",
    prop: "from_substation_name",
    minWidth: "100",
  },
  {
    label: "末端母线",
    prop: "to_bus_name",
    minWidth: "100",
  },
  {
    label: "末端厂站",
    prop: "to_substation_name",
    minWidth: "100",
  },
  {
    label: "电阻(Ω)",
    prop: "r_ohm_per_km",
    minWidth: "100",
  },
  {
    label: "电抗(Ω)",
    prop: "x_ohm_per_km",
    minWidth: "100",
  },
  {
    label: "单端电纳(F)",
    prop: "c_nf_per_km",
    minWidth: "100",
  },
  {
    label: "电导/km",
    prop: "g_us_per_km",
    minWidth: "100",
  },
];
const bbLoadTableColumn = [
  {
    label: "负荷名称",
    prop: "load_name",
    minWidth: "150",
    fixed: true,
  },
  {
    label: "负荷所在母线",
    prop: "bus_name",
    minWidth: "180",
  },
  {
    label: "所属区域",
    prop: "zone_name",
    minWidth: "100",
  },
  {
    label: "电压等级",
    prop: "basevoltage_name",
    minWidth: "100",
  },
  {
    label: "所属厂站",
    prop: "substation_name",
    minWidth: "100",
  },
  {
    label: "有功功率(MW)",
    prop: "p_mw",
    minWidth: "120",
    slotName: "p_mw",
  },
  {
    label: "无功功率(Mvar)",
    prop: "q_mvar",
    minWidth: "120",
    slotName: "q_mvar",
  },
  {
    label: "容量(MVA)",
    prop: "sn_mva",
    minWidth: "100",
  },
  {
    label: "功率因数",
    prop: "scaling",
    minWidth: "100",
  },
];
const bbSgenTableColumn = [
  {
    label: "发电机名称",
    prop: "sgen_name",
    minWidth: "150",
    fixed: true,
  },
  {
    label: "发电机所在母线",
    prop: "bus_name",
    minWidth: "180",
  },
  {
    label: "所属厂站",
    prop: "substation_name",
    minWidth: "120",
  },
  {
    label: "有功功率(MW)",
    prop: "p_mw",
    minWidth: "150",
    slotName: "p_mw",
  },
  {
    label: "无功功率(Mvar)",
    prop: "q_mvar",
    minWidth: "150",
    slotName: "q_mvar",
  },
  {
    label: "机端电压(kV)",
    prop: "vm_pu",
    minWidth: "100",
  },
  {
    label: "额定电压(kV)",
    prop: "vn_kv",
    minWidth: "100",
  },
  {
    label: "容量(MVA)",
    prop: "sn_mva",
    minWidth: "100",
  },
  {
    label: "功率因数",
    prop: "scaling",
    minWidth: "100",
  },
  {
    label: "发电机节点类型",
    prop: "mode_ctrl",
    minWidth: "100",
    slotName: "mode_ctrl",
  },
];
const bbTrafoTableColumn = [
  {
    label: "主变名称",
    prop: "trafo_name",
    minWidth: "150",
    fixed: true,
  },
  {
    label: "所属厂站",
    prop: "substation_name",
    minWidth: "120",
  },
  {
    label: "高压侧所在母线",
    prop: "hv_bus_name",
    minWidth: "180",
  },
  {
    label: "低压侧所在母线",
    prop: "lv_bus_name",
    minWidth: "180",
  },
  {
    label: "容量(MVA)",
    prop: "sn_mva",
    minWidth: "90",
  },
  {
    label: "高压侧额定电压(kV)",
    prop: "vn_hv_kv",
    minWidth: "140",
  },
  {
    label: "低压侧额定电压(kV)",
    prop: "vn_lv_kv",
    minWidth: "140",
  },
  {
    label: "高-低短路电压(百分比)",
    prop: "vk_percent",
    minWidth: "150",
  },
  {
    label: "高-低短路电压实部(百分比)",
    prop: "vkr_percent",
    minWidth: "160",
  },
];
const bbTrafo3WTableColumn = [
  {
    label: "主变名称",
    prop: "trafo3w_name",
    minWidth: "150",
    fixed: true,
  },
  {
    label: "所属厂站",
    prop: "substation_name",
    minWidth: "120",
  },
  {
    label: "高压侧所在母线",
    prop: "hv_bus_name",
    minWidth: "140",
  },
  {
    label: "中压侧所在母线",
    prop: "mv_bus_name",
    minWidth: "140",
  },
  {
    label: "低压侧所在母线",
    prop: "lv_bus_name",
    minWidth: "140",
  },
  {
    label: "高压侧容量(MVA)",
    prop: "sn_hv_mva",
    minWidth: "125",
  },
  {
    label: "中压侧容量(MVA)",
    prop: "sn_mv_mva",
    minWidth: "125",
  },
  {
    label: "低压侧容量(MVA)",
    prop: "sn_lv_mva",
    minWidth: "125",
  },
  {
    label: "高压侧额定电压(kV)",
    prop: "vn_hv_kv",
    minWidth: "135",
  },
  {
    label: "中压侧额定电压(kV)",
    prop: "vn_mv_kv",
    minWidth: "135",
  },
  {
    label: "低压侧额定电压(kV)",
    prop: "vn_lv_kv",
    minWidth: "135",
  },
  {
    label: "高-中短路电压(百分比)",
    prop: "vk_hv_percent",
    minWidth: "150",
  },
  {
    label: "中-低短路电压(百分比)",
    prop: "vk_mv_percent",
    minWidth: "150",
  },
  {
    label: "高-低短路电压(百分比)",
    prop: "vk_lv_percent",
    minWidth: "150",
  },
  {
    label: "高-中短路电压实部(百分比)",
    prop: "vkr_hv_percent",
    minWidth: "175",
  },
  {
    label: "中-低短路电压实部(百分比)",
    prop: "vkr_mv_percent",
    minWidth: "175",
  },
  {
    label: "高-低短路电压实部(百分比)",
    prop: "vkr_lv_percent",
    minWidth: "175",
  },
];

// 初始化方法
const init = (id: number, equipList?: any[]) => {
  if (equipList === undefined) {
    selectionList.value = [];
  } else {
    selectionList.value = equipList;
  }
  dialogVisible.value = true;
  bbCaseId.value = id;
  getPowerList();
  getPartitionList();
  getSubstation();
  getList();
};

// 查询条件电压等级下拉列表获取电压等级列表
const getPowerList = async () => {
  loading.value = true;
  const query = {
    bb_case_id: bbCaseId.value,
  };
  try {
    const res = await calculateApi.getBasevoltage(query);
    powerList.value = res.data;
  } catch (error) {
    // console.error('获取电压等级列表失败', error);
  } finally {
    loading.value = false;
  }
};

// 查询条件所属区域下拉列表获取所属区域
const getPartitionList = async () => {
  loading.value = true;
  const query = {
    bb_case_id: bbCaseId.value,
  };
  try {
    const res = await calculateApi.getCimeControl(query);
    partitionList.value = res.data;
  } catch (error) {
    // console.error('获取所属区域列表失败', error);
  } finally {
    loading.value = false;
  }
};

// 查询条件所属厂站下拉列表获取所属厂站
const getSubstation = async () => {
  loading.value = true;
  const query = {
    bb_case_id: bbCaseId.value,
    cime_basevoltage_id: scheme.value.toString(),
    cime_controlarea_id: partition.value.toString(),
  };
  try {
    const res = await calculateApi.getCimeSubstation(query);
    substationList.value = res.data;
  } catch (error) {
    // console.error('获取所属厂站列表失败', error);
  } finally {
    loading.value = false;
  }
};

// 点击下拉前根据已选择的电压等级和分区重新查询厂站下拉选项，实现级联效果
const changeSubstation = (flag: boolean) => {
  if (flag) {
    getSubstation();
  }
};

// 设备Tab页被点击切换时调用，查询设备列表
const handleClick = (val: string) => {
  isPagination.value = false;
  page.value = 1;
  limit.value = 30;
  getList();
  setTimeout(() => {
    isPagination.value = true;
  }, 200);
};

// 点击查询按钮时列表检索
const searchTable = () => {
  if (isCheck.value) {
    ElMessage.confirm("已选择设备,是否保存?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(() => {
      // 确定操作
    }).catch(() => {
      isCheck.value = false;
      page.value = 1;
      getList();
    });
  } else {
    page.value = 1;
    getList();
  }
};

// 重置查询条件
const resetSearch = () => {
  searchValue.value = "";
  scheme.value = [];
  partition.value = [];
  substation.value = [];
  isCheck.value = false;
  getList();
};

// 确定添加设备
const addSelectEquip = () => {
  if (!faultSetName.value) {
    ElMessage.warning("请输入新添的故障组名称");
    return;
  }
  emit('addSelectEquip', {
    selectionList: selectionList.value,
    fault_group_name: faultSetName.value,
  });
  dialogVisible.value = false;
};

// 分页每页记录数变更时调用
const handleSizeChange = (val: number) => {
  page.value = 1;
  limit.value = val;
  getList();
};

// 翻页时调用
const handleCurrentChange = (val: number) => {
  page.value = val;
  getList();
};

// 设备列表的复选框变化（勾选或取消勾选）时调用
const selectionChange = (selection: any[], row: any) => {
  const selected = selection.length && selection.indexOf(row) !== -1;
  const trueToOne = selected ? 1 : 0;
  if (trueToOne === 1) {
    switch (activeName.value) {
      case 'bbBus':
        selectionList.value.push({
          equip_id: row.bus_id,
          equip_name: row.bus_name,
          equip_type_name: "母线",
          equip_type: "bus",
        });
        break;
      case 'bbLine':
        selectionList.value.push({
          equip_id: row.line_id,
          equip_name: row.line_name,
          equip_type_name: "线路",
          equip_type: "line",
        });
        break;
      case 'bbLoad':
        selectionList.value.push({
          equip_id: row.load_id,
          equip_name: row.load_name,
          equip_type_name: "负荷",
          equip_type: "load",
        });
        break;
      case 'bbSgen':
        selectionList.value.push({
          equip_id: row.sgen_id,
          equip_name: row.sgen_name,
          equip_type_name: "发电机",
          equip_type: "sgen",
        });
        break;
      case 'bbTrafo':
        selectionList.value.push({
          equip_id: row.trafo_id,
          equip_name: row.trafo_name,
          equip_type_name: "两绕变",
          equip_type: "trafo",
        });
        break;
      case 'bbTrafo3W':
        selectionList.value.push({
          equip_id: row.trafo3w_id,
          equip_name: row.trafo3w_name,
          equip_type_name: "三绕变",
          equip_type: "trafo3w",
        });
        break;
    }
  } else {
    const index = selectionList.value.findIndex((item) => {
      switch (activeName.value) {
        case 'bbBus':
          return item.equip_id === row.bus_id;
        case 'bbLine':
          return item.equip_id === row.line_id;
        case 'bbLoad':
          return item.equip_id === row.load_id;
        case 'bbSgen':
          return item.equip_id === row.sgen_id;
        case 'bbTrafo':
          return item.equip_id === row.trafo_id;
        case 'bbTrafo3W':
          return item.equip_id === row.trafo3w_id;
        default:
          return false;
      }
    });
    if (index !== -1) {
      selectionList.value.splice(index, 1);
    }
  }
};

// 设备列表查询
const getList = async () => {
  loading.value = true;
  tableData.value = [];
  selectionSetList.value = [];
  const queryNormal = {
    page: page.value,
    limit: limit.value,
    bb_case_id: bbCaseId.value,
    cime_basevoltage_id: scheme.value.toString(),
    cime_controlarea_id: partition.value.toString(),
    cime_substation_id: substation.value.toString(),
  };

  const equipids = selectionList.value.map((item) => item.equip_id);

  const fetchData = async (endpoint: (query: any) => Promise<any>, query: any) => {
    try {
      const res = await endpoint(query);
      tableData.value = res.data;
      page.value = res.page;
      limit.value = res.limit;
      total.value = res.total;
      isCheck.value = false;
      setTimeout(() => {
        if (Array.isArray(tableData.value)) {
            selectionSetList.value = tableData.value.filter((item) => {
              switch (activeName.value) {
                case 'bbBus':
                  return equipids.includes(item.bus_id);
                case 'bbLine':
                  return equipids.includes(item.line_id);
                case 'bbLoad':
                  return equipids.includes(item.load_id);
                case 'bbSgen':
                  return equipids.includes(item.sgen_id);
                case 'bbTrafo':
                  return equipids.includes(item.trafo_id);
                case 'bbTrafo3W':
                  return equipids.includes(item.trafo3w_id);
                default:
                  return false;
              }
            });
          } else {
            // console.warn('tableData 不是数组，无法调用 filter 方法');
          }
        }, 500);
    } catch (error) {
      // console.error('获取设备列表失败', error);
    } finally {
      loading.value = false;
    }
  };

  switch (activeName.value) {
    case 'bbBus':
      const busQuery = {
        ...queryNormal,
        bus_name: searchValue.value,
      };
      await fetchData(api.GetbbBusList, busQuery);
      break;
    case 'bbLine':
      const lineQuery = {
        ...queryNormal,
        line_name: searchValue.value,
      };
      await fetchData(api.GetbbLineList, lineQuery);
      break;
    case 'bbLoad':
      const loadQuery = {
        ...queryNormal,
        load_name: searchValue.value,
      };
      await fetchData(api.GetbbLoadList, loadQuery);
      break;
    case 'bbSgen':
      const sgenQuery = {
        ...queryNormal,
        sgen_name: searchValue.value,
      };
      await fetchData(api.GetbbSgenList, sgenQuery);
      tableData.value.forEach((item) => {
        item.mode_ctrl = item.mode_ctrl.toString();
      });
      break;
    case 'bbTrafo':
      const trafoQuery = {
        ...queryNormal,
        trafo_name: searchValue.value,
      };
      await fetchData(api.GetbbTrafoList, trafoQuery);
      break;
    case 'bbTrafo3W':
      const trafo3WQuery = {
        ...queryNormal,
        trafo3w_name: searchValue.value,
      };
      await fetchData(api.GetbbTrafo3WList, trafo3WQuery);
      break;
  }
};
defineExpose({ init });
</script>

<style scoped lang="scss">
.calculationModelDivClass {
  .parameterBox {
    .tools {
      display: flex;
      justify-content: space-between;

      .toolFlex {
        display: flex;
        align-items: center;

        .toolFlexItem {
          display: flex;
          align-items: center;
          padding-left: 10px;

          .name {
            padding-right: 8px;
          }
        }
      }

      .fa {
        padding-right: 10px;
      }

      .calculate {
        margin-left: 10px;
      }
    }

    .paginationStyle {
      padding: 30px 0;
      text-align: left;
    }
  }
}

.previewDialog {
  height: 650px;

  .el-dialog__header {
    display: none;
  }

  .dj-dialog-content {
    padding: 0;
    overflow: unset;
  }
}
</style>
