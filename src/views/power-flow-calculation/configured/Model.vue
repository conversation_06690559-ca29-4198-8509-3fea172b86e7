<template>
	<div class="flex flex-col h-full relative">
		<KeepAlive>
			<component :is="componentName"></component>
		</KeepAlive>
	</div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import Configured from './Configured.vue';
import SubstationGraph from '../graph/substation/index.vue';
import { useRoute } from 'vue-router';
const route = useRoute();
const componentName = computed(() => {
	return route.query.page === 'graph' ? SubstationGraph : Configured;
});
</script>

<style lang="scss" scoped></style>
