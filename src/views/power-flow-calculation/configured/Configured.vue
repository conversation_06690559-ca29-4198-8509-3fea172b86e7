<template>
	<Split :left-size="15" class="rounded-md flex row-col h-full overflow-hidden">
		<template #left>
			<div class="bg-white h-full flex flex-col p-2">
				<!-- <div class="text-lg font-bold">模型</div> -->
				<data-tree
					:props="{ label: 'name', value: 'table_key' }"
					:data="powerFlowCalculation"
					v-model="selected.table_key"
					@node-click="handleNodeClick"
					:is-search="false"
				/>
			</div>
		</template>
		<template #right>
			<div class="flex flex-col bg-white h-full flex-1" v-if="selected.table_key">
				<KeepAlive>
					<component :is="componentName" :key="selected.table_key" :param="selected"></component>
				</KeepAlive>
			</div>
			<div v-else class="bg-white rounded-md h-full flex-1 flex justify-center items-center">
				<el-empty description="暂无数据" />
			</div>
		</template>
	</Split>
</template>

<script setup lang="ts">
import { powerFlowCalculation } from '/@/config/SimulationConfig';
import Split from '/@/components/split/index.vue';
import DataTree from '/@/components/pm/DataTree.vue';
import { ref, computed } from 'vue';
import ConfiguredGrid from '/@/views/simulation-experiment/configured/ConfiguredGrid.vue';
const componentName = computed(() => {
	return ConfiguredGrid;
});
const selected = ref({ table_key: 'bb_bus', table_name: '母线' });
function handleNodeClick(data: Recordable) {
	if (!data.table_key) return;
	selected.value.table_key = data.table_key;
	selected.value.table_name = data.name;
}
</script>

<style lang="scss" scoped></style>
