import { ref, Ref, computed, ComputedRef } from 'vue';
import * as go from 'gojs';

export interface UsePaletteOptions {
	onNodeDropped?: (event: PaletteDroppedEvent) => void;
}

/**
 * 组件库 hook
 * @param paletteRef 组件库容器的引用
 * @param options 配置选项
 */
export function usePalette(paletteRef: Ref<HTMLElement | null>, componentObjects: IconInfo[], options?: UsePaletteOptions) {
	// 组件库实例
	let myPalette: go.Palette | null = null;

	/**
	 * 初始化组件库
	 * @returns 组件库实例
	 */
	const initPalette = (): go.Palette | null => {
		if (!paletteRef.value) return null;

		const $ = go.GraphObject.make;

		// 创建组件库实例
		myPalette = $(go.Palette, paletteRef.value as HTMLDivElement, {
			// 允许复制节点
			allowCopy: true,
			// 不允许删除节点
			allowDelete: false,
			// 布局选项
			layout: $(go.GridLayout, {
				wrappingColumn: 1, // 单列布局
				cellSize: new go.Size(1, 1), // 调整为更接近正方形的尺寸
				spacing: new go.Size(0, 10), // 调整垂直间距
				alignment: go.GridLayout.Position,
				arrangement: go.GridLayout.LeftToRight,
			}),
			// 内容居中设置
			initialContentAlignment: go.Spot.Center,
			contentAlignment: go.Spot.Center,
			padding: new go.Margin(60, 0, 60, 0), // 上下内边距
			// 不显示网格
			'grid.visible': false,
			// 最大选择数量
			maxSelectionCount: 1,
			// 自定义选中框样式
			nodeSelectionAdornmentTemplate: $(
				go.Adornment,
				'Auto',
				$(
					go.Shape,
					'RoundedRectangle', // 使用圆角矩形
					{
						fill: null,
						stroke: '#1677ff',
						strokeWidth: 2,
						spot1: new go.Spot(0, 0, 4, 4), // 设置圆角的位置
						spot2: new go.Spot(1, 1, -4, -4), // 设置圆角的位置
					}
				),
				$(go.Placeholder)
			),
		});

		// 为组件库创建自定义节点模板
		if (myPalette) {
			createPaletteTemplates(myPalette);

			// 创建组件库模型
			myPalette.model = new go.GraphLinksModel();

			// 直接添加组件到组件库
			componentObjects.forEach((comp) => {
				myPalette!.model.addNodeData({
					key: `palette-${comp.type}`,
					category: comp.type, // 使用type作为category
					name: comp.name,
					type: comp.type,
					color: comp.defaultColor,
					pos: [0, 0],
					angle: 0,
					isTemplate: true,
				});
			});
		}

		return myPalette;
	};

	/**
	 * 为组件库创建自定义节点模板
	 * @param palette 组件库实例
	 */
	const createPaletteTemplates = (palette: go.Palette) => {
		const $ = go.GraphObject.make;

		// 创建端口样式函数 - 在Palette中不显示端口，但需要保持配置一致
		const createPortTemplate = (portId: string, alignment: go.Spot, fromLinkable: boolean = true, toLinkable: boolean = true) => {
			return $(
				go.Shape,
				'Circle', // 使用圆形端口，与主图表一致
				{
					fill: 'transparent', // 在Palette中端口透明不可见
					stroke: null,
					desiredSize: new go.Size(6, 6),
					alignment: alignment,
					portId: portId,
					fromLinkable: fromLinkable,
					toLinkable: toLinkable,
					fromSpot: go.Spot.AllSides,
					toSpot: go.Spot.AllSides,
					cursor: 'pointer',
					visible: false, // 在Palette中不显示端口
				}
			);
		};

		componentObjects.forEach((comp) => {
			palette.nodeTemplateMap.add(
				comp.type, // 使用type作为category
				$(
					go.Node,
					'Spot',
					{
						locationSpot: go.Spot.Center,
						selectionAdorned: true,
						cursor: 'grab',
						background: 'transparent',
						width: 80, // 调整为正方形大小
						height: 80, // 设置相同的高度，确保是正方形
						resizable: false,
						rotatable: false,
					},
					// 背景形状 - 只在选中时显示
					$(
						go.Shape,
						'RoundedRectangle',
						{
							fill: 'rgba(22, 119, 255, 0.08)', // 淡蓝色背景
							stroke: null,
							strokeWidth: 0,
							stretch: go.GraphObject.Fill,
							alignment: go.Spot.Center,
							visible: false, // 默认不可见
						},
						new go.Binding('visible', 'isSelected').ofObject()
					),
					$(
						go.Panel,
						'Vertical',
						{
							alignment: go.Spot.Center,
							defaultAlignment: go.Spot.Center,
						},
						// 图标
						$(
							go.Shape,
							{
								name: 'SHAPE',
								geometryString: comp.path || '',
								fill: 'transparent',
								stroke: comp.defaultColor || '#1c57ea',
								strokeWidth: comp.strokeWidth || 1.5,
								width: (comp.width || 0) * 1.4, // 放大图标
								height: (comp.height || 0) * 1.4, // 放大图标
								alignment: go.Spot.Center,
								margin: new go.Margin(0, 0, 5, 0), // 下方添加间距
							},
							new go.Binding('stroke', 'color')
						),
						$(
							go.TextBlock,
							{
								name: 'TEXT',
								font: '12px sans-serif',
								stroke: '#333',
								alignment: go.Spot.Center,
								margin: new go.Margin(0, 0, 0, 0),
								maxSize: new go.Size(80, NaN), // 限制文本宽度
							},
							new go.Binding('text', 'name')
						)
					),
					// 添加端口（在Palette中不可见，但拖放到画布时会复制）
					...(comp.ports || []).map((port) => createPortTemplate(port.id, port.alignment, port.fromLinkable, port.toLinkable))
				)
			);
		});
	};

	/**
	 * 获取组件库实例
	 * @returns 组件库实例
	 */
	const getPalette = (): go.Palette | null => myPalette;

	return {
		initPalette,
		getPalette,
	};
}
