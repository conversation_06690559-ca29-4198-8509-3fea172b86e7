/*
 * 自定义BusResizeMultipleTool工具，扩展自ResizeMultipleTool
 * 允许同时调整多个母线节点的宽度
 */

import * as go from 'gojs';
import { ResizeMultipleTool } from './ResizeMultipleTool';

/**
 * BusResizeMultipleTool类允许用户同时调整多个母线节点的宽度。
 * 该工具从ResizeMultipleTool继承，但添加了以下特性：
 * 1. 只允许调整母线类型节点
 * 2. 只调整宽度而不调整高度
 * 3. 确保调整后的宽度被保存到数据模型中
 */
export class BusResizeMultipleTool extends ResizeMultipleTool {
	/**
	 * 构造函数
	 */
	constructor(init?: Partial<BusResizeMultipleTool>) {
		super();
		this.name = 'BusResizeMultiple';
		if (init) Object.assign(this, init);
	}

	/**
	 * 重写canStart方法，只有当所有选中节点都是母线节点时才允许开始调整。
	 */
	override canStart(): boolean {
		// 首先检查是否有选中的对象
		if (this.diagram.selection.count === 0) {
			return false;
		}

		// 检查是否所有选中的对象都是母线节点
		let allBuses = true;
		let busCount = 0;

		this.diagram.selection.each((part) => {
			if (part instanceof go.Link) return; // 跳过连线

			// 检查是否是母线节点
			if (part.data && part.data.type === 'BusbarSection') {
				busCount++;
			} else {
				allBuses = false;
			}
		});

		// 只有选中了母线节点且所有选中的节点都是母线时才允许调整
		if (!allBuses || busCount === 0) {
			return false;
		}

		// 最后检查基础条件（调用原生 ResizingTool 的检查）
		const baseCanStart = super.canStart();
		if (!baseCanStart) {
			return false;
		}

		return true;
	}

	/**
	 * 重写resize方法，调整所有选中母线节点的宽度。
	 * 只调整宽度，保持高度不变。
	 * @param newr - 新的矩形边界
	 */
	override resize(newr: go.Rect): void {
		const diagram = this.diagram;

		// 开始事务
		diagram.model.startTransaction('resize multiple buses');

		try {
			diagram.selection.each((part) => {
				if (part instanceof go.Link) return; // 跳过连线

				// 确保是母线节点
				if (part.data && part.data.type === 'BusbarSection') {
					// 只更新数据模型中的width属性，让数据绑定自动处理视觉更新
					diagram.model.setDataProperty(part.data, 'width', newr.width);

					console.log(`母线 ${part.data.key} 宽度调整为: ${newr.width}`);
				}
			});
		} finally {
			// 提交事务
			diagram.model.commitTransaction('resize multiple buses');
		}
	}

	/**
	 * 重写doActivate方法
	 */
	override doActivate(): void {
		super.doActivate();
	}
}
