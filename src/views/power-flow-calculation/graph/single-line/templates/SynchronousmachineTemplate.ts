import * as go from 'gojs';
import { createPortStyle, createNodeShape, createBaseNodeConfig, createBaseNodeBindings, createMainPanel } from './BaseTemplate';

/**
 * 同步发电机节点模板
 */

/**
 * 创建同步发电机节点模板
 * @returns GoJS Node模板
 */
export const createSynchronousmachineTemplate = () => {
	const $ = go.GraphObject.make;

	// 发电机的几何路径
	const generatorPath =
		'M737.28 545.28c0 108.866-91.692 197.12-204.8 197.12s-204.8-88.254-204.8-197.12c0-108.866 91.692-197.12 204.8-197.12s204.8 88.254 204.8 197.12z' +
		'M404.48 547.84c0-33.28 28.16-61.44 64-61.44s64 28.16 64 61.44' +
		'M660.48 545.28c0 33.28-28.16 61.44-64 61.44s-64-28.16-64-61.44' +
		'M527.36 281.6v61.44';

	return $(
		go.Node,
		'Spot',
		createBaseNodeConfig(),
		...createBaseNodeBindings(),
		createMainPanel(
			// 图标部分
			createNodeShape(generatorPath, '#1c57ea', 1.5, 16, 18),
			// 添加端口
			createPortStyle('O', new go.Spot(0.5, 0), true, true, 1, 1) // 顶部端口
		)
	);
};
