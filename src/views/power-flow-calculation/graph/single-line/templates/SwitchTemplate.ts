import * as go from 'gojs';
import { createPortStyle, createNodeShape, createBaseNodeConfig, createBaseNodeBindings, createMainPanel } from './BaseTemplate';

/**
 * 开关节点模板
 */

/**
 * 创建开关节点模板
 * @returns GoJS Node模板
 */
export const createSwitchTemplate = () => {
	const $ = go.GraphObject.make;

	// 开关的几何路径
	const switchPath = 'M281.6 286.976v460.8h460.8v-460.8zM512 389.376v256';

	return $(
		go.Node,
		'Spot',
		createBaseNodeConfig(),
		...createBaseNodeBindings(),
		createMainPanel(
			// 图标部分
			createNodeShape(switchPath, '#1c57ea', 1.5, 6, 6),
			// 文本标签部分
			// createNodeLabel(),
			// 添加端口
			createPortStyle('I', new go.Spot(0.5, 0), true, true, 1, 1), // 顶部端口
			createPortStyle('J', new go.Spot(0.5, 1), true, true, 1, 1) // 底部端口
		)
	);
};
