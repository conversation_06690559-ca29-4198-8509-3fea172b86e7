import * as go from 'gojs';
import { createTransformerCoil, createBaseNodeConfig, createBaseNodeBindings, createMainPanel } from './BaseTemplate';

/**
 * 三绕组变压器节点模板
 *
 * 功能说明:
 * - 由三个独立的圆圈组成，分别代表高压侧(H)、中压侧(M)、低压侧(L)绕组
 * - 每个圆圈都有独立的端口，可以单独连接线路
 * - 支持每个绕组的独立颜色配置
 * - 三角形布局: 上方1个(H)，下方左右各1个(M,L)
 * - 端口位置精确匹配圆圈位置，确保连线视觉效果
 */

/**
 * 创建三绕组变压器节点模板
 * @returns GoJS Node模板，包含三个独立的变压器绕组
 */
export const createTrafo3wTemplate = () => {
	const $ = go.GraphObject.make;

	return $(
		go.Node,
		'Spot', // 使用Spot布局，支持精确定位子元素
		createBaseNodeConfig(), // 应用基础节点配置
		...createBaseNodeBindings(), // 应用基础数据绑定
		createMainPanel(
			// === 高压侧绕组 (上方圆圈) ===
			// 位置: (0.5, 0.2) = 节点上方中央，略向下偏移
			// 端口方向: go.Spot.Top = 连线从上方进出
			// 颜色: '#1c57ea' = 蓝色 (默认颜色)
			// 尺寸: 18px 直径 (略小于两绕组)
			// 端口ID: 'H' (High voltage - 高压侧)
			createTransformerCoil('H', new go.Spot(0.5, 0), go.Spot.Top, '#1c57ea', 18),

			// === 中压侧绕组 (左下圆圈) ===
			// 位置: (0.2, 0.8) = 节点左下方
			// 端口方向: go.Spot.Left = 连线从左侧进出
			// 颜色: '#1c57ea' = 蓝色 (默认颜色)
			// 尺寸: 18px 直径
			// 端口ID: 'M' (Medium voltage - 中压侧)
			createTransformerCoil('M', new go.Spot(0.2, 1), go.Spot.Left, '#1c57ea', 18),

			// === 低压侧绕组 (右下圆圈) ===
			// 位置: (0.8, 0.8) = 节点右下方
			// 端口方向: go.Spot.Right = 连线从右侧进出
			// 颜色: '#1c57ea' = 蓝色 (默认颜色)
			// 尺寸: 18px 直径
			// 端口ID: 'L' (Low voltage - 低压侧)
			createTransformerCoil('L', new go.Spot(0.8, 1), go.Spot.Right, '#1c57ea', 18)

			// === 节点文本标签通过独立的LabelNode实现 ===
		)
	);
};
