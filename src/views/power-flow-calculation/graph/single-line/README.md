# 潮流计算 - 站内接线图页面

## 概述

这是潮流计算模块中的站内接线图页面，已经使用优化后的站内接线图组件进行了重构。

## 主要改进

### 1. 组件化重构
- ✅ **替换为优化组件**: 使用 `@/views/components/gojs/single-line/index.vue` 组件
- ✅ **业务逻辑分离**: 页面只负责业务数据获取和转换，图表渲染交给组件
- ✅ **代码简化**: 从1500+行代码简化为250行左右

### 2. 性能优化
- ✅ **增量更新**: 组件内置增量更新机制，提升大数据量渲染性能
- ✅ **内存管理**: 自动内存清理，避免内存泄漏
- ✅ **性能监控**: 内置性能报告功能

### 3. 用户体验提升
- ✅ **加载状态**: 优雅的加载动画
- ✅ **错误处理**: 完善的错误提示
- ✅ **操作反馈**: 成功/失败消息提示

## 文件结构

```
src/views/power-flow-calculation/graph/single-line/
├── index.vue          # 主页面（已重构）
├── api.ts            # API接口定义
├── icons/            # 图标资源
├── templates/        # GoJS模板（已迁移到组件）
├── hooks/            # 旧的hooks（已废弃）
└── README.md         # 本文档
```

## 核心功能

### 1. 数据加载
```typescript
// 从业务API获取数据
const { data } = await query_single_line_graph({
  bb_case_id: dataPacket.value.id,
  substation_id: props.nodeInfo.key,
});

// 转换为组件需要的格式
const { nodes, links } = transformApiData(data);

// 加载到图表组件
singleLineRef.value.loadGraphData(nodes, links, {
  autoCenter: true,
  preserveViewport: false,
  showProperties: showResult.value
});
```

### 2. 数据转换
`transformApiData` 函数负责将业务API返回的数据转换为组件需要的格式：

- **节点数据转换**: 包含位置、颜色、属性等信息
- **连线数据转换**: 包含端口、电压等级、颜色等信息
- **错误处理**: 跳过无效的连线数据

### 3. 事件处理
- **节点选择**: `handleNodeSelected` - 显示节点详细信息
- **模型变化**: `handleModelChanged` - 监听图表变化
- **属性切换**: `toggleShowResult` - 切换属性显示状态

## API接口

### Props
```typescript
interface Props {
  nodeInfo: {
    key: string;    // 变电站ID
    name: string;   // 变电站名称
  };
}
```

### Events
```typescript
interface Events {
  nodeSelected: (node: any | null) => void;  // 节点选择事件
}
```

## 使用方式

### 1. 基础使用
```vue
<template>
  <SingleLineGraph :nodeInfo="substationInfo" @nodeSelected="handleNodeSelected" />
</template>

<script setup>
const substationInfo = {
  key: 'substation-001',
  name: '220kV变电站A'
};

const handleNodeSelected = (node) => {
  console.log('选中节点:', node);
};
</script>
```

### 2. 依赖注入
页面需要注入 `dataPacket` 对象：
```typescript
const dataPacket = inject('dataPacket') as ComputedRef<{
  id: string;  // 基础数据包ID
}>;
```

## 性能特性

### 1. 增量更新
- 组件自动检测数据变化
- 仅更新发生变化的节点和连线
- 大幅提升大数据量场景的性能

### 2. 内存管理
- 自动清理缓存数据
- 支持手动内存清理
- 避免长时间使用导致的内存泄漏

### 3. 性能监控
```typescript
// 获取性能报告
const report = singleLineRef.value.getPerformanceReport();
console.log('性能报告:', {
  节点数: report.totalNodes,
  连线数: report.totalLinks,
  更新耗时: report.lastUpdateDuration,
  平均耗时: report.averageUpdateDuration
});
```

## 故障排除

### 1. 数据加载失败
- 检查 `dataPacket.value.id` 是否有效
- 检查 `props.nodeInfo.key` 是否正确
- 查看浏览器控制台错误信息

### 2. 图表显示异常
- 确认组件路径是否正确
- 检查数据格式是否符合要求
- 查看组件内部错误日志

### 3. 性能问题
- 使用性能报告检查渲染耗时
- 考虑启用增量更新模式
- 定期清理内存

## 迁移说明

### 从旧版本迁移
1. **移除旧依赖**: 删除旧的hooks和模板文件
2. **更新导入**: 使用新的组件路径
3. **调整事件**: 使用新的事件处理方式
4. **测试功能**: 确保所有功能正常工作

### 兼容性
- ✅ **API兼容**: 保持原有API接口不变
- ✅ **数据兼容**: 支持原有数据格式
- ✅ **功能兼容**: 保留所有原有功能

## 开发建议

1. **数据预处理**: 在 `transformApiData` 中处理数据异常
2. **错误处理**: 使用 try-catch 包装API调用
3. **性能优化**: 定期检查性能报告
4. **用户反馈**: 提供清晰的加载和错误状态

## 更新日志

### v2.0.0 (当前版本)
- ✨ 使用优化后的站内接线图组件
- 🚀 增量更新和性能优化
- 🎨 改进的用户界面
- 🧪 完整的错误处理

### v1.x.x (旧版本)
- 基础的GoJS图表功能
- 单体组件架构
- 简单的数据加载
