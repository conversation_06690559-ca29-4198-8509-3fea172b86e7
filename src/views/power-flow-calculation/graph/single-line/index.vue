<template>
	<div class="h-full" v-loading="loading" :element-loading-text="loading ? '加载单线图中...' : '正在计算布局，请稍候...'">
		<!-- 使用优化后的站内接线图组件 -->
		<SingleLine
			ref="singleLineRef"
			:title="nodeInfo.name"
			:showToolbar="true"
			:showPropertyPanel="false"
			:showResultToggle="true"
			:autoInitialize="true"
			@nodeSelected="handleNodeSelected"
			@modelChanged="handleModelChanged"
		/>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, inject, ComputedRef } from 'vue';
import { ElMessage } from 'element-plus';
import { getVoltageColor } from '/@/config/GraphConfig';
import { query_single_line_graph } from '../../api';
import SingleLine from '/@/views/components/gojs/single-line/index.vue';

// ===== 组件配置 =====
const dataPacket = inject('dataPacket') as ComputedRef<Recordable>;

const props = defineProps({
	nodeInfo: {
		type: Object,
		default: () => ({}),
	},
});

// 定义事件抛出
const emit = defineEmits<{
	nodeSelected: [node: any | null];
}>();

// ===== 状态管理 =====
const loading = ref<boolean>(false);
const showResult = ref<boolean>(false);
const singleLineRef = ref<InstanceType<typeof SingleLine> | null>(null);

// ===== 数据转换函数 =====

/**
 * 从API数据格式化属性文本
 */
const formatPropertyText = (apiNode: any): string => {
	const properties = apiNode.properties || {};
	const nodeType = apiNode.type || '';
	let resultLines: string[] = [];

	if (!properties || Object.keys(properties).length === 0) {
		return '';
	}

	switch (nodeType) {
		case 'BusbarSection':
			if (properties.vn_kv !== undefined && properties.vn_kv !== null) {
				resultLines.push(`U: ${Number(properties.vn_kv).toFixed(2)}`);
			}
			if (properties.dt_vn_kv !== undefined && properties.dt_vn_kv !== null) {
				resultLines.push(`U(M): ${Number(properties.dt_vn_kv).toFixed(2)}`);
			}
			break;

		case 'ACLineDot':
		case 'Load':
			if (properties.p_mw !== undefined && properties.p_mw !== null) {
				resultLines.push(`P: ${Number(properties.p_mw).toFixed(2)}`);
			}
			if (properties.dt_p_mw !== undefined && properties.dt_p_mw !== null) {
				resultLines.push(`P(M): ${Number(properties.dt_p_mw).toFixed(2)}`);
			}
			if (properties.q_mvar !== undefined && properties.q_mvar !== null) {
				resultLines.push(`Q: ${Number(properties.q_mvar).toFixed(2)}`);
			}
			if (properties.dt_q_mvar !== undefined && properties.dt_q_mvar !== null) {
				resultLines.push(`Q(M): ${Number(properties.dt_q_mvar).toFixed(2)}`);
			}
			break;

		case 'Synchronousmachine':
			if (properties.vn_kv !== undefined && properties.vn_kv !== null) {
				resultLines.push(`U: ${Number(properties.vn_kv).toFixed(2)}`);
			}
			if (properties.dt_vn_kv !== undefined && properties.dt_vn_kv !== null) {
				resultLines.push(`U(M): ${Number(properties.dt_vn_kv).toFixed(2)}`);
			}
			if (properties.p_mw !== undefined && properties.p_mw !== null) {
				resultLines.push(`P: ${Number(properties.p_mw).toFixed(2)}`);
			}
			if (properties.dt_p_mw !== undefined && properties.dt_p_mw !== null) {
				resultLines.push(`P(M): ${Number(properties.dt_p_mw).toFixed(2)}`);
			}
			if (properties.q_mvar !== undefined && properties.q_mvar !== null) {
				resultLines.push(`Q: ${Number(properties.q_mvar).toFixed(2)}`);
			}
			if (properties.dt_q_mvar !== undefined && properties.dt_q_mvar !== null) {
				resultLines.push(`Q(M): ${Number(properties.dt_q_mvar).toFixed(2)}`);
			}
			break;

		case 'Trafo':
			if (properties.p_hv_mw !== undefined && properties.p_hv_mw !== null) {
				resultLines.push(`P: ${Number(properties.p_hv_mw).toFixed(2)}`);
			}
			if (properties.q_hv_mvar !== undefined && properties.q_hv_mvar !== null) {
				resultLines.push(`Q: ${Number(properties.q_hv_mvar).toFixed(2)}`);
			}
			if (properties.dt_p_hv_mw !== undefined && properties.dt_p_hv_mw !== null) {
				resultLines.push(`P(M): ${Number(properties.dt_p_hv_mw).toFixed(2)}`);
			}
			if (properties.dt_q_hv_mvar !== undefined && properties.dt_q_hv_mvar !== null) {
				resultLines.push(`Q(M): ${Number(properties.dt_q_hv_mvar).toFixed(2)}`);
			}
			break;

		case 'Trafo3w':
			if (properties.p_hv_mw !== undefined && properties.p_hv_mw !== null) {
				resultLines.push(`PH: ${Number(properties.p_hv_mw).toFixed(2)}`);
			}
			if (properties.q_hv_mvar !== undefined && properties.q_hv_mvar !== null) {
				resultLines.push(`QH: ${Number(properties.q_hv_mvar).toFixed(2)}`);
			}
			if (properties.p_mv_mw !== undefined && properties.p_mv_mw !== null) {
				resultLines.push(`PM: ${Number(properties.p_mv_mw).toFixed(2)}`);
			}
			if (properties.q_mv_mvar !== undefined && properties.q_mv_mvar !== null) {
				resultLines.push(`QM: ${Number(properties.q_mv_mvar).toFixed(2)}`);
			}
			if (properties.p_lv_mw !== undefined && properties.p_lv_mw !== null) {
				resultLines.push(`PL: ${Number(properties.p_lv_mw).toFixed(2)}`);
			}
			if (properties.q_lv_mvar !== undefined && properties.q_lv_mvar !== null) {
				resultLines.push(`QL: ${Number(properties.q_lv_mvar).toFixed(2)}`);
			}
			break;

		case 'ShuntCompensator':
			if (properties.q_mvar !== undefined && properties.q_mvar !== null) {
				resultLines.push(`Q: ${Number(properties.q_mvar).toFixed(2)}`);
			}
			break;

		default:
			break;
	}

	return resultLines.join('\n');
};

/**
 * 转换API数据为组件需要的格式
 */
const transformApiData = (apiData: any) => {
	const nodes: any[] = [];
	const links: any[] = [];

	// 处理节点数据
	if (apiData.nodes && Array.isArray(apiData.nodes)) {
		apiData.nodes.forEach((node: any) => {
			// 创建主节点
			const nodeData = {
				key: node.id,
				category: node.type,
				type: node.type,
				name: node.name || '未命名节点',
				color: getVoltageColor(node.voltage),
				pos: [0, 0],
				angle: node.angle || 0,
				voltage: node.voltage,
				voltage2: node.voltage2,
				voltage3: node.voltage3,
				color2: getVoltageColor(node.voltage2),
				color3: getVoltageColor(node.voltage3),
				width: node.type === 'BusbarSection' ? 150 : undefined,
				properties: node.properties || {},
			};
			nodes.push(nodeData);

			// 创建标签节点 (LabelNode)
			const labelKey = node.id + '-label';
			const labelNode = {
				key: labelKey,
				text: node.name || '未命名节点',
				loc: '50 80', // 临时位置，布局后会重新计算
				category: 'LabelNode',
				visible: true,
				parentNodeId: node.id,
				color: getVoltageColor(node.voltage),
				offsetX: 0,
				offsetY: 30,
			};
			nodes.push(labelNode);

			// 创建属性节点 (PropertyNode) - 如果有属性数据
			const propertyText = formatPropertyText(node);
			if (propertyText) {
				const propertyKey = node.id + '-property';
				const propertyNode = {
					key: propertyKey,
					properties: propertyText,
					loc: '170 50', // 临时位置，布局后会重新计算
					category: 'PropertyNode',
					visible: showResult.value,
					parentNodeId: node.id,
					color: getVoltageColor(node.voltage),
					offsetX: 120,
					offsetY: 0,
				};
				nodes.push(propertyNode);

				// 创建注释连线 (AnnotationLink)
				const annotationKey = node.id + '-annotation';
				const annotationLink = {
					key: annotationKey,
					from: node.id,
					to: propertyKey,
					category: 'AnnotationLink',
					visible: showResult.value,
					color: getVoltageColor(node.voltage),
				};
				links.push(annotationLink);
			}
		});
	}

	// 处理连线数据
	if (apiData.edges && Array.isArray(apiData.edges)) {
		apiData.edges.forEach((link: any, index: number) => {
			if (!link.source || !link.target) {
				console.warn(`第${index}个连线缺少源或目标节点，已跳过`);
				return;
			}

			const linkData = {
				key: `link-${index}`,
				from: link.source,
				to: link.target,
				fromPort: link.source_port,
				toPort: link.target_port,
				properties: link.properties || {},
				voltage: link.voltage,
				color: getVoltageColor(link.voltage),
			};
			links.push(linkData);
		});
	}

	return { nodes, links };
};

/**
 * 加载单线图数据
 */
async function loadSubstationData() {
	loading.value = true;
	try {
		const { data } = await query_single_line_graph({
			bb_case_id: dataPacket.value.id,
			substation_id: props.nodeInfo.key,
		});

		// 确保数据存在且有效
		if (!data) {
			throw new Error('服务器返回的数据为空');
		}

		// 转换数据格式
		const { nodes, links } = transformApiData(data);

		// 加载数据到组件
		if (singleLineRef.value) {
			const success = singleLineRef.value.loadGraphData(nodes, links, {
				autoCenter: true,
				preserveViewport: false,
				showProperties: showResult.value,
			});

			if (success) {
				console.log('单线图数据加载成功:', {
					节点数: nodes.length,
					连线数: links.length,
				});
				ElMessage.success('单线图加载成功');
			}
		}
	} catch (error) {
		console.error('加载单线图失败:', error);
		ElMessage.error(`加载单线图失败: ${error instanceof Error ? error.message : '未知错误'}`);
	} finally {
		loading.value = false;
	}
}

// ===== 事件处理函数 =====

/**
 * 处理节点选择事件
 */
const handleNodeSelected = (node: any) => {
	emit('nodeSelected', node);
	console.log('选中节点:', node);
};

/**
 * 处理模型变化事件
 */
const handleModelChanged = (data: { nodes: any[]; links: any[] }) => {
	console.log('模型变化:', data);
};

// ===== 组件生命周期 =====

onMounted(() => {
	// 页面加载时自动加载数据
	loadSubstationData();
});
</script>
