<template>
	<div class="flex h-full overflow-hidden" v-loading="loading" :element-loading-text="loading ? '加载单线图中...' : '正在计算布局，请稍候...'">
		<!-- 使用优化后的站内接线图组件 -->
		<SingleLine
			ref="singleLineRef"
			:showToolbar="true"
			:showPropertyPanel="false"
			:autoInitialize="true"
			:enableSearch="true"
			:enableCategorization="true"
			@nodeSelected="handleNodeSelected"
			@modelChanged="handleModelChanged"
			class="flex-1"
		/>

		<!-- 自定义工具栏覆盖 -->
		<div class="absolute top-0 left-64 right-0 h-12 bg-white border-b border-gray-200 flex items-center justify-between px-4 z-10">
			<div class="flex items-center space-x-4">
				<div class="leading-[24px] font-bold">{{ nodeInfo.name }}</div>
				<el-tooltip :content="showResult ? '隐藏结果' : '显示结果'" placement="top">
					<el-button
						class="!m-0"
						:icon="showResult ? Hide : View"
						size="small"
						:type="showResult ? 'default' : 'primary'"
						circle
						@click="toggleShowResult"
					>
					</el-button>
				</el-tooltip>
			</div>

			<div class="flex items-center space-x-2">
				<el-button @click="refreshData" size="small" :loading="loading"> 刷新数据 </el-button>
				<el-button @click="showPerformanceReport" size="small"> 性能报告 </el-button>
			</div>
		</div>

		<!-- 右侧属性面板（如果需要显示选中节点信息） -->
		<div v-if="selectedNodeInfo && showNodePanel" class="w-60 bg-gray-50 border-l border-gray-200 flex flex-col">
			<div class="px-3 text-base py-2 font-medium text-gray-700 leading-[24px] border-b border-gray-200">节点信息</div>

			<div class="px-3 py-2 overflow-y-auto">
				<div class="mb-4">
					<div class="flex items-center py-1.5">
						<div class="w-16 text-xs text-gray-600">ID</div>
						<div class="flex-1 text-xs">{{ selectedNodeInfo.key }}</div>
					</div>

					<div class="flex items-center py-1.5">
						<div class="w-16 text-xs text-gray-600">名称</div>
						<div class="flex-1 text-xs">{{ selectedNodeInfo.name }}</div>
					</div>

					<div class="flex items-center py-1.5">
						<div class="w-16 text-xs text-gray-600">类型</div>
						<div class="flex-1 text-xs">{{ selectedNodeInfo.type }}</div>
					</div>

					<div v-if="selectedNodeInfo.voltage" class="flex items-center py-1.5">
						<div class="w-16 text-xs text-gray-600">电压</div>
						<div class="flex-1 text-xs">{{ selectedNodeInfo.voltage }}kV</div>
					</div>

					<div v-if="selectedNodeInfo.properties && Object.keys(selectedNodeInfo.properties).length > 0" class="mt-3">
						<div class="text-xs text-gray-600 mb-2">属性信息</div>
						<div class="text-xs space-y-1">
							<div v-for="(value, key) in selectedNodeInfo.properties" :key="key" class="flex justify-between">
								<span class="text-gray-600">{{ key }}:</span>
								<span>{{ typeof value === 'number' ? value.toFixed(2) : value }}</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, inject, ComputedRef } from 'vue';
import { ElMessage, ElButton } from 'element-plus';
import { View, Hide } from '@element-plus/icons-vue';
import { getVoltageColor } from '/@/config/GraphConfig';
import { query_single_line_graph } from '../../api';
import SingleLine from '/@/views/components/gojs/single-line/index.vue';

// ===== 组件配置 =====
const dataPacket = inject('dataPacket') as ComputedRef<Recordable>;

const props = defineProps({
	nodeInfo: {
		type: Object,
		default: () => ({}),
	},
});

// 定义事件抛出
const emit = defineEmits<{
	nodeSelected: [node: any | null];
}>();

// ===== 状态管理 =====
const loading = ref<boolean>(false);
const showResult = ref<boolean>(false);
const showNodePanel = ref<boolean>(false);
const selectedNodeInfo = ref<any>(null);
const singleLineRef = ref<InstanceType<typeof SingleLineComponent> | null>(null);

// ===== 数据转换函数 =====

/**
 * 转换API数据为组件需要的格式
 */
const transformApiData = (apiData: any) => {
	const nodes: any[] = [];
	const links: any[] = [];

	// 处理节点数据
	if (apiData.nodes && Array.isArray(apiData.nodes)) {
		apiData.nodes.forEach((node: any) => {
			const nodeData = {
				key: node.id,
				category: node.type,
				type: node.type,
				name: node.name || '未命名节点',
				color: getVoltageColor(node.voltage),
				pos: [0, 0],
				angle: node.angle || 0,
				voltage: node.voltage,
				voltage2: node.voltage2,
				voltage3: node.voltage3,
				color2: getVoltageColor(node.voltage2),
				color3: getVoltageColor(node.voltage3),
				width: node.type === 'BusbarSection' ? 150 : undefined,
				properties: node.properties || {},
			};
			nodes.push(nodeData);
		});
	}

	// 处理连线数据
	if (apiData.edges && Array.isArray(apiData.edges)) {
		apiData.edges.forEach((link: any, index: number) => {
			if (!link.source || !link.target) {
				console.warn(`第${index}个连线缺少源或目标节点，已跳过`);
				return;
			}

			const linkData = {
				key: `link-${index}`,
				from: link.source,
				to: link.target,
				fromPort: link.source_port,
				toPort: link.target_port,
				properties: link.properties || {},
				voltage: link.voltage,
				color: getVoltageColor(link.voltage),
			};
			links.push(linkData);
		});
	}

	return { nodes, links };
};

/**
 * 加载单线图数据
 */
async function loadSubstationData() {
	loading.value = true;
	try {
		const { data } = await query_single_line_graph({
			bb_case_id: dataPacket.value.id,
			substation_id: props.nodeInfo.key,
		});

		// 确保数据存在且有效
		if (!data) {
			throw new Error('服务器返回的数据为空');
		}

		// 转换数据格式
		const { nodes, links } = transformApiData(data);

		// 加载数据到组件
		if (singleLineRef.value) {
			const success = singleLineRef.value.loadGraphData(nodes, links, {
				autoCenter: true,
				preserveViewport: false,
				showProperties: showResult.value,
			});

			if (success) {
				console.log('单线图数据加载成功:', {
					节点数: nodes.length,
					连线数: links.length,
				});
				ElMessage.success('单线图加载成功');
			}
		}
	} catch (error) {
		console.error('加载单线图失败:', error);
		ElMessage.error(`加载单线图失败: ${error instanceof Error ? error.message : '未知错误'}`);
	} finally {
		loading.value = false;
	}
}

// ===== 事件处理函数 =====

/**
 * 处理节点选择事件
 */
const handleNodeSelected = (node: any) => {
	selectedNodeInfo.value = node;
	showNodePanel.value = !!node;
	emit('nodeSelected', node);
	console.log('选中节点:', node);
};

/**
 * 处理模型变化事件
 */
const handleModelChanged = (data: { nodes: any[]; links: any[] }) => {
	console.log('模型变化:', data);
};

/**
 * 切换属性显示
 */
const toggleShowResult = () => {
	showResult.value = !showResult.value;
	if (singleLineRef.value) {
		singleLineRef.value.toggleProperties(showResult.value);
	}
};

/**
 * 刷新数据
 */
const refreshData = () => {
	loadSubstationData();
};

/**
 * 显示性能报告
 */
const showPerformanceReport = () => {
	if (singleLineRef.value) {
		const report = singleLineRef.value.getPerformanceReport();
		console.log('性能报告:', report);
		ElMessage.info(`节点数: ${report.totalNodes}, 连线数: ${report.totalLinks}, 更新耗时: ${report.lastUpdateDuration.toFixed(2)}ms`);
	}
};

// ===== 组件生命周期 =====

onMounted(() => {
	// 页面加载时自动加载数据
	loadSubstationData();
});
</script>
