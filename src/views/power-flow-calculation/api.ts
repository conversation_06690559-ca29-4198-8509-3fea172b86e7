import { request } from '/@/utils/service';

export function query_connect_info() {
	return request({
		url: `/api/pm/topology_diagram/query_connect_info/`,
		method: 'POST',
	});
}

export function query_substation_graph(data: Recordable) {
	return request({
		url: `/api/pm/diagram/network_substation_graph/`,
		method: 'POST',
		data,
	});
}

export function query_single_line_graph({ bb_case_id, substation_id }: Recordable) {
	return request({
		url: `/api/pm/diagram/single_line_graph/`,
		method: 'POST',
		data: {
			bb_case_id,
			substation_id,
		},
	});
}

export function power_flow_calculation(data: { bb_case_id: string | number }) {
	return request({
		url: `/api/pm/pf/runPf/`,
		method: 'POST',
		data,
	});
}
