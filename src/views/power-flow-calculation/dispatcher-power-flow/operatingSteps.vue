<template>
    <el-dialog v-model="dialogVisible" title="操作步骤" width="1200">
        <div v-loading="loading">
            <el-button-group class="operatingSteps">
                <el-button type="primary" title="将所选多个操作步骤进行组合" @click="showForm">组合步骤</el-button>
                <el-button type="primary" @click="deleteOperationMode">删除</el-button>
            </el-button-group>
            <el-dialog :modal="false" :center="true" v-model="formVisible" width="30%">
                <el-form :model="form" ref="opform" :rules="rules">
                    <el-form-item label="步骤组合名称" prop="name">
                        <el-input v-model="form.name"></el-input>
                    </el-form-item>
                </el-form>
                <template #footer>
                    <div class="dialog-footer">
                        <el-button @click="formVisible = false">取 消</el-button>
                        <el-button type="primary" @click="saveOperationMode">
                            确 定
                        </el-button>
                    </div>
                </template>
            </el-dialog>
            <div class="dialogContent">
                <div class="dialogItem">
                    <div class="title">操作步骤</div>
                    <div class="treeBox">
                        <el-tree :data="dispatchFlowList" :props="defaultProps" ref="optree" node-key="nodeid" show-checkbox
                            check-strictly @check="toDispatchFlow" :default-checked-keys="dispatchFlowCheck">
                        </el-tree>
                    </div>
                </div>
                <div class="dialogItem">
                     <div class="title">对比计算</div>
                    <div class="treeBox">
                        <el-tree :data="dispatchFlowList" :props="defaultProps" ref="optree2" node-key="nodeid"
                            @check="toDispatchFlow2" show-checkbox check-strictly :default-checked-keys="dispatchFlowCheck2">
                         </el-tree>
                    </div>
                </div>
            </div>
        </div>

    </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus'
import * as calculateApi from '../../FaultSet/Popup/Add/calculateApi';
// 定义 emits
const emit = defineEmits(['changelinecolor', 'dispatchFlow', 'dispatchFlow2', 'dispatchFlow']);
const loading = ref(false)
const isShow = ref(false)
const dialogVisible = ref(false)
const bb_case_id = ref('')
const dispatchFlowList = ref<string[]>([])
const optree = ref()
const optree2 = ref()
const dispatchFlowCheck = ref<string[]>([])
const dispatchFlowCheck_temp = ref<string[]>([]);
const dispatchFlowCheck2 = ref<string[]>([])
const formVisible = ref(false)
const form = ref({
    name: ''
})
let rules = {
    name: [
        {
            required: true,
            message: '请输入步骤组合名称',
            trigger: 'blur'
        }
    ]
}
const opform = ref()
let defaultProps = {
    children: 'children',
    label: 'nodename'
}
const toDispatchFlow = (data) => {
    // 明确 lineids 数组的类型
    const lineids: any[] = [];
    lineids.push(data.line_id);
    emit('changelinecolor', lineids)
    const checkedNodes = optree.value?.getCheckedNodes ? optree.value.getCheckedNodes() : [];
    // 明确 nodeValues 的类型为 string 数组
    const nodeValues: string[] = [];
    dispatchFlowCheck.value = []
    for (var k = 0; k < checkedNodes.length; k++) {
        // 明确 dispatchFlowCheck 的类型为 string[] 或 number[]，这里假设 nodeid 为 string 类型
        const dispatchFlowCheck = ref<string[]>([]);
        // 修改后的代码
        dispatchFlowCheck.value.push(checkedNodes[k].nodeid as string);
        if (checkedNodes[k].nodetype === 'df') {
            nodeValues.push(checkedNodes[k].nodeid as string);
        } else {
            for (var j = 0; j < checkedNodes[k].children.length; j++) {
                nodeValues.push(checkedNodes[k].children[j].nodeid as string);
            }
        }
    }
    dispatchFlowCheck_temp.value = nodeValues
    emit('dispatchFlow', nodeValues)
}
const toDispatchFlow2 = () => {
    var checkedNodes = optree2.value?.getCheckedNodes ? optree2.value.getCheckedNodes() : [];
    dispatchFlowCheck2.value = []
    // 明确 nodeValues 的类型为 string 数组
    const nodeValues: string[] = [];
    for (var k = 0; k < checkedNodes.length; k++) {
        // 明确 dispatchFlowCheck2 的类型为 string[] 或 number[]，这里假设 nodeid 为 string 类型
        (dispatchFlowCheck2.value as string[]).push(checkedNodes[k].nodeid as string);
        if (checkedNodes[k].nodetype === 'df') {
            nodeValues.push(checkedNodes[k].nodeid)
        } else {
            for (var j = 0; j < checkedNodes[k].children.length; j++) {
                nodeValues.push(checkedNodes[k].children[j].nodeid)
            }
        }
    }
    emit('dispatchFlow2', nodeValues)
}
const showForm = () => {
    var hasop = 0
    var checkedNodes = optree.value?.getCheckedNodes ? optree.value.getCheckedNodes() : [];
    for (var i = 0; i < checkedNodes.length; i++) {
        var nodeType = checkedNodes[i].nodetype
        if (nodeType === 'op') {
            hasop = 1
        }
    }
    if (hasop === 0) {
        formVisible.value = true // 点击按钮时显示对话框
    } else {
        ElMessage.error('选择项中存在步骤组合，请选择操作步骤')
    }
}
const saveOperationMode = () => {
    loading.value = true
    opform.value.validate(err => {
        if (!err) {
            return false
        } else {
            var checkedNodes = optree.value.getCheckedNodes()
            const nodeValues: string[] = [];
            for (var i = 0; i < checkedNodes.length; i++) {
                var nodeValue = checkedNodes[i].nodeid
                nodeValues.push(nodeValue)
            }

            let query = {
                bb_case_id: bb_case_id.value,
                operation_mode_name: form.value.name,
                dispatch_flows: nodeValues
            }
            calculateApi.saveOperationMode(query).then((res) => {
                loading.value = false
                formVisible.value = false
                form.value.name = ''
                getDispatchFlowList()
                optree.value.setCheckedKeys([])
            }).catch(() => {
                loading.value = false
            })
        }
    })
}
const deleteOperationMode = () => {
    var checkedNodes =optree.value.getCheckedNodes()
    if (checkedNodes.length === 0) {
        ElMessage.warning('请选择需要删除的步骤组合，或操作步骤')
        return false
    } else {
        ElMessageBox('此操作将永久删除所选步骤组合, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(() => {
            const lineids: any[] = [];
            var nodeValues = new Array(checkedNodes.length)
            for (var k = 0; k < checkedNodes.length; k++) {
                if (checkedNodes[k].nodetype === 'df') {
                    for (var i = 0; i < dispatchFlowList.value.length; i++) {
                        if (dispatchFlowList.value[i].nodetype === 'op') {
                            for (var j = 0; j < dispatchFlowList.value[i].children.length; j++) {
                                if (checkedNodes[k].nodeid === dispatchFlowList.value[i].children[j].nodeid) {
                                    ElMessage({
                                        message: '[' + checkedNodes[k].nodename + '] 在 [' + dispatchFlowList.value[i].nodename + '] 中，不能删除',
                                        type: 'warning'
                                    })
                                    return false
                                }
                            }
                        }
                    }
                }
                nodeValues.push([checkedNodes[k].nodeid, checkedNodes[k].nodetype])
                lineids.push(checkedNodes[k].line_id)
            }
            emit('changelinecolor', lineids)
            let query = {
                bb_case_id: bb_case_id.value,
                operation_modes: nodeValues
            }
            calculateApi.deleteOperationMode(query).then((res) => {
                loading.value = false
                dispatchFlowCheck_temp.value = []
                emit('dispatchFlow', [])
                getDispatchFlowList()
            }).catch(() => {
               loading.value = false
            })
        }).catch(() => {
            return false
        })
    }
}
// 设备修改记录
const getDispatchFlowList = () => {
    loading.value = true
    let query = {
        bb_case_id: bb_case_id.value
    }
    calculateApi.getDispatchFlow(query).then((res) => {
        loading.value = false
        dispatchFlowList.value = res.data
        if (dispatchFlowCheck_temp.value.length === 0) {
            // 第一次初始化的时候根据数据库存储的状态设置默认选中选
            // 明确 line_ids 数组的类型
            const line_ids: any[] = [];
            for (const v of res.data) {
                if (v.ischecked === 1) {
                    // this.$refs.optree.setChecked(v.nodeid,true,false)
                    dispatchFlowCheck_temp.value.push(v.nodeid)

                    line_ids.push(v.line_id);
                }
            }
            dispatchFlowCheck.value = dispatchFlowCheck_temp.value;
            emit('dispatchFlow', dispatchFlowCheck_temp.value)
        } else {
            // 修改线路状态时根据记录的历史选中项加设置默认选中
            dispatchFlowCheck.value = dispatchFlowCheck_temp.value
        }
    }).catch(() => {
        loading.value = false
    })
}
const init = () => {
    dialogVisible.value = true
}
defineExpose({ init });
</script>
<style scoped lang="scss">
.dialogContent{
    width: 100%;
    display: flex;
    padding-top: 20px;
    justify-content: space-between;
    .dialogItem{
        width: 48%;
        .title{
            padding-bottom: 10px;
        }
        .treeBox{
            width: 100%;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            height: 600px;
        }
    }
}
</style>