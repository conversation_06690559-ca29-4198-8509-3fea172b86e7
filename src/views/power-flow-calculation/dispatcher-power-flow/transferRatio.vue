<template>
    <el-dialog v-model="dialogVisible" title="转移比(Top20)" width="1200">
        <div v-loading="loading">
            <div class="dialogContent">
                <el-tabs v-model="activeName">
                    <el-tab-pane label="线路" name="first">
                        <table-common :tableData="powerTransferList" :tableColumn="powerTransferListTableColumn"
                            :height="tableHeight" @sortChange="sorTableChange">
                            <template #line_name="scope">
                                <el-button type="text" size="small">
                                    <a @click="liclick(scope.row.line_name)">{{ scope.row.line_name }}</a>
                                </el-button>
                            </template>
                            <template #loading_percent="scope">
                                <el-tag :type="scope.row.loading_percent > 80 ? 'danger' : 'success'"
                                    close-transition>{{ scope.row.loading_percent }}</el-tag>
                            </template>
                        </table-common>
                    </el-tab-pane>
                    <el-tab-pane label="主变" name="second">
                        <table-common :tableData="transformerTransferList"
                            :tableColumn="transformerTransferListTableColumn" :height="tableHeight"
                            @sortChange="sorTableChange2">
                            <template #substation_name="scope">
                                <el-button type="text" size="small">
                                    <a @click="liclickTransformer(scope.row.substation_name)">{{
                                        scope.row.substation_name }}</a>
                                </el-button>
                            </template>
                        </table-common>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>

    </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus'
import * as calculateApi from '../../FaultSet/Popup/Add/calculateApi';
import tableCommon from "/@/components/table-common/index.vue";
// 定义 emits
const emit = defineEmits(['transforerListclick']);
const loading = ref(false)
const dialogVisible = ref(false)
const activeName = ref('first')
const bb_case_id = ref('')
const tableHeight = 600
const proptype = ref('')
const powerTransferList = ref<string[]>([])
const transformerTransferList = ref<string[]>([])
const powerTransferListTableColumn = [
    {
        label: '线路名称',
        prop: 'line_name',
        slotName: 'line_name'
    },
    {
        label: '转移比(%)',
        prop: 'transfer_ratio',
        slotName: 'transfer_ratio',
        sortable: 'custom'
    },
    {
        label: '负载率(%)',
        prop: 'loading_percent',
        slotName: 'loading_percent',
        sortable: 'custom'
    },
    {
        label: '方向',
        width: '70',
        prop: 'transfer_direction',
        sortable: 'custom'
    },
    {
        label: '变化量',
        prop: 'transfer_p_mw',
        sortable: 'custom'
    },
    {
        label: '切除前',
        prop: 'p_from_mw_y',
        sortable: 'custom'
    },
    {
        label: '切除后',
        prop: 'p_from_mw_x',
        sortable: 'custom'
    }

]
const transformerTransferListTableColumn = [
    {
        label: '厂站',
        prop: 'substation_name',
        slotName: 'substation_name'
    },
    {
        label: '主变',
        prop: 'trafo_name'
    },
    {
        label: '转移比(%)',
        prop: 'transfer_ratio',
        slotName: 'transfer_ratio',
        sortable: 'custom'
    },
    {
        label: '变化量',
        prop: 'transfer_p_mw',
        sortable: 'custom'
    },
    {
        label: '切除前',
        prop: 'p_hv_mw_y',
        sortable: 'custom'
    },
    {
        label: '切除后',
        prop: 'p_hv_mw_x',
        sortable: 'custom'
    }

]
const sorTableChange = (column) => {
    proptype.value = column.prop
    if (column.prop === 'transfer_direction') {
        if (column.order === 'descending') {
            powerTransferList.value.sort(chn_desc_sort)
        } else if (column.order === 'ascending') {
            powerTransferList.value.sort(chn_asc_sort)
        }
    } else {
        if (column.order === 'descending') {
            powerTransferList.value.sort(desc_sort)
        } else if (column.order === 'ascending') {
            powerTransferList.value.sort(asc_sort)
        }
    }
}
const sorTableChange2 = (column)=> {
    proptype.value = column.prop
    if (column.order === 'descending') {
       transformerTransferList.value.sort(desc_sort)
    } else if (column.order === 'ascending') {
       transformerTransferList.value.sort(asc_sort)
    }
}
const desc_sort = (a, b)=> {
    return b[proptype.value] - a[proptype.value]
}
const asc_sort = (a, b)=> {
    return a[proptype.value] - b[proptype.value]
}
const chn_desc_sort = (a, b)=> {
    return b[proptype.value].localeCompare(a[proptype.value])
}
const chn_asc_sort = (a, b)=> {
    return a[proptype.value].localeCompare(b[proptype.value])
}
const liclick = (item)=> {
    emit('transforerListclick', item)
}
const liclickTransformer = (item) => {
    emit('transforerListclick', item)
}
const init = () => {
    dialogVisible.value = true
}
defineExpose({ init });
</script>
<style scoped lang="scss">
.dialogContent {
    width: 100%;
}
</style>
