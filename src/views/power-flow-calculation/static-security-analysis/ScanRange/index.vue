<template>
  <el-dialog v-model="localVisible" width="70%" append-to-body title="扫描范围" @close="handleClose">
    <div class="dialog-content" style="height: 400px;overflow: auto;">
      <el-row :gutter="24">
        <el-col :span="24">
          <teg content="N-1范围" style="margin-bottom: 10px;"></teg>
          <div class="form-group">
            <el-collapse v-model="activeNamesN1" class="custom-collapse">
              <!-- 分区 -->
              <el-collapse-item name="zone">
                <template #title> 分区 </template>
                <div class="radio-group-container">
                  <el-checkbox-group v-model="localN1.zone" class="full-width">
                    <el-checkbox v-for="item in findControlareaBycaseidList" :key="item.mRID" :label="item.mRID"
                      class="checkbox-custom">
                      <span class="checkbox-label">{{ item.name }}</span>
                    </el-checkbox>
                  </el-checkbox-group>
                </div>
              </el-collapse-item>

              <!-- 电压等级 -->
              <el-collapse-item name="voltagelevel">
                <template #title> 电压等级 </template>
                <div class="radio-group-container">
                  <el-checkbox-group v-model="localN1.voltagelevel" class="full-width">
                    <el-checkbox v-for="item in findBasevoltageBycaseidList" :key="item.mRID" :label="item.mRID"
                      class="checkbox-custom">
                      <span class="checkbox-label">{{ item.name }}</span>
                    </el-checkbox>
                  </el-checkbox-group>
                </div>
              </el-collapse-item>

              <!-- 设备类型 -->
              <el-collapse-item name="deviceType">
                <template #title> 设备类型 </template>
                <el-checkbox-group v-model="localN1.euqiptype" class="full-width">
                  <el-checkbox label="line" class="checkbox-custom">
                    <span class="checkbox-label">线路</span>
                  </el-checkbox>
                  <el-checkbox label="trafo" class="checkbox-custom">
                    <span class="checkbox-label">两绕变</span>
                  </el-checkbox>
                  <el-checkbox label="trafo3w" class="checkbox-custom">
                    <span class="checkbox-label">三绕变</span>
                  </el-checkbox>
                  <el-checkbox label="gen" class="checkbox-custom">
                    <span class="checkbox-label">发电机</span>
                  </el-checkbox>
                  <el-checkbox label="load" class="checkbox-custom">
                    <span class="checkbox-label">负荷</span>
                  </el-checkbox>
                  <el-checkbox label="bus" class="checkbox-custom">
                    <span class="checkbox-label">母线</span>
                  </el-checkbox>
                </el-checkbox-group>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-col>
      </el-row>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="confirmSelection">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue';
import * as api from "./api";
import teg from "/@/components/teg/teg/index.vue";

// 定义 props 类型
const props = defineProps<{
  visible: boolean;
  bb_case_id: string;
  n1: Record<string, any>;
}>();

// 定义 emit 类型
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'update:n1', value: Record<string, any>): void;
}>();

// 响应式数据
const localVisible = ref(props.visible);
const localN1 = ref<Record<string, any>>(JSON.parse(JSON.stringify(props.n1)));
const findBasevoltageBycaseidList = ref<any[]>([]);
const findControlareaBycaseidList = ref<any[]>([]);
const activeNamesN1 = ref<string[]>(["zone", "voltagelevel", "deviceType"]);
const activeNamesN2 = ref<string[]>(["zone", "voltagelevel", "deviceType"]);

// 监听 props 变化
watch(() => props.n1, (newVal) => {
  localN1.value = JSON.parse(JSON.stringify(newVal));
});


watch(() => props.visible, (newVal) => {
  localVisible.value = newVal;
});

// 监听本地变量变化，触发父组件更新
watch(localVisible, (newVal) => {
  emit('update:visible', newVal);
});

// 生命周期钩子
onMounted(async () => {
  await findControlareaBycaseid();
  await findBasevoltageBycaseid();
});

// 方法定义
const findControlareaBycaseid = async () => {
  const res = await api.findControlareaBycaseid({
    bb_case_id: props.bb_case_id,
  });
  findControlareaBycaseidList.value = res.data;
};

const findBasevoltageBycaseid = async () => {
  const res = await api.findBasevoltageBycaseid({
    bb_case_id: props.bb_case_id,
  });
  findBasevoltageBycaseidList.value = res.data;
};

const confirmSelection = () => {
  emit('update:n1', localN1.value);
  localVisible.value = false;
};

const handleCancel = () => {
  localVisible.value = false;
};

const handleClose = () => {
  localVisible.value = false;
};
</script>
<style scoped lang="scss">
.dialog-content {
  padding: 20px;
}
.form-group {
  border: 1px solid #ddd;
  padding: 20px;
  margin-bottom: 20px;
}
</style>

<style scoped>
/* 给折叠面板设置一个最大高度，并允许滚动 */
.custom-collapse .el-collapse-item__content {
  max-height: 200px; /* 根据需要调整 */
  overflow-y: auto;
}

.radio-group-container {
  width: 100%;
}

.full-width {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* 确保每个选项占据一整行 */
.checkbox-custom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 8px 0; /* 调整间距 */
  height: 40px; /* 固定高度 */
}

/* 文字靠左对齐 */
.checkbox-label {
  margin-right: auto; /* 让文字尽可能靠近左边 */
  margin-left: 5px; /* 可根据需要调整 */
  flex-grow: 1; /* 让文字占据剩余空间 */
}

/* 复选框容器样式调整 */
.el-checkbox__input {
  flex-shrink: 0;
}

/* 如果需要进一步定制选中状态下的样式 */
.el-checkbox__input.is-checked .el-checkbox__inner {
  border-color: #409eff;
  background: #409eff;
}

/* 增加每个选项之间的间距 */
.checkbox-custom + .checkbox-custom {
  border-top: 1px solid #ebeef5; /* 可以根据需要调整颜色 */
}
</style>
