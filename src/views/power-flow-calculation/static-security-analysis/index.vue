<template>
	<div class="bg-[#f7f7f7] h-full flex flex-col overflow-hidden p-[15px]" style="overflow-y: auto">
		<div class="header flex justify-between">
			<div class="operationName flex w-[400px]" style="align-items: center">
				<div class="w-[75px]" style="min-width: 75px;">作业名称：</div>
				<el-input v-model="operationName" placeholder="请输入作业名称"></el-input>
				<div class="ml-[10px]">
					<el-button type="primary" @click="selectOperation">···</el-button>
				</div>
			</div>
			<div class="tool flex" style="align-items: center">
				<div class="border-[#409eff] border-solid border-[1px] flex pt-[3px] pb-[3px] pl-[10px] pr-[10px] mr-[10px]" style="border-radius: 3px">
					<el-checkbox v-model="customCheck" :disabled="specialCheck" label="选择故障集" style="margin: 0;"></el-checkbox>
					<div class="ml-[10px] mr-[10px] text-[#000]"><el-button type="primary" @click="customizeFaultGroup" :disabled="specialCheck || !customCheck">选择故障集</el-button></div>
					<el-checkbox v-model="NCheck" :disabled="specialCheck || !customCheck">N-1</el-checkbox>
				</div>
				<div class="border-[#409eff] border-solid border-[1px]  flex  pt-[3px] pb-[3px] pl-[10px] pr-[10px]" style="border-radius: 3px">
					<el-checkbox v-model="specialCheck" :disabled="customCheck" label="特殊故障集"></el-checkbox>
				</div>
				<div class="ml-[10px]">
					<el-button @click="isChildDialogVisible = true">扫码范围</el-button>
				</div>
				<div class="ml-[10px]">
					<el-button @click="getResectionPlanList">{{loading ? '计算中':'计算'}}</el-button>
				</div>
				<div class="ml-[10px]">
					<el-button>新建</el-button>
				</div>
				<div class="ml-[10px]">
					<el-button>保存</el-button>
				</div>
			</div>
		</div>
		<!--特殊故障组-->
		<div class="pt-[10px]" v-if="specialCheck">
			<div class="text-[18px] pb-[5px]">特殊故障组</div>
			<div style="overflow-y:auto;box-sizing: border-box">
				<table-common :tableData="specialFaultTableData" height="300"
							  :tableColumn="specialFaultColumn"
							  :selectionList="specialFaultSelectionList" selection radio @select="specialFaultSelect" @selectAll="specialFaultSelect">
				</table-common>
			</div>
		</div>
		<!--自定义故障组-->
		<div class="pt-[10px]" v-else>
			<div class="text-[18px] pb-[5px]">故障设备</div>
			<div style="overflow-y:auto;box-sizing: border-box">
				<table-common :tableData="failureEquipmentTableData" height="300"
							  :tableColumn="failureEquipmentColumn" @rowContextmenu="failureEquipmentRowContextmenu">
				</table-common>
			</div>
		</div>
		<div class="pt-[10px] flex justify-between">
			<div class="w-[60%]">
				<div class="text-[18px] pb-[5px] flex" style="align-items: center">
					<div class="mr-[10px]">切除方案</div>
					<el-button type="primary">刷新</el-button>
				</div>
				<div style="overflow-y:auto;box-sizing: border-box">
					<table-common :tableData="resectionPlanTableData" :height="tableHeight"
								  :tableColumn="resectionPlanColumn">
						<template #result="scope">
							<el-button type="text" size="small">
								<a @click="liclickTransformer(scope.row.result)">{{
									scope.row.result }}</a>
							</el-button>
						</template>
					</table-common>
					<div class="pagination mt-[10px]  flex justify-end">
						<el-pagination v-model:current-page="resectionPlanCurrentPage" v-model:page-size="resectionPlanPageSize" :pager-count="5"
									   :page-sizes="[10, 20, 30, 50, 80, 100]" :total="resectionPlanTotal" layout="total, sizes, prev, pager, next, jumper"
									   background @size-change="resectionPlanHandleSizeChange" @current-change="resectionPlanHandleCurrentChange">
						</el-pagination>
					</div>
				</div>
			</div>
			<div class="w-[39%]">
				<div class="text-[18px] pb-[5px]">越限信息</div>
				<div style="overflow-y:auto;box-sizing: border-box">
					<table-common :tableData="overLimitTableData" :height="tableHeight"
								  :tableColumn="overLimitColumn">
					</table-common>
				</div>
			</div>
		</div>
		<!--自定义故障组右击菜单-->
		<div
			v-show="contextMenu.visible"
			class="custom-context-menu"
			:style="{left: `${contextMenu.x}px`,top: `${contextMenu.y}px`}"
		>
			<div class="menu-item" @click="handleMenuClick('delete')">删除</div>
			<div class="menu-item" @click="handleMenuClick('deleteAll')">清空</div>
		</div>
		<!--作业选择-->
		<el-dialog
			v-model="operationDialogVisible"
			title="作业选择"
			width="70%">
			<div>
				<div class="tableContent" ref="tableContentRef">
					<table-common :tableData="listItems" :tableColumn="tableColumn"
								  :operationTable="operationTable" selection radio @select="selectionSelect" @selectAll="selectionSelect"
								  :selectionList="selectionList"></table-common>
				</div>
				<div class="pagination mt-[15px]  flex justify-end">
					<el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :pager-count="5"
								   :page-sizes="[10, 20, 30, 50, 80, 100]" :total="total" layout="total, sizes, prev, pager, next, jumper"
								   background @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange">
					</el-pagination>
				</div>
			</div>
			<template #footer>
				<div class="dialog-footer flex justify-end">
					<el-button type="primary" @click="submitOperation">
						确定
					</el-button>
				</div>
			</template>
		</el-dialog>
		<!--选择自定义故障组-->
		<fault-set-selector v-model:visible="showCustomDialog" :loading="isLoading" :id="bb_case_id || ''"
							@addSelectEquip="handleAddFaultSetFromCustomDialog"></fault-set-selector>
		<!-- 扫描范围弹窗 -->
		<scan-range-dialog v-if="isChildDialogVisible" v-model:visible="isChildDialogVisible" :bb_case_id="bb_case_id || ''"
						   v-model:n1="parentN1"></scan-range-dialog>
	</div>
</template>

<script setup lang="ts">
    import { ref, onMounted, watch,nextTick } from 'vue';
    import tableCommon from '/@/components/table-common/index.vue';
    import FaultSetSelector from "./faultSelectorDialog/index.vue";
    import ScanRangeDialog from "./ScanRange/index.vue";
    import { ElMessage, ElMessageBox } from 'element-plus'
    import * as api from "./api";
    import { useRoute, useRouter } from 'vue-router';
    const route = useRoute();
    const router = useRouter();
    const tableHeight = ref(300);
    const loading = ref(false);
    const bb_case_id = ref<number>(0);
    // 作业名称
	const operationName = ref('');
	const operationDialogVisible = ref<boolean>(false)
    const listItems = ref([]);
    const total = ref(0)
    const currentPage = ref(1)
    const pageSize = ref(15)
	const operationLoading = ref<boolean>(false)
    const tableColumn = [
        {
            label: "名称",
            prop: "case_name",
            minWidth: "150",
            fixed: true,
        }
    ];
    const operationTable = {
        label: "操作",
        width: "100",
        fixed: true,
        buttons: [
            {
                label: '删除',
                type: 'danger',
                clickFunction: (item: any) => {
                    delsaCaseList(item)
                }
            }
        ]
    }
    const operationSelect = ref<{ [key: string]: any }>({})
	const selectionList = ref([])
	// 自定义故障集
	const customCheck = ref<boolean>(true);
    const showCustomDialog = ref<boolean>(false);
    const currentRow = ref();
    const contextMenu = ref({
        visible: false,
        x: 0,
        y: 0
    })
	// 特殊故障集
	const specialCheck = ref<boolean>(false);
	const specialFaultTableData = ref([
        { id: "1", name: "变电站N-1(单一变电站全失)" },
        { id: "2", name: "并列线路N-2(平行双回线路同时跳闸)" },
        { id: "3", name: "主变N-2(同一变电站任意两台主变同时跳闸)" },
	])
	const specialFaultColumn:TableColumn[] = [
        {
            label: '特殊故障集',
            prop: 'name',
            headerAlign: "center",
        }
    ];
    const specialFaultSelectionList = ref([])
	const specialFaultSelectVal = ref<{ [key: string]: any }>({})
	// 扫描范围
    const isChildDialogVisible = ref<boolean>(false);
    const parentN1 = ref<{ zone: any[]; voltagelevel: any[]; euqiptype: any[] }>({ zone: [], voltagelevel: [], euqiptype: [] });
	// n-1
	const NCheck = ref<boolean>(false);
	// 故障设备列表
	const failureEquipmentTableData = ref([]);
	const failureEquipmentColumn:TableColumn[] = [
        {
            label: '故障集名称',
            prop: 'fault_group_name',
            width: '400',
			headerAlign: "center"
        },
        {
            label: '设备信息',
            prop: 'equip_name',
            headerAlign: "center"
        }
    ];
	// 切除方案
	const resectionPlanTableData = ref([]);
    const resectionPlanColumn:TableColumn[] = [
        {
            label: '切除方案',
            prop: 'name',
            headerAlign: "center"
        },
        {
            label: '计算结果',
            prop: 'result',
            headerAlign: "center",
            align: "center",
			width: '200',
            slotName: 'result'
        }
    ];
    const resectionPlanTotal = ref(0)
    const resectionPlanCurrentPage = ref(1)
    const resectionPlanPageSize = ref(15)
	// 越限信息
	const overLimitTableData = ref([]);
	const overLimitColumn:TableColumn[] = [
        {
            label: '越限设备',
            prop: 'name',
            headerAlign: "center"
        },
        {
            label: '越限数据',
            prop: 'content',
            headerAlign: "center",
			align: "center"
        }
    ];
    // 初始化方法
    const init = () => {
        let winHeight = window.innerHeight > 768? window.innerHeight: 768
        tableHeight.value = winHeight - 580;
        bb_case_id.value = route.query.id;
        operationSelect.value = {}
        const handleResize = () => {
            tableHeight.value = winHeight - 538;
        };
        window.addEventListener('resize', handleResize);
        // 组件卸载时移除事件监听器
        return () => {
            window.removeEventListener('resize', handleResize);
        };
    };
    
    // 选择作业
	const selectOperation = ()=>{
        operationDialogVisible.value = true
        getList()
	}
	const selectionSelect = (val: any)=>{
        operationSelect.value = val
	}
	const submitOperation = ()=>{
		console.log(operationSelect.value)
        operationDialogVisible.value = false
		if(operationSelect.value && operationSelect.value.id){
            operationName.value = operationSelect.value.case_name
		}
	}
	// 根据名称查询列表是否有同名数据
	const getSelectList = async () => {
        if(operationName.value){
            const params = { page: 1, limit: 1, case_name: operationName.value};
            const res = await api.GetList(params);
            if (res.code === 2000) {
                selectionList.value = res.data;
                operationSelect.value = selectionList.value && selectionList.value.length ? selectionList.value[0]: {}
            }
		} else {
            selectionList.value = []
		}
    };
    const getList = async (page = currentPage.value, size = pageSize.value) => {
        operationLoading.value = true
        const params = { page, limit: size, ordering: "create_datetime"};
        const res = await api.GetList(params);
        if (res.code === 2000) {
            listItems.value = res.data;
            total.value = res.total;
            getSelectList()
        } else {
            ElMessage.error("获取列表失败");
        }
        operationLoading.value = false
    };
    // 分页改变
    const onHandleSizeChange = (val: number) => {
        pageSize.value = val;
        getList(currentPage.value, pageSize.value)
    };
    // 分页改变
    const onHandleCurrentChange = (val: number) => {
        currentPage.value = val;
        getList(currentPage.value, pageSize.value)
    };
    const delsaCaseList =  (item: any) => {
        ElMessageBox.confirm(
            '确定要删除吗?',
            '提示',
            {
                confirmButtonText: '删除',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
            .then(async() => {
                const res = await api.DelObj(item.id,)
                if (res.code === 2000) {
                    ElMessage.success("删除成功!");
                    // 假设 item 是一个包含 id 属性的对象，并且列表项也有 id 属性
                    listItems.value = listItems.value.filter((i: { id: any }) => i.id !== item.id);
                    if (listItems.value.length === 0 && currentPage.value > 1) {
                        currentPage.value -= 1;
                    }
                    getList(currentPage.value, pageSize.value)
                } else {
                    ElMessage.error("删除失败");
                }
            })
            .catch(() => {

            })
    }
    
    // 选择自定义故障组
    const customizeFaultGroup = () => {
        showCustomDialog.value = true; // 打开自定义故障集选择对话框
    };
    const handleAddFaultSetFromCustomDialog = (res: any) => {
        // console.log(res, "返回");
        if (!res.length) {
            ElMessage.warning("请选择故障组设备");
            return;
        }
        res.forEach((item: any) => {
            const data = {
                ...item,
                selectList: item,
            };
            failureEquipmentTableData.value.push(data);
        });
        ElMessage.success("选择成功");
    };
    const failureEquipmentRowContextmenu = (row: any, column: any, event: any) =>{
        event.preventDefault();
        currentRow.value = row;
        contextMenu.value = {
            visible: true,
            x: event.clientX,
            y: event.clientY
        }
	}
    // 点击其他地方关闭菜单
    document.addEventListener('click', () => {
        contextMenu.value.visible = false
    })
    const handleMenuClick = (command:any) => {
        if(command === 'delete'){
            failureEquipmentTableData.value = failureEquipmentTableData.value.filter(item=>{
                return item.id !== currentRow.value.id
			})
		} else {
            failureEquipmentTableData.value = []
		}
    };
    // 选择特殊故障组
    const specialFaultSelect = (val: any)=>{
        specialFaultSelectVal.value = val
    }
    // 切除方案
    const getResectionPlanList = async (page = resectionPlanCurrentPage.value, size = resectionPlanPageSize.value) => {
        loading.value = true
        const params = { page, limit: size, ordering: "create_datetime"};
        resectionPlanTableData.value = [
            {
                name: 'XX变#1主变，XX线，YY变2号主变，设备1',
                result: '不收敛'
            },
            {
                name: 'XX变#1主变，XX线，YY变2号主变，设备2',
                result: '过载|高电压'
            },
            {
                name: 'XX变#1主变，XX线，YY变2号主变，设备3',
                result: '高电压'
            },
            {
                name: 'XX变#1主变，XX线，YY变2号主变，设备4',
                result: '低电压'
            }
        ]
        // const res = await api.GetList(params);
        // if (res.code === 2000) {
        //     listItems.value = res.data;
        //     total.value = res.total;
        //     getSelectList()
        // } else {
        //     ElMessage.error("获取列表失败");
        // }
        loading.value = false
    };
    const liclickTransformer = ()=>{
        overLimitTableData.value = [
			{
			    name: '设备1',
				content: '过载90%'
			},
            {
                name: '设备2',
                content: '高电压560.8%'
            }
		]
	}
    // 分页改变
    const resectionPlanHandleSizeChange = (val: number) => {
        resectionPlanPageSize.value = val;
        getResectionPlanList(resectionPlanCurrentPage.value, resectionPlanPageSize.value)
    };
    // 分页改变
    const resectionPlanHandleCurrentChange = (val: number) => {
        resectionPlanCurrentPage.value = val;
        getResectionPlanList(resectionPlanCurrentPage.value, resectionPlanPageSize.value)
    };
    onMounted(() => {
        init();
    });
</script>
<style lang="scss" scoped>
	.custom-context-menu {
		position: fixed;
		z-index: 9999;
		background: #fff;
		padding: 10px 15px;
		border-radius: 5px;
		box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);
		.menu-item{
			cursor: pointer;
			padding: 5px 0;
			&:hover{
				color: #3967FF;
			}
		}
	}
</style>
