<template>
  <el-dialog v-model="dialogVisible" :close-on-click-modal="false" :append-to-body="true" :top="'2vh'"
    class="previewDialog" width="70%" v-loading="loading" title="自定义故障组">
    <div class="calculationModelDivClass" v-loading="loading">
      <div class="parameterBox">
        <div class="form"></div>
        <div class="tools" style="margin-bottom: 10px">
          <div class="toolFlex">
            <div class="toolFlexItem">
              <div class="name">名称:</div>
              <el-input placeholder="请输入名称" v-model="searchValue" size="mini" style="width: 300px" clearable></el-input>
            </div>
            <el-button class="calculate" type="primary" :icon="Search" size="mini" @click="searchTable">查询</el-button>
            <el-button class="calculate" :icon="Refresh" size="mini" @click="resetSearch">重置</el-button>
          </div>
        </div>
        <div style="overflow-y: auto; box-sizing: border-box">
          <table-common :tableData="tableData" :height="tableHeight" :tableColumn="tableColumn" :selection="true"
            :selectionList="selectionSetList" @select="selectionChange" @selectAll="selectionChange"></table-common>
        </div>
        <div class="tools">
          <div class="toolFlex" style="justify-content: end;">
            <div class="toolFlexItem">
              <el-button size="mini" @click="dialogVisible = false">取消</el-button>
              <el-button type="primary" size="mini" @click="addSelectEquip">确定</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import * as api from './api';
import tableCommon from '/@/components/table-common/index.vue';
import { Search, Refresh } from '@element-plus/icons-vue'
import {ElMessage} from 'element-plus'

// 定义 props
const props = defineProps<{
  visible: boolean;
  loading: boolean;
  id: string;
}>();

// 定义 emits
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'addSelectEquip', selection: any[]): void;
}>();

// 响应式数据
const dialogVisible = ref(props.visible);
const searchValue = ref('');
const tableData = ref<any[]>([]);
const tableColumn = [
  { prop: 'fault_group_name', label: '故障组名称' },
  { prop: 'equip_name', label: '设备名称' },
];
const selectionSetList = ref<any[]>([]);
const tableHeight = ref(400);

// 监听 props.visible 的变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
});

// 监听 dialogVisible 的变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal);
  if (newVal) {
    fetchData();
  }
});

// 获取数据的方法
const fetchData = async () => {
  try {
    // 调用 API 获取数据
    const params = {
      id: props.id,
      search: searchValue.value || null, // 搜索条件
      is_sa_edit: 0, // 这里是查询自定义故障集的 is_sa_edit 给 0，编辑回显示的时候才给 1
    };

    const response = await api.getBulkQuery(params);

    // 更新表格数据和总记录数
    if (response.code === 2000) {
      tableData.value = response.data || [];
    } else {
      // console.error('Error fetching data:', response.msg);
      ElMessage.error(response.msg);
    }
  } catch (error) {
    // console.error('Error fetching data:', error);
    ElMessage.error('获取数据失败，请稍后再试！');
  }
};

// 搜索表格
const searchTable = () => {
  // 重新获取数据
  fetchData();
};

// 重置搜索
const resetSearch = () => {
  searchValue.value = '';
  fetchData(); // 重新获取数据
};

// 选择变化
const selectionChange = (selection: any[]) => {
  selectionSetList.value = selection;
};

// 添加选中设备
const addSelectEquip = () => {
  emit('addSelectEquip', selectionSetList.value);
  dialogVisible.value = false;
};
</script>

<style scoped>
.previewDialog {
  padding: 20px;
}
.parameterBox {
  /* margin-bottom: 20px; */
}
.tools {
  margin-top: 10px;
}
.toolFlex {
  display: flex;
  align-items: center;
}
.toolFlexItem {
  margin-right: 10px;
  display: flex;
  align-items: center;
}
.name {
  margin-right: 5px;
}
</style>
