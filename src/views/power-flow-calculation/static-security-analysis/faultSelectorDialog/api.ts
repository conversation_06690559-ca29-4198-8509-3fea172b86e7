// 假设 request 函数的类型，实际中应根据 '/@/utils/service' 里的定义替换
type RequestFunction = (options: { url: string; method: string; params?: any }) => Promise<any>;

// 从指定路径导入 request 函数
import { request } from '/@/utils/service';

// 定义 url 前缀常量
export const urlPrefix: string = "/api/poweranalysis/faultSet";
export const urlModelPrefix: string = "/api/poweranalysis/faultSet";

// 定义获取自定义故障集的函数，并指定参数和返回值类型
export function getBulkQuery(params: any): Promise<any> {
  return request({
    url: '/api/pm/fault_group/bulk_query',
    method: "get",
    params,
  });
}