import { request } from '/@/utils/service';

// 定义 url 前缀
export const urlPrefix: string = '/api/pm/';

// 定义请求参数的通用类型
type RequestQuery = Record<string, any>;

// 定义请求返回值的通用类型，这里使用泛型，你可以根据实际情况替换
type RequestResponse<T = any> = Promise<T>;

// 母线
export function GetppResultBusList(query: RequestQuery): RequestResponse {
  return request({
    url: urlPrefix + 'pp_result/result_bus_bb',
    method: 'get',
    params: query
  });
}

export function GetppResultBusSaList(query: RequestQuery): RequestResponse {
  return request({
    url: urlPrefix + 'ppResultBus/resultBus_sa/',
    method: 'get',
    params: query
  });
}

export function GetppResultBusDispatchList(query: RequestQuery): RequestResponse {
  return request({
    url: urlPrefix + 'dispatcherFlow/cal_result_forbus/',
    method: 'get',
    params: query
  });
}

// 外网
export function GetppResultExtGridList(query: RequestQuery): RequestResponse {
  return request({
    url: urlPrefix + 'ppResultExtGrid',
    method: 'get',
    params: query
  });
}

// 线路
export function GetppResultLineList(query: RequestQuery): RequestResponse {
  return request({
    url: urlPrefix + 'pp_result/result_line_bb',
    method: 'get',
    params: query
  });
}

export function GetppResultLineSaList(query: RequestQuery): RequestResponse {
  return request({
    url: urlPrefix + 'ppResultLine/resultLine_sa/',
    method: 'get',
    params: query
  });
}

// 负荷
export function GetppResultLoadList(query: RequestQuery): RequestResponse {
  return request({
    url: urlPrefix + 'pp_result/result_load_bb',
    method: 'get',
    params: query
  });
}

export function GetppResultLoadSaList(query: RequestQuery): RequestResponse {
  return request({
    url: urlPrefix + 'ppResultLoad/resultLoad_sa/',
    method: 'get',
    params: query
  });
}

// 发电机
export function GetppResultSgenList(query: RequestQuery): RequestResponse {
  return request({
    url: urlPrefix + 'pp_result/result_sgen_bb',
    method: 'get',
    params: query
  });
}

export function GetppResultSgenSaList(query: RequestQuery): RequestResponse {
  return request({
    url: urlPrefix + 'ppResultSgen/resultSgen_sa/',
    method: 'get',
    params: query
  });
}

// 并联容抗
export function GetppResultShuntList(query: RequestQuery): RequestResponse {
  return request({
    url: urlPrefix + 'pp_result/result_shunt_bb',
    method: 'get',
    params: query
  });
}

export function GetppResultShuntSaList(query: RequestQuery): RequestResponse {
  return request({
    url: urlPrefix + 'ppResultShunt/resultShunt_sa/',
    method: 'get',
    params: query
  });
}

// 两绕变
export function GetppResultTrafoList(query: RequestQuery): RequestResponse {
  return request({
    url: urlPrefix + 'pp_result/result_trafo_bb',
    method: 'get',
    params: query
  });
}

export function GetppResultTrafoSaList(query: RequestQuery): RequestResponse {
  return request({
    url: urlPrefix + 'ppResultTrafo/resultTrafo_sa/',
    method: 'get',
    params: query
  });
}

// 三绕变
export function GetppResultTrafo3wList(query: RequestQuery): RequestResponse {
  return request({
    url: urlPrefix + 'pp_result/result_trafo3w_bb',
    method: 'get',
    params: query
  });
}

export function GetppResultTrafo3wSaList(query: RequestQuery): RequestResponse {
  return request({
    url: urlPrefix + 'ppResultTrafo3W/resultTrafo3W_sa/',
    method: 'get',
    params: query
  });
}
