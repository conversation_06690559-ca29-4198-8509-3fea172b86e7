<template>
	<div class="bg-[#f7f7f7] h-full flex flex-col overflow-hidden">
		<div class="parameterModificationDialogClass">
			<div class="parameterBox">
				<div class="tools">
					<div class="toolFlex">
						<div class="toolFlexItem">
							<div class="name">名称:</div>
							<el-input placeholder="请输入名称" v-model="searchValue" size="small" style="width: 200px;"
								clearable></el-input>
						</div>
						<div class="toolFlexItem">
							<div class="name">电压等级:</div>
							<el-select v-model="scheme" multiple filterable placeholder="请选择电压等级" size="mini" clearable>
								<el-option v-for="item in powerList" :key="item.mRID" :label="item.name"
									:value="item.mRID">
								</el-option>
							</el-select>
						</div>
						<div class="toolFlexItem">
							<div class="name">所属区域:</div>
							<el-select v-model="partition" multiple filterable placeholder="请选择所属区域" size="mini"
								clearable>
								<el-option v-for="item in partitionList" :key="item.mRID" :label="item.name"
									:value="item.mRID">
								</el-option>
							</el-select>
						</div>
						<div class="toolFlexItem">
							<div class="name">所属厂站:</div>
							<el-select v-model="substation" multiple filterable placeholder="请选择所属厂站" size="mini"
								clearable @visible-change="changeSubstation">
								<el-option v-for="item in substationList" :key="item.mRID" :label="item.name"
									:value="item.mRID">
								</el-option>
							</el-select>
						</div>
						<el-button class="calculate" type="primary" :icon="Search" size="mini" @click="searchTable">
							查询
						</el-button>
						<el-button class="calculate" :icon="Refresh" size="mini" @click="resetSearch">
							重置
						</el-button>
					</div>
				</div>
				<el-tabs v-model="activeName" @tab-change="handleClick">
					<el-tab-pane label="母线" name="ppResultBus">
						<div style="overflow-y:auto;box-sizing: border-box" v-if="activeName === 'ppResultBus'">
							<table-common :tableData="tableData" :height="tableHeight"
								:tableColumn="ppResultBusTableColumn" @sortChange="sorTableChange">
								<template #vm_actual="scope">
									{{scope.row.vn_kv && scope.row.vm_pu ? (scope.row.vn_kv * scope.row.vm_pu).toFixed(2): '' }}
								</template>
								<template #vm_pu="scope">
									{{  scope.row.vm_pu ? scope.row.vm_pu.toFixed(6): '' }}
								</template>
							</table-common>
						</div>
					</el-tab-pane>
					<el-tab-pane label="线路" name="ppResultLine">
						<div style="overflow-y:auto;box-sizing: border-box" v-if="activeName === 'ppResultLine'">
							<table-common :tableData="tableData" :height="tableHeight"
								:tableColumn="ppResultLineTableColumn" @sortChange="sorTableChange">
								<template #loading_percent="scope">
									<el-tag v-if="scope.row.loading_percent && scope.row.loading_percent > 80" type="danger" close-transition>{{
								parseFloat(scope.row.loading_percent).toFixed(2) }}</el-tag>
								</template>
							</table-common>
						</div>
					</el-tab-pane>
					<el-tab-pane label="负荷" name="ppResultLoad">
						<div style="overflow-y:auto;box-sizing: border-box" v-if="activeName === 'ppResultLoad'">
							<table-common :tableData="tableData" :height="tableHeight"
								:tableColumn="ppResultLoadTableColumn" @sortChange="sorTableChange">
							</table-common>
						</div>
					</el-tab-pane>
					<el-tab-pane label="发电机" name="ppResultSgen">
						<div style="overflow-y:auto;box-sizing: border-box" v-if="activeName === 'ppResultSgen'">
							<table-common :tableData="tableData" :height="tableHeight"
								:tableColumn="ppResultSgenTableColumn" @sortChange="sorTableChange">
							</table-common>
						</div>
					</el-tab-pane>
					<el-tab-pane label="并联容抗" name="ppResultShunt">
						<div style="overflow-y:auto;box-sizing: border-box" v-if="activeName === 'ppResultShunt'">
							<table-common :tableData="tableData" :height="tableHeight"
								:tableColumn="ppResultShuntTableColumn" @sortChange="sorTableChange">
							</table-common>
						</div>
					</el-tab-pane>
					<el-tab-pane label="两绕变" name="ppResultTrafo">
						<div style="overflow-y:auto;box-sizing: border-box" v-if="activeName === 'ppResultTrafo'">
							<table-common :tableData="tableData" :height="tableHeight"
								:tableColumn="ppResultTrafoTableColumn" @sortChange="sorTableChange">
								<template #loading_percent="scope">
									<el-tag v-if="scope.row.loading_percent && scope.row.loading_percent > 80" type="danger" close-transition>{{
								parseFloat(scope.row.loading_percent).toFixed(2) }}</el-tag>
								</template>
							</table-common>
						</div>
					</el-tab-pane>
					<el-tab-pane label="三绕变" name="ppResultTrafo3w">
						<div style="overflow-y:auto;box-sizing: border-box" v-if="activeName === 'ppResultTrafo3w'">
							<table-common :tableData="tableData" :height="tableHeight"
								:tableColumn="ppResultTrafo3wTableColumn" @sortChange="sorTableChange">
								<template #loading_percent="scope">
									<el-tag v-if="scope.row.loading_percent && scope.row.loading_percent > 80" type="danger" close-transition>{{
								parseFloat(scope.row.loading_percent).toFixed(2) }}</el-tag>
								</template>
							</table-common>
						</div>
					</el-tab-pane>
					<el-tab-pane label="外电网" name="ppResultExtGrid">
						<div style="overflow-y:auto;box-sizing: border-box" v-if="activeName === 'ppResultExtGrid'">
							<table-common :tableData="tableData" :height="tableHeight"
								:tableColumn="ppResultExtGridTableColumn">
							</table-common>
						</div>
					</el-tab-pane>
				</el-tabs>
				<div class="pagination" style="margin-top: 10px;">
					<el-pagination v-if="isPagination" background @size-change="handleSizeChange"
						@current-change="handleCurrentChange" :page-sizes="[30, 40, 60, 100]" :page-size="limit"
						layout="total, sizes, prev, pager, next, jumper" :total="total">
					</el-pagination>
				</div>

			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue';
import * as api from './ppApi';
import * as calculateApi from '../../FaultSet/Popup/Add/calculateApi';
import tableCommon from '/@/components/table-common/index.vue';
import { Search, Refresh } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router';
const route = useRoute();
const router = useRouter();
// 定义接口类型
interface PowerItem {
	mRID: string | number;
	name: string;
}

interface TableColumn {
	label: string;
	prop: string;
	minWidth: string | number;
	fixed?: boolean;
	isFixed?: number;
	sortable?: 'custom' | boolean;
	slotName?: string;
}

// 响应式数据
const loading = ref(false);
const activeName = ref('ppResultBus');
const searchValue = ref('');
const tableData = ref<any[]>([]);
const total = ref(0);
const page = ref(1);
const limit = ref(30);
const isPagination = ref(true);
const bb_case_id = ref(0);
const tableHeight = ref(550);
const scheme = ref<(string | number)[]>([]);
const powerList = ref<PowerItem[]>([]);
const partition = ref<(string | number)[]>([]);
const partitionList = ref<PowerItem[]>([]);
const modelId = ref('');
const substation = ref<(string | number)[]>([]);
const substationList = ref<PowerItem[]>([]);
const isHeaderShow = ref(true);
const ordering = ref('');

const ppResultBusTableColumn: TableColumn[] = [
	{
		label: '母线名称',
		prop: 'bus_name',
		minWidth: '200',
		fixed: true
	},
	{
		label: '所属厂站',
		prop: 'substation_name',
		minWidth: '150',
		fixed: true
	},
	{
		label: '母线基准电压(kV)',
		prop: 'vn_kv',
		minWidth: '140'
	},
	{
		label: '相角',
		prop: 'va_degree',
		minWidth: '100',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '电压有名值',
		prop: 'vm_actual',
		minWidth: '120',
		slotName: 'vm_actual',
		isFixed: 2
	},
	{
		label: '电压幅值 (p.u)',
		prop: 'vm_pu',
		minWidth: '120',
		slotName: 'vm_pu',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '有功功率',
		prop: 'p_mw',
		minWidth: '100',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '无功功率',
		prop: 'q_mvar',
		minWidth: '100',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '母线设备ID',
		prop: 'bus_id',
		minWidth: '150'
	}
	// 其他列保持不变，此处省略...
];

const ppResultLineTableColumn: TableColumn[] = [
	{
		label: '线路名称',
		prop: 'line_name',
		minWidth: '100',
		fixed: true
	},
	{
		label: '首端母线',
		prop: 'from_bus_name',
		minWidth: '120',
		fixed: true
	},
	{
		label: '首端厂站',
		prop: 'from_substation',
		minWidth: '100',
		fixed: true
	},
	{
		label: '末端母线',
		prop: 'to_bus_name',
		minWidth: '120',
		fixed: true
	},
	{
		label: '末端厂站',
		prop: 'to_substation',
		minWidth: '100',
		fixed: true
	},
	{
		label: '负载率',
		prop: 'loading_percent',
		minWidth: '80',
		slotName: 'loading_percent',
		isFixed: 2
	},
	{
		label: '最大电流',
		prop: 'max_i_ka',
		minWidth: '80',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '首端有功',
		prop: 'p_from_mw',
		minWidth: '80',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '末端有功',
		prop: 'p_to_mw',
		minWidth: '80',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '有功损耗',
		prop: 'pl_mw',
		minWidth: '80',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '首端无功',
		prop: 'q_from_mvar',
		minWidth: '80',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '末端无功',
		prop: 'q_to_mvar',
		minWidth: '80',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '无功消耗',
		prop: 'ql_mvar',
		minWidth: '80',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '首端电流',
		prop: 'i_from_ka',
		minWidth: '80',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '末端电流',
		prop: 'i_to_ka',
		minWidth: '80',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '电流',
		prop: 'i_ka',
		minWidth: '80',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '首端电压',
		prop: 'vm_from_pu',
		minWidth: '80',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '首端电压相角',
		prop: 'va_from_degree',
		minWidth: '100',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '末端电压',
		prop: 'vm_to_pu',
		minWidth: '80',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '末端电压相角',
		prop: 'va_to_degree',
		minWidth: '100',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '首端母线ID',
		prop: 'from_bus',
		minWidth: '100'
	},
	{
		label: '末端母线ID',
		prop: 'to_bus',
		minWidth: '100'
	}
]
const ppResultLoadTableColumn: TableColumn[] = [
	{
		label: '负荷名称',
		prop: 'load_name',
		minWidth: '200',
		fixed: true
	},
	{
		label: '负荷所在母线',
		prop: 'bus_name',
		minWidth: '140'
	},
	{
		label: '所属厂站',
		prop: 'substation_name',
		minWidth: '140'
	},
	{
		label: '有功功率',
		prop: 'p_mw',
		minWidth: '100',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '无功功率',
		prop: 'q_mvar',
		minWidth: '100',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '负荷所在母线ID',
		prop: 'bus',
		minWidth: '140'
	}
]
const ppResultSgenTableColumn: TableColumn[] = [
	{
		label: '发电机名称',
		prop: 'sgen_name',
		minWidth: '200',
		fixed: true
	},
	{
		label: '发电机所在母线',
		prop: 'bus_name',
		minWidth: '140'
	},
	{
		label: '所属厂站',
		prop: 'substation_name',
		minWidth: '140'
	},
	{
		label: '有功功率',
		prop: 'p_mw',
		minWidth: '100',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '无功功率',
		prop: 'q_mvar',
		minWidth: '100',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '发电机所在母线ID',
		prop: 'bus',
		minWidth: '140'
	}
]
const ppResultShuntTableColumn: TableColumn[] = [
	{
		label: '并联容抗名称',
		prop: 'shunt_name',
		minWidth: '200',
		fixed: true
	},
	{
		label: '所在母线',
		prop: 'bus_name',
		minWidth: '140'
	},
	{
		label: '所属厂站',
		prop: 'substation_name',
		minWidth: '140'
	},
	{
		label: '无功功率',
		prop: 'q_mvar',
		minWidth: '100',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '所在母线ID',
		prop: 'bus',
		minWidth: '140'
	}
]
const ppResultTrafoTableColumn: TableColumn[] = [
	{
		label: '主变名称',
		prop: 'trafo_name',
		minWidth: '100',
		fixed: true
	},
	{
		label: '所属厂站',
		prop: 'substation_name',
		minWidth: '140'
	},
	{
		label: '高压侧所在母线',
		prop: 'hv_bus_name',
		minWidth: '140'
	},
	{
		label: '低压侧所在母线',
		prop: 'lv_bus_name',
		minWidth: '140'
	},
	{
		label: '负载率',
		prop: 'loading_percent',
		minWidth: '120',
		slotName: 'loading_percent',
		isFixed: 2
	},
	{
		label: '高压侧有功',
		prop: 'p_hv_mw',
		minWidth: '90',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '低压侧有功',
		prop: 'p_lv_mw',
		minWidth: '90',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '有功损耗',
		prop: 'pl_mw',
		minWidth: '80',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '高压侧无功',
		prop: 'q_hv_mvar',
		minWidth: '90',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '低压侧无功',
		prop: 'q_lv_mvar',
		minWidth: '90',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '无功消耗',
		prop: 'ql_mvar',
		minWidth: '80',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '容量',
		prop: 'sn_mva',
		minWidth: '60',
		sortable: 'custom'
	},
	{
		label: '高压侧额定电压(KV)',
		prop: 'vn_hv_kv',
		minWidth: '140'
	},
	{
		label: '低压侧额定电压(KV)',
		prop: 'vn_lv_kv',
		minWidth: '140'
	},
	{
		label: '高压侧电流',
		prop: 'i_hv_ka',
		minWidth: '90',
		isFixed: 2
	},
	{
		label: '低压侧电流',
		prop: 'i_lv_ka',
		minWidth: '90',
		isFixed: 2
	},
	{
		label: '高压侧电压',
		prop: 'vm_hv_pu',
		minWidth: '90',
		isFixed: 2
	},
	{
		label: '高压侧电压相角',
		prop: 'va_hv_degree',
		minWidth: '120',
		isFixed: 2
	},
	{
		label: '低压侧电压',
		prop: 'vm_lv_pu',
		minWidth: '90',
		isFixed: 2
	},
	{
		label: '低压侧电压相角',
		prop: 'va_lv_degree',
		minWidth: '120',
		isFixed: 2
	},
	{
		label: '高压侧所在母线ID',
		prop: 'hv_bus',
		minWidth: '130'
	},
	{
		label: '低压侧所在母线ID',
		prop: 'lv_bus',
		minWidth: '130'
	}
]
const ppResultTrafo3wTableColumn: TableColumn[] = [
	{
		label: '主变名称',
		prop: 'trafo3w_name',
		minWidth: '100',
		fixed: true
	},
	{
		label: '所属厂站',
		prop: 'substation_name',
		minWidth: '140'
	},
	{
		label: '高压侧所在母线',
		prop: 'hv_bus_name',
		minWidth: '140'
	},
	{
		label: '中压侧所在母线',
		prop: 'mv_bus_name',
		minWidth: '140'
	},
	{
		label: '低压侧所在母线',
		prop: 'lv_bus_name',
		minWidth: '140'
	},
	{
		label: '负载率',
		prop: 'loading_percent',
		minWidth: '120',
		slotName: 'loading_percent',
		isFixed: 2
	},
	{
		label: '高压侧有功',
		prop: 'p_hv_mw',
		minWidth: '90',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '中压侧有功',
		prop: 'p_mv_mw',
		minWidth: '90',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '低压侧有功',
		prop: 'p_lv_mw',
		minWidth: '90',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '有功损耗',
		prop: 'pl_mw',
		minWidth: '80',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '高压侧无功',
		prop: 'q_hv_mvar',
		minWidth: '90',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '中压侧无功',
		prop: 'q_mv_mvar',
		minWidth: '90',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '低压侧无功',
		prop: 'q_lv_mvar',
		minWidth: '90',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '无功消耗',
		prop: 'ql_mvar',
		minWidth: '80',
		isFixed: 2,
		sortable: 'custom'
	},
	{
		label: '高压侧容量',
		prop: 'sn_hv_mva',
		minWidth: '90'
	},
	{
		label: '中压侧容量',
		prop: 'sn_mv_mva',
		minWidth: '90'
	},
	{
		label: '低压侧容量',
		prop: 'sn_lv_mva',
		minWidth: '90'
	},
	{
		label: '高压侧额定电压(KV)',
		prop: 'vn_hv_kv',
		minWidth: '120'
	},
	{
		label: '中压侧额定电压(KV)',
		prop: 'vn_mv_kv',
		minWidth: '120'
	},
	{
		label: '低压侧额定电压(KV)',
		prop: 'vn_lv_kv',
		minWidth: '120'
	},
	{
		label: '高压侧电流',
		prop: 'i_hv_ka',
		minWidth: '90',
		isFixed: 2
	},
	{
		label: '中压侧电流',
		prop: 'i_mv_ka',
		minWidth: '90',
		isFixed: 2
	},
	{
		label: '低压侧电流',
		prop: 'i_lv_ka',
		minWidth: '90',
		isFixed: 2
	},
	{
		label: '高压侧电压',
		prop: 'vm_hv_pu',
		minWidth: '90',
		isFixed: 2
	},
	{
		label: '高压侧电压相角',
		prop: 'va_hv_degree',
		minWidth: '120',
		isFixed: 2
	},
	{
		label: '中压侧电压',
		prop: 'vm_mv_pu',
		minWidth: '90',
		isFixed: 2
	},
	{
		label: '中压侧电压相角',
		prop: 'va_mv_degree',
		minWidth: '120',
		isFixed: 2
	},
	{
		label: '低压侧电压',
		prop: 'vm_lv_pu',
		minWidth: '90',
		isFixed: 2
	},
	{
		label: '低压侧电压相角',
		prop: 'va_lv_degree',
		minWidth: '120',
		isFixed: 2
	},
	{
		label: '高压侧所在母线ID',
		prop: 'hv_bus',
		minWidth: '130'
	},
	{
		label: '中压侧所在母线ID',
		prop: 'mv_bus',
		minWidth: '130'
	},
	{
		label: '低压侧所在母线ID',
		prop: 'lv_bus',
		minWidth: '130'
	}
]
const ppResultExtGridTableColumn: TableColumn[] = [
	{
		label: '外电网名称',
		prop: 'ext_grid_name',
		minWidth: '200',
		fixed: true
	},
	{
		label: '有功功率',
		prop: 'p_mw',
		minWidth: '100',
		isFixed: 2,
		sortable: true
	},
	{
		label: '无功功率',
		prop: 'q_mvar',
		minWidth: '100',
		isFixed: 2,
		sortable: true
	}
]

// 初始化方法
const init = () => {
	tableHeight.value = window.innerHeight - 250;
	bb_case_id.value = route.query.id;
	handleTypeClick();
	getPowerList();
	getPartitionList();
	const handleResize = () => {
		tableHeight.value = window.innerHeight - 250;
	};

	window.addEventListener('resize', handleResize);

	// 组件卸载时移除事件监听器
	return () => {
		window.removeEventListener('resize', handleResize);
	};
};

// 标签点击事件
const handleClick = (val: string) => {
	isPagination.value = false;
	page.value = 1;
	limit.value = 30;
	ordering.value = '';
	searchValue.value = '';
	getList();
	setTimeout(() => {
		isPagination.value = true;
	}, 200);
};

// 获取列表数据
const getList = async () => {
	loading.value = true;
	tableData.value = [];
	let queryAll: any = {};
	queryAll = {
		page: page.value,
		limit: limit.value,
		bb_case_id: bb_case_id.value,
		cime_basevoltage_id: scheme.value.toString(),
		cime_controlarea_id: partition.value.toString(),
		cime_substation_id: substation.value.toString(),
		ordering: ordering.value
	};

	const getListByType = async (type: string, query: any) => {
		try {
			let res;
			switch (type) {
				case 'ppResultBus':
					res = await api.GetppResultBusList(query);
					break;
				case 'ppResultExtGrid':
					res = await api.GetppResultExtGridList(query);
					break;
				case 'ppResultLine':
					res = await api.GetppResultLineList(query);
					break;
				case 'ppResultLoad':
					res = await api.GetppResultLoadList(query);
					break;
				case 'ppResultSgen':
					res = await api.GetppResultSgenList(query);
					break;
				case 'ppResultShunt':
					res = await api.GetppResultShuntList(query);
					break;
				case 'ppResultTrafo':
					res = await api.GetppResultTrafoList(query);
					break;
				case 'ppResultTrafo3w':
					res = await api.GetppResultTrafo3wList(query);
					break;
				default:
					res = await api.GetppResultBusList(query);
					break;
			}
			loading.value = false;
			tableData.value = res.data;
			page.value = res.page;
			limit.value = res.limit;
			total.value = res.total;
		} catch (error) {
			loading.value = false;
		}
	};

	let query: any;
	switch (activeName.value) {
		case 'ppResultBus':
			query = {
				bus_name: searchValue.value,
				...queryAll
			};
			break;
		case 'ppResultExtGrid':
			query = {
				ext_grid_name: searchValue.value,
				...queryAll
			};
			break;
		case 'ppResultLine':
			query = {
				line_name: searchValue.value,
				...queryAll
			};
			break;
		case 'ppResultLoad':
			query = {
				load_name: searchValue.value,
				...queryAll
			};
			break;
		case 'ppResultSgen':
			query = {
				sgen_name: searchValue.value,
				...queryAll
			};
			break;
		case 'ppResultShunt':
			query = {
				shunt_name: searchValue.value,
				...queryAll
			};
			break;
		case 'ppResultTrafo':
			query = {
				trafo_name: searchValue.value,
				...queryAll
			};
			break;
		case 'ppResultTrafo3w':
			query = {
				trafo3w_name: searchValue.value,
				...queryAll
			};
			break;
		default:
			query = {
				bus_name: searchValue.value,
				...queryAll
			};
			break;
	}

	await getListByType(activeName.value, query);
};

// 分页大小改变
const handleSizeChange = (val: number) => {
	page.value = 1;
	limit.value = val;
	getList();
};

// 当前页码改变
const handleCurrentChange = (val: number) => {
	page.value = val;
	getList();
};

// 搜索表格
const searchTable = () => {
	page.value = 1;
	getList();
};

// 重置搜索条件
const resetSearch = () => {
	searchValue.value = '';
	scheme.value = [];
	partition.value = [];
	substation.value = [];
	getList();
};

// 获取电压等级列表
const getPowerList = async () => {
	loading.value = true;
	const query = {
		bb_case_id: bb_case_id.value
	};
	try {
		const res = await calculateApi.getBasevoltage(query);
		loading.value = false;
		powerList.value = res.data;
	} catch (error) {
		loading.value = false;
	}
};

// 获取所属区域列表
const getPartitionList = async () => {
	loading.value = true;
	const query = {
		bb_case_id: bb_case_id.value
	};
	try {
		const res = await calculateApi.getCimeControl(query);
		loading.value = false;
		partitionList.value = res.data;
	} catch (error) {
		loading.value = false;
	}
};

// 获取所属厂站列表
const getSubstation = async () => {
	loading.value = true;
	const query = {
		bb_case_id: bb_case_id.value,
		cime_basevoltage_id: scheme.value.toString(),
		cime_controlarea_id: partition.value.toString()
	};
	try {
		const res = await calculateApi.getCimeSubstation(query);
		loading.value = false;
		substationList.value = res.data;
	} catch (error) {
		loading.value = false;
	}
};

// 厂站选择框可见性改变
const changeSubstation = (flag: boolean) => {
	if (flag) {
		getSubstation();
	}
};

// 切换类型
const handleTypeClick = () => {
	isHeaderShow.value = false;
	isPagination.value = false;
	page.value = 1;
	limit.value = 30;
	setTimeout(() => {
		isPagination.value = true;
	}, 200);
	searchValue.value = '';
	scheme.value = [];
	partition.value = [];
	substation.value = [];
	activeName.value = 'ppResultBus';
	getList();
};

// 表格排序改变
const sorTableChange = (column: any) => {
	if (column.prop === null || column.order === null) {
		ordering.value = '';
	} else {
		if (column.order === 'ascending') {
			ordering.value = column.prop;
		} else {
			ordering.value = `-${column.prop}`;
		}
	}
	getList();
};
onMounted(() => {
	init();
});

</script>

<style scoped lang="scss">
.parameterModificationDialogClass {
	padding: 20px 20px 0 20px;
	.parameterBox {
		.tools {
			display: flex;
			justify-content: flex-end;

			.toolFlex {
				display: flex;
				align-items: center;

				.toolFlexItem {
					display: flex;
					align-items: center;
					padding-left: 10px;

					.name {
						padding-right: 8px;
						min-width: 80px;
					}
				}
			}

			.fa {
				padding-right: 10px;
			}

			.calculate {
				margin-left: 10px;
			}
		}

		.paginationStyle {
			padding: 30px 0;
			text-align: right;
		}
	}
}
</style>
