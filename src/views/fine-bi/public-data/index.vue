<template>
	<div class="w-full h-full" v-loading="loading" element-loading-text="页面加载中..." element-loading-background="rgba(255, 255, 255, 1)">
		<iframe v-if="!iframeSrc" :src="cookieUrl" style="display: none"></iframe>
		<iframe v-else :src="iframeSrc" width="100%" height="100%" frameborder="0" @load="handleMainIframeLoaded"></iframe>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from 'vue';
import axios from 'axios';

const BI_SERVICE = import.meta.env.VITE_BI_SERVICE;

const iframeSrc = ref('');
const cookieUrl = ref(encodeURI(`${BI_SERVICE}/webroot/decision/login/cross/domain?fine_username=admin&fine_password=eJl0n4Aw0m$cfHpU&validity=-2`));
const cookieIframeLoaded = ref(false);
const mainIframeLoaded = ref(false);
const isLoading = ref(true); // 控制整体加载状态

// 计算总体loading状态 - 添加isLoading状态确保过程连续
const loading = computed(() => {
	return isLoading.value || !cookieIframeLoaded.value || (iframeSrc.value && !mainIframeLoaded.value);
});

// 主iframe加载完成处理
const handleMainIframeLoaded = () => {
	if (iframeSrc.value) {
		mainIframeLoaded.value = true;
		// 确保视觉上流畅，延迟一小段时间后再完全结束loading
		setTimeout(() => {
			isLoading.value = false;
		}, 300);
	}
};

const fetchAuthToken = async () => {
	try {
		const finebiUrl = '/finebi-login';
		const params = {
			fine_username: 'admin',
			fine_password: 'eJl0n4Aw0m$cfHpU',
			validity: '-2',
			callback: '',
		};

		const response = await axios.get(finebiUrl, { params });

		if (response.status === 200) {
			const jsonStr = response.data
				.trim()
				.replace(/^callback\(/, '')
				.replace(/\)$/, '');
			const data = JSON.parse(jsonStr);
			const accessToken = data.accessToken;

			if (accessToken) {
				// 设置新的iframe src之前保持loading状态
				mainIframeLoaded.value = false;
				await nextTick();
				iframeSrc.value = `${BI_SERVICE}/webroot/decision/v5/api/conf/page?fine_auth_token=${accessToken}`;

				// 添加超时保护，防止iframe无法加载导致loading一直存在
				setTimeout(() => {
					if (!mainIframeLoaded.value) {
						mainIframeLoaded.value = true;
						isLoading.value = false;
						console.warn('Iframe loading timeout, force ended loading state');
					}
				}, 15000);

				return true;
			} else {
				console.error('未获取到 token');
				isLoading.value = false;
				return false;
			}
		} else {
			console.error('请求失败，状态码：', response.status);
			isLoading.value = false;
			return false;
		}
	} catch (error) {
		console.error('获取 token 时发生错误:', error);
		isLoading.value = false;
		return false;
	}
};

const initCookieIframe = () => {
	const iframe = document.createElement('iframe');
	iframe.src = cookieUrl.value;
	iframe.style.display = 'none';
	document.body.appendChild(iframe);

	iframe.onload = () => {
		cookieIframeLoaded.value = true;
		document.body.removeChild(iframe);
	};

	// 添加超时保护，防止cookie iframe加载失败
	setTimeout(() => {
		if (!cookieIframeLoaded.value) {
			cookieIframeLoaded.value = true;
			document.body.removeChild(iframe);
			console.warn('Cookie iframe loading timeout, force ended loading state');
		}
	}, 10000);
};

onMounted(async () => {
	isLoading.value = true;
	initCookieIframe();
	await new Promise((resolve) => {
		const check = () => {
			if (cookieIframeLoaded.value) resolve(true);
			else setTimeout(check, 100);
		};
		check();
	});
	mainIframeLoaded.value = false; // 重置主iframe加载状态
	await fetchAuthToken();
});
</script>
