<template>
	<div class="bg-white h-full px-6 py-5">
		<div class="grid grid-cols-4 gap-4 mt-4">
			<el-card shadow="hover" class="flex justify-center items-center cursor-pointer h-[200px]" @click="addMarketDesign">
				<div class="flex justify-center items-center h-full">
					<el-icon class="mr-3"><Plus /></el-icon>
					新增规则参数
				</div>
			</el-card>
			<el-card class="market-design-card" shadow="hover" v-for="item in list">
				<template #header>
					<div class="font-bold text-[20px]">{{ item.name }}</div>
				</template>

				<div class="grid grid-cols-[80px,1fr] gap-y-2 py-2">
					<div>创建时间</div>
					<el-text tag="div">{{ item.create_datetime }}</el-text>
					<div>创建人</div>
					<el-text tag="div">{{ item.creator_name }}</el-text>
					<div>规则说明</div>
					<el-text tag="div" class="h-[42px]" line-clamp="2">{{ item.details }}</el-text>
				</div>
				<div class="grid grid-cols-4 gap-2 justify-center">
					<div class="flex justify-center py-2 cursor-pointer hover:text-[#3e8cf2]" @click="editMarketDesign(item.id)">
						<el-icon :size="18"><Edit /></el-icon>
					</div>
					<div class="flex justify-center py-2 cursor-pointer hover:text-[#3e8cf2]" @click="handleDelete(item)">
						<el-icon :size="18"><Delete /></el-icon>
					</div>
					<div class="flex justify-center py-2 cursor-pointer hover:text-[#3e8cf2]" @click="handleCopy(item)">
						<el-icon :size="18"><CopyDocument /></el-icon>
					</div>
					<div class="flex justify-center py-2 cursor-pointer hover:text-[#3e8cf2]" @click="handleSetParams(item)">
						<el-icon :size="18"><Setting /></el-icon>
					</div>
				</div>
			</el-card>
		</div>
		<el-dialog v-model="dialogVisible" :title="formParams.type === 'add' ? '新增规则' : '编辑规则'" width="500">
			<Form :schema="schema" @register="formRegister" />

			<template #footer>
				<el-button type="primary" @click="formSubmit" :loading="submitLoading">确定</el-button>
				<el-button @click="dialogVisible = false">取消</el-button>
			</template>
		</el-dialog>
		<market-rule-params :ruleId="formParams.id" v-model="drawerVisible" />
	</div>
</template>

<script setup lang="ts">
import { Form, type FormSchema } from '/@//components/Form';
import { useForm } from '/@/components/Form/useForm';
import { ref, reactive, nextTick } from 'vue';
import { Plus, Setting, Delete, Edit, CopyDocument } from '@element-plus/icons-vue';
import { AddMarketRule, GetList, GetMarketRule, UpdateMarketRule, DelMarketRule, CopyMarketRule, type TaskDetails } from '/@/views/market-design/api';
import { successNotification } from '/@/utils/message';
import MarketRuleParams from './MarketRuleParams.vue';
const drawerVisible = ref(false);
const handleSetParams = ({ id }: TaskDetails) => {
	formParams.id = id;
	drawerVisible.value = true;
};
const list = ref<TaskDetails[]>([]);
const getDataList = async () => {
	const { data } = await GetList();
	list.value = data;
};
getDataList();
import { ElMessageBox } from 'element-plus';
const handleDelete = ({ id, name }: TaskDetails) => {
	ElMessageBox.confirm(`您确认删除：${name} 吗?`, '温馨提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		const res = await DelMarketRule(id);
		if (res?.code === 2000) {
			successNotification(res.msg as string);
			getDataList();
		}
	});
};
const dialogVisible = ref(false);
const formParams = reactive<{ id: number; type: 'add' | 'edit' }>({
	id: 0,
	type: 'add',
});
const schema = reactive<FormSchema[]>([
	{
		field: 'name',
		label: '规则名称',
		component: 'Input',
		colProps: { span: 24 },
		formItemProps: {
			rules: [{ required: true, message: '请输入规则名称' }],
		},
	},
	{
		field: 'details',
		label: '规则说明',
		component: 'Input',
		colProps: { span: 24 },
		formItemProps: {
			rules: [{ required: true, message: '请输入规则名称' }],
		},
		componentProps: { type: 'textarea' },
	},
]);
const addMarketDesign = () => {
	formParams.id = 0;
	formParams.type = 'add';
	dialogVisible.value = true;
	nextTick(async () => {
		const elFormExpose = await getElFormExpose();
		elFormExpose?.resetFields();
	});
};
const editMarketDesign = async (id: number) => {
	formParams.id = id;
	formParams.type = 'edit';
	const { data } = await GetMarketRule(id);
	dialogVisible.value = true;
	nextTick(() => {
		setValues(data);
	});
};

const handleCopy = async (item: TaskDetails) => {
	ElMessageBox.confirm(`您确认要复制：${item.name} 吗?`, '温馨提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		const res = await CopyMarketRule(item);
		if (res?.code === 2000) {
			successNotification(res.msg as string);
			getDataList();
		}
	});
};

const { formRegister, formMethods } = useForm();
const { getElFormExpose, getFormData, setValues } = formMethods;
const submitLoading = ref(false);
const formSubmit = async () => {
	const elFormExpose = await getElFormExpose();
	elFormExpose?.validate(async (valid) => {
		if (valid) {
			const params = await getFormData();
			try {
				let res;
				submitLoading.value = true;
				if (formParams.type === 'add') {
					res = await AddMarketRule(params);
				} else {
					res = await UpdateMarketRule(params);
				}
				if (res?.code === 2000) {
					successNotification(res.msg as string);
					dialogVisible.value = false;
					getDataList();
				}
			} finally {
				submitLoading.value = false;
			}
		}
	});
};
</script>
<style lang="scss" scoped>
:deep(.market-design-card) {
	.el-card__header,
	.el-card__body,
	.el-card__footer {
		padding: 6px 12px;
	}
}
</style>
