<template>
	<el-dialog :close-on-click-modal="false" v-model="drawerVisible" @open="OpenDrawer" title="设置规则参数" width="660">
		<Form class="m-2.5" :schema="paramsSchema" @register="formRegister" />
		<template #footer>
			<el-button type="primary" @click="formSubmit" :loading="submitLoading">确定</el-button>
			<el-button @click="drawerVisible = false">取消</el-button>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { Form, type FormSchema } from '/@//components/Form';
import { useForm } from '/@/components/Form/useForm';
import { ref, reactive, nextTick, onMounted } from 'vue';
import { SetRuleDetail, GetAllParams, SetParams } from '/@/views/market-design/api';
import { dictionary } from '/@/utils/dictionary';
import { successNotification } from '/@/utils/message';
const drawerVisible = defineModel<boolean>({ default: false });
const paramsSchema = reactive<FormSchema[]>([]);
const { formRegister, formMethods } = useForm();
const { getElFormExpose, getFormData, setValues, addSchema } = formMethods;
// radio select input inputNumber
const elementMap: Recordable = {
	radio: 'RadioGroup',
	inputNumber: 'InputNumber',
	select: 'Select',
};
const props = defineProps<{ ruleId: number }>();
onMounted(async () => {
	const { data: params, code: code1 } = await GetAllParams();
	const columns = groupByTradingInstrumentType(params);
	paramsSchema.length = 0;
	columns.forEach((item, index) => {
		paramsSchema.push({
			field: `c${index}`,
			label: item.typeName,
			component: 'Divider',
		});
		const { list } = item;
		list.forEach((dt: Recordable) => {
			const schema = {
				field: `${dt.code}`,
				label: dt.name,
				component: elementMap[dt.value_type] || 'Input',
				colProps: { span: 24 },
				componentProps: {
					controlsPosition: dt.value_type === 'inputNumber' ? 'right' : undefined,
					options: dt.dict_key ? dictionary(dt.dict_key) : undefined,
				},
				value: dt.default_value,
				formItemProps: {
					rules: [{ required: true, message: '该项为必填项' }],
				},
			};
			paramsSchema.push(schema);
		});
	});
});
const OpenDrawer = async () => {
	const {
		data: { param_values = [] },
		code,
	} = await SetRuleDetail(props.ruleId);
	if (code === 2000) {
		nextTick(async () => {
			const elFormExpose = await getElFormExpose();
			elFormExpose?.resetFields();
			const initValue: Recordable = {};
			param_values.forEach((item: Recordable) => {
				initValue[item.param_code] = item.value;
			});
			setValues(initValue);
		});
	}
};
const submitLoading = ref(false);
const emit = defineEmits(['success']);
const formSubmit = async () => {
	const elFormExpose = await getElFormExpose();
	elFormExpose?.validate(async (valid) => {
		if (valid) {
			const params = await getFormData();
			try {
				submitLoading.value = true;
				const data: Recordable[] = [];
				Object.keys(params).forEach((key) => {
					data.push({
						code: key,
						value: params[key],
					});
				});
				const res = await SetParams(props.ruleId, data);
				if (res?.code === 2000) {
					successNotification(res.msg as string);
					drawerVisible.value = false;
					emit('success');
				}
			} finally {
				submitLoading.value = false;
			}
		}
	});
};
const typeName: Recordable = {
	1: '日前市场',
	2: '实时市场',
};
function groupByTradingInstrumentType(data: Recordable[]) {
	const groupedData: Recordable = {};

	data.forEach((item) => {
		const type = item.trading_instrument_type;

		// 如果类型不存在，则初始化一个数组
		if (!groupedData[type]) {
			groupedData[type] = { type: type, typeName: typeName[type], list: [] };
		}

		// 将当前项添加到对应类型的列表中
		groupedData[type].list.push(item);
	});

	// 将对象转换成数组形式
	return Object.values(groupedData);
}
</script>

<style lang="scss" scoped></style>
