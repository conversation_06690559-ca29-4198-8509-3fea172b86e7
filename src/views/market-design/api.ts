import { request } from '/@/utils/service';
import { AddReq, EditReq, InfoReq } from '@fast-crud/fast-crud';
export interface TaskDetails {
	create_datetime: string; // 创建时间，格式为 "YYYY-MM-DD HH:mm:ss"
	creator: number; // 创建者ID
	creator_name: string; // 创建者名称
	dept_belong_id: string; // 所属部门ID
	description: string | null; // 任务描述，可能为null
	details: string; // 任务详情
	id: number; // 任务ID
	modifier: string; // 修改者ID
	modifier_name: string; // 修改者名称
	name: string; // 任务名称
	update_datetime: string; // 更新时间，格式为 "YYYY-MM-DD HH:mm:ss"
}

export const apiPrefix = '/api/pm/market_rule/';
export function GetList() {
	return request({
		url: apiPrefix,
		method: 'get',
	});
}
export function GetMarketRule(id: number) {
	return request({
		url: apiPrefix + id,
		method: 'get',
	});
}

export function AddMarketRule(MarketRule: AddReq) {
	return request({
		url: apiPrefix,
		method: 'post',
		data: MarketRule,
	});
}

export function UpdateMarketRule(MarketRule: EditReq) {
	return request({
		url: apiPrefix + MarketRule.id + '/',
		method: 'put',
		data: MarketRule,
	});
}

export function DelMarketRule(id: number) {
	return request({
		url: apiPrefix + id + '/',
		method: 'delete',
		data: { id },
	});
}

export function CopyMarketRule({ id, name, details }: InfoReq) {
	return request({
		url: apiPrefix + id + '/copy/',
		method: 'post',
		data: {
			rule_id: id,
			name,
			details,
		},
	});
}

export function SetRuleDetail(id: number) {
	return request({
		url: apiPrefix + id + '/details/',
		method: 'post',
		data: {
			id,
		},
	});
}

export function GetAllParams() {
	return request({
		url: apiPrefix + 'get_all_params/',
		method: 'get',
	});
}

export function SetParams(id: number, params: Recordable[]) {
	return request({
		url: apiPrefix + id + '/set_params/',
		method: 'post',
		data: { params },
	});
}
