<template>
	<div class="flex flex-col h-full">
		<div class="flex flex-1 h-0 overflow-hidden">
			<KeepAlive>
				<component :mode-type="modeType" :is="componentName" :key="modeType"></component>
			</KeepAlive>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { type SiMu } from '/@/views/config/api';
import DayAheadMarketTree from './DayAheadMarketTree.vue';
import { useRoute } from 'vue-router';
const route = useRoute();

const modeType = computed(() => {
	return ['user-day-ahead-market', 'unit-day-ahead-market'].includes(route.query.page as string)
		? (route.query.page as 'user-day-ahead-market' | 'unit-day-ahead-market')
		: 'user-day-ahead-market';
});

const componentName = computed(() => DayAheadMarketTree);

const { siMuInfo } = defineProps<{
	siMuInfo: SiMu;
}>();
</script>
