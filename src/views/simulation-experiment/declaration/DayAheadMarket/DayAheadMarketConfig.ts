export const bb_unit_offer_energy = {
	title: '机组',
	api: {
		tree: '/api/pm/bb_sgen/build_unit_classification/?classification=MA',
		list: '/api/pm/bb_unit_offer_energy/query_list/',
		save: '/api/pm/bb_unit_offer_energy/batch_create_update/',
		init: '/api/pm/bb_unit_offer_energy/init_declaration/',
		cancel: '/api/pm/bb_unit_offer_energy/cancel_declaration/',
	},
	columns: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '机组ID',
			field: 'unit_id',
			filter: false,
			sortable: false,
		},
		{
			headerName: '时段',
			field: 'period_id',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '段序号',
			field: 'segment_order',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '分段起始容量',
			field: 'power_range_dn',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '分段终止容量',
			field: 'power_range_up',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '分段报价',
			field: 'price',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
	],
};
export const bb_user_bid_energy = {
	title: '用户',
	api: {
		tree: '/api/pm/bb_user_basic/build_user_classification/?classification=MA',
		list: '/api/pm/bb_user_bid_energy/query_list/',
		save: '/api/pm/bb_user_bid_energy/batch_create_update/',
		init: '/api/pm/bb_user_bid_energy/init_declaration/',
		cancel: '/api/pm/bb_user_bid_energy/cancel_declaration/',
	},
	columns: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '用户ID',
			field: 'user_id',
			filter: false,
			sortable: false,
		},
		{
			headerName: '时段',
			field: 'period_id',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '段序号',
			field: 'segment_order',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '分段起始电力',
			field: 'load_range_dn',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '分段终止电力',
			field: 'load_range_up',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '分段报价',
			field: 'price',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
	],
};

export default {
	'user-day-ahead-market': bb_user_bid_energy,
	'unit-day-ahead-market': bb_unit_offer_energy,
};
