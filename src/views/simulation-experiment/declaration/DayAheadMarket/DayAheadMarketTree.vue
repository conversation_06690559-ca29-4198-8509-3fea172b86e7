<template>
	<!-- 
		Split分割面板组件：将页面分为左右两部分
		- left-size="15"：左侧面板占15%宽度
		- 右侧面板自动占据剩余85%宽度
		- Split组件支持用户拖拽调整左右面板大小
		- 适用于树形导航 + 内容展示的经典布局
	-->
	<Split :left-size="15" class="rounded-md flex row-col h-full overflow-hidden">
		<!-- 左侧插槽：树形导航区域 -->
		<template #left>
			<!-- 
				左侧导航面板：
				- 固定宽度，包含标题、状态筛选、搜索框、树形组件
				- bg-white：白色背景
				- h-full：占满父容器高度
				- flex flex-col：垂直布局
				- p-2：内边距
			-->
			<div class="bg-white h-full flex flex-col p-2">
				<!-- 标题和状态筛选器 -->
				<div class="flex items-center justify-between">
					<!-- 动态标题：根据模式类型显示不同标题 -->
					<div class="text-lg font-bold">{{ config.title }}</div>

					<!-- 
						状态分段控制器：用于筛选申报状态
						- size="small"：小尺寸
						- options：筛选选项数组
						- v-model：双向绑定当前选中的状态
					-->
					<el-segmented size="small" v-model="status" :options="statusOptions" />
				</div>

				<!-- 
					树形组件容器：
					- flex-1：占据剩余空间
					- overflow-hidden：隐藏溢出内容
					- v-loading：显示加载状态
					- element-loading-text：加载提示文本
				-->
				<div
					ref="treeContainerRef"
					class="flex-1 flex flex-col overflow-hidden el-tree-bg-color"
					v-loading="loading"
					element-loading-text="数据加载中..."
				>
					<!-- 搜索框 -->
					<div class="mt-2">
						<el-input class="mb-2" v-model="searchValue" placeholder="搜索" :suffix-icon="Search" />
					</div>

					<!-- 
						虚拟化树形组件：
						- el-tree-v2：Element Plus的虚拟化树组件，适合大数据量
						- :height="treeHeight"：动态计算树的高度
						- :data="filteredTreeData"：过滤后的树数据
						- :props：字段映射配置
						- default-expand-all：默认展开所有节点
						- :expand-on-click-node="false"：点击节点不展开/收起
						- @node-click：节点点击事件
						- :filter-method：过滤方法
					-->
					<el-tree-v2
						class="tree-bg-color flex-1 flex flex-col"
						ref="treeRef"
						:height="treeHeight"
						:data="filteredTreeData"
						:props="treeFieldMapping"
						default-expand-all
						:expand-on-click-node="false"
						@node-click="handleNodeClick"
						:filter-method="filterMethod"
					>
						<!-- 自定义节点内容 -->
						<template #default="{ node, data }">
							<div
								class="flex group overflow-hidden justify-between items-center px-2 w-full relative"
								:class="selectedNodeId === data[treeFieldMapping.value] ? 'select-bg-color' : ''"
							>
								<!-- 节点标签：根据申报状态显示不同样式 -->
								<div
									class="flex items-center w-full"
									:class="{ 'text-gray-400': data[treeFieldMapping.isApplyed] === 0 && data[treeFieldMapping.value] !== '0' }"
								>
									{{ data[treeFieldMapping.label] }}
								</div>

								<!-- 节点操作按钮：只在选中且非"全部"节点时显示 -->
								<div
									class="flex items-center gap-1 absolute top-0 right-1"
									v-if="selectedNodeId === data[treeFieldMapping.value] && data[treeFieldMapping.value] !== '0'"
								>
									<!-- 
										操作按钮弹出框：
										- :width="300"：弹出框宽度
										- @show/@hide：显示/隐藏事件，用于控制按钮高亮
									-->
									<el-popover :width="300" @show="handlePopoverVisibleChange(data, true)" @hide="handlePopoverVisibleChange(data, false)">
										<template #reference>
											<!-- 更多操作按钮：鼠标悬停时显示 -->
											<div
												@click.stop=""
												:class="{ 'bg-[#dee2ea] rounded-[2px] opacity-100': hoverNodeId === data[treeFieldMapping.value] }"
												class="opacity-0 group-hover:opacity-100 px-1 hover:bg-[#dee2ea] hover:rounded-[2px] w-[20px] h-[20px] flex justify-center items-center"
											>
												<el-icon><MoreFilled /></el-icon>
											</div>
										</template>

										<!-- 弹出框内容：显示节点信息和操作按钮 -->
										<template #default>
											{{ data[treeFieldMapping.label] }}
											<div class="flex justify-end pt-3">
												<el-space size="small">
													<!-- 初始化申报按钮：未申报时显示 -->
													<el-button
														type="primary"
														size="small"
														@click="initDeclaration({ ObjId: data[treeFieldMapping.value] })"
														v-if="data.isApplyed === 0"
														:loading="initLoading"
													>
														初始化申报
													</el-button>

													<!-- 取消申报按钮：已申报时显示 -->
													<el-button
														type="danger"
														size="small"
														@click="cancelDeclaration({ ObjId: data[treeFieldMapping.value] })"
														v-if="data.isApplyed === 1"
														:loading="cancelLoading"
													>
														取消申报
													</el-button>
												</el-space>
											</div>
										</template>
									</el-popover>
								</div>
							</div>
						</template>
					</el-tree-v2>
				</div>
			</div>
		</template>

		<!-- 右侧插槽：内容展示区域 -->
		<template #right>
			<!-- 
				右侧内容面板：
				- flex flex-col：垂直布局
				- flex-1：占据剩余空间
				- h-full：占满父容器高度
				- overflow-hidden：隐藏溢出内容
				- bg-white：白色背景
				- rounded-md：圆角
			-->
			<div class="flex flex-col flex-1 h-full overflow-hidden bg-white rounded-md">
				<!-- 
					动态组件：日前市场Grid组件
					- mode="unit"：固定为机组模式
					- :is="DayAheadMarketGrid"：动态组件引用
					- :table-key="modeType"：表格配置键名
					- :key：组件唯一标识，用于强制重新渲染
					- :uuid：UUID标识符，用于事件监听
					- :param：查询参数，传递选中的节点ID
				-->
				<component
					mode="unit"
					:is="DayAheadMarketGrid"
					:table-key="modeType"
					:key="modeType + selectedNodeId"
					:uuid="`${modeType}_${selectedNodeId}`"
					:param="{ [treeFieldMapping.value]: selectedNodeId === '0' ? '' : selectedNodeId }"
				/>
			</div>
		</template>
	</Split>
</template>

<script setup lang="ts">
import { ref, watch, inject, ComputedRef, computed } from 'vue';
import { Search, MoreFilled } from '@element-plus/icons-vue';
import { ElTree } from 'element-plus';
import { request } from '/@/utils/service';
import XEUtils from 'xe-utils';
import DayAheadMarketGrid from './DayAheadMarketGrid.vue';
import DayAheadMarketConfig from './DayAheadMarketConfig';
import { useResizeObserver } from '@vueuse/core';
import { ElMessage } from 'element-plus';
import mittBus from '/@/utils/mitt';
import Split from '/@/components/split/index.vue';

// ========================= 组件属性定义 =========================

/**
 * 组件属性接口
 * @description 定义日前市场树组件的属性
 */
interface Props {
	/** 模式类型：用户日前市场 | 机组日前市场 */
	modeType: 'user-day-ahead-market' | 'unit-day-ahead-market';
}

const props = withDefaults(defineProps<Props>(), {
	modeType: 'user-day-ahead-market',
});

// ========================= 依赖注入和配置 =========================

/** 注入的数据包，包含当前案例的基本信息 */
const dataPacket = inject('dataPacket') as ComputedRef<Recordable>;

/** 根据模式类型获取对应的配置 */
const config = computed(() => {
	return DayAheadMarketConfig[props.modeType as keyof typeof DayAheadMarketConfig];
});

// ========================= 树形组件相关状态 =========================

/** 树组件容器引用（用于尺寸监听） */
const treeContainerRef = ref<HTMLElement>();

/** 树组件引用 */
const treeRef = ref<InstanceType<typeof ElTree>>();

/** 树组件高度（用于虚拟化） */
const treeHeight = ref(0);

/** 申报状态筛选值 */
const status = ref('');

/** 状态筛选选项 */
const statusOptions = ref([
	{ label: '全部', value: '' },
	{ label: '已申报', value: 1 },
	{ label: '未申报', value: 0 },
]);

/** 搜索关键词 */
const searchValue = ref('');

/** 树数据加载状态 */
const loading = ref(false);

/** 原始树数据 */
const unitList = ref<Recordable[]>([]);

/** 悬停节点状态（用于控制操作按钮显示） */
const hoverNodeId = ref<number | string>('');

// ========================= 计算属性 =========================

/**
 * 树字段映射配置
 * @description 根据模式类型返回不同的字段映射
 */
const treeFieldMapping = computed(() => {
	return props.modeType === 'user-day-ahead-market'
		? {
				label: 'user_name', // 用户名称字段
				value: 'user_id', // 用户ID字段
				isApplyed: 'isApplyed', // 申报状态字段
		  }
		: {
				label: 'sgen_name', // 机组名称字段
				value: 'unit_id', // 机组ID字段
				isApplyed: 'isApplyed', // 申报状态字段
		  };
});

/**
 * 过滤后的树数据
 * @description 根据状态筛选条件过滤树数据
 */
const filteredTreeData = computed(() => {
	return status.value === '' ? unitList.value : unitList.value.filter((item) => item.isApplyed === status.value);
});

/**
 * "全部"选项数据
 * @description 构造"全部"选项的数据结构
 */
const allOptionData = computed(() => {
	return {
		[treeFieldMapping.value.value as string]: '0',
		[treeFieldMapping.value.label as string]: '全部',
	};
});

// ========================= 选择状态管理 =========================

/** 选中的节点数据 */
const selectedNodeData = ref<Recordable>(allOptionData.value);

/** 选中的节点ID */
const selectedNodeId = computed(() => {
	return selectedNodeData.value[treeFieldMapping.value.value];
});

// ========================= 树组件事件处理 =========================

/**
 * 监听树组件尺寸变化
 * @description 使用ResizeObserver监听树组件容器尺寸变化，更新虚拟化高度
 */
useResizeObserver(treeContainerRef, (entries) => {
	const entry = entries[0];
	const { height } = entry.contentRect;
	treeHeight.value = height;
});

/**
 * 搜索功能
 * @description 监听搜索关键词变化，执行防抖搜索
 */
watch(
	() => searchValue.value,
	(val) => {
		debouncedFilter(treeRef.value, val);
	}
);

/**
 * 树节点过滤方法
 * @param value 搜索关键词
 * @param node 树节点数据
 * @returns 是否匹配搜索条件
 */
const filterMethod = (value: string, node: any) => {
	return node[treeFieldMapping.value.label].includes(value);
};

/**
 * 防抖搜索函数
 * @description 使用XEUtils的防抖功能，避免频繁搜索
 */
const debouncedFilter = XEUtils.debounce((ref, val) => {
	ref.filter(val);
}, 300);

/**
 * 树节点点击事件处理
 * @param data 点击的节点数据
 * @description 更新选中状态
 */
function handleNodeClick(data: Recordable) {
	selectedNodeData.value = data;
}

/**
 * 弹出框显示/隐藏事件处理
 * @param data 节点数据
 * @param visible 是否显示
 * @description 控制操作按钮的高亮状态
 */
const handlePopoverVisibleChange = (data: Recordable, visible: boolean) => {
	if (visible) {
		hoverNodeId.value = data[treeFieldMapping.value.value];
	} else {
		hoverNodeId.value = '';
	}
};

// ========================= 申报操作相关 =========================

/** 取消申报加载状态 */
const cancelLoading = ref(false);

/** 初始化申报加载状态 */
const initLoading = ref(false);

/**
 * 取消申报操作
 * @param param 包含对象ID的参数
 * @description 调用取消申报API，更新本地状态，触发刷新事件
 */
const cancelDeclaration = async ({ ObjId }: { ObjId: string }) => {
	cancelLoading.value = true;
	try {
		const { msg } = await request({
			url: config.value.api.cancel,
			method: 'post',
			data: { bb_case_id: dataPacket.value.id, [treeFieldMapping.value.value]: ObjId },
		});

		ElMessage.success(msg);

		// 更新本地数据状态
		const index = unitList.value.findIndex((item) => item[treeFieldMapping.value.value] === ObjId);
		if (index !== -1) {
			unitList.value[index].isApplyed = 0;
		}

		// 触发Grid刷新事件
		mittBus.emit('refreshDeclaration', `${props.modeType}_${ObjId}`);
	} finally {
		cancelLoading.value = false;
	}
};

/**
 * 初始化申报操作
 * @param param 包含对象ID的参数
 * @description 调用初始化申报API，更新本地状态，触发刷新事件
 */
const initDeclaration = async ({ ObjId }: { ObjId: string }) => {
	initLoading.value = true;
	try {
		const { msg } = await request({
			url: config.value.api.init,
			method: 'post',
			data: { bb_case_id: dataPacket.value.id, [treeFieldMapping.value.value]: ObjId },
		});

		ElMessage.success(msg);

		// 更新本地数据状态
		const index = unitList.value.findIndex((item) => item[treeFieldMapping.value.value] === ObjId);
		if (index !== -1) {
			unitList.value[index].isApplyed = 1;
		}

		// 触发Grid刷新事件
		mittBus.emit('refreshDeclaration', `${props.modeType}_${ObjId}`);
	} finally {
		initLoading.value = false;
	}
};

// ========================= 数据获取 =========================

/**
 * 获取树数据
 * @description 从服务器获取树形数据，包含"全部"选项
 */
const getUnitBasicTree = async () => {
	try {
		loading.value = true;
		const res = await request({
			url: `${config.value.api.tree}&bb_case_id=${dataPacket.value.id}`,
			method: 'get',
		});

		// 在数据前面添加"全部"选项
		unitList.value = [allOptionData.value, ...res.data];
	} finally {
		loading.value = false;
	}
};

// 初始化数据
getUnitBasicTree();
</script>

<style scoped>
/**
 * 树组件样式定制
 * @description 隐藏展开图标，调整节点内容宽度
 */
.el-tree-bg-color {
	:deep(.el-tree-node__expand-icon) {
		display: none;
	}
	:deep(.el-tree-node__content) {
		width: 100%;
	}
}
</style>
