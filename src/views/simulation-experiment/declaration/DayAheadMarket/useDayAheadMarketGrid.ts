import { type GridApi } from 'ag-grid-community';
import { ref, inject, ComputedRef, computed } from 'vue';
import mittBus from '/@/utils/mitt';
import { myTheme, localeText, prefixKey, sideBar, defaultColDef } from '/@/config/ag-grid';
import { ElMessageBox } from 'element-plus';
import { request } from '/@/utils/service';
import config from './DayAheadMarketConfig';
import { successNotification } from '/@/utils/message';

// ========================= AG Grid 基础配置 =========================

/**
 * 创建Grid选项配置
 * @description 配置AG Grid的基本选项，启用内置分页功能
 */
const createGridOptions = () => ({
	singleClickEdit: false, // 禁用单击编辑，需要双击才能编辑
	cellSelection: true, // 启用单元格选择功能
	enableCharts: true, // 启用图表功能
	suppressAggFuncInHeader: true, // 禁用列头中的聚合函数显示
	// AG Grid内置分页配置
	pagination: true, // 启用内置分页
	paginationPageSize: 100, // 每页显示数量
	paginationPageSizeSelector: [100, 200, 300, 400, 500], // 分页大小选择器
	// 自定义覆盖层模板
	overlayLoadingTemplate: `
		<div style="
			display: flex; 
			flex-direction: column; 
			align-items: center; 
			justify-content: center; 
			height: 100%; 
			font-size: 14px; 
			color: #666;
		">
			<div style="
				width: 32px; 
				height: 32px; 
				border: 3px solid #f3f3f3; 
				border-top: 3px solid #409eff; 
				border-radius: 50%; 
				animation: spin 1s linear infinite; 
				margin-bottom: 12px;
			"></div>
			<div>数据加载中...</div>
			<style>
				@keyframes spin {
					0% { transform: rotate(0deg); }
					100% { transform: rotate(360deg); }
				}
			</style>
		</div>
	`,
	overlayNoRowsTemplate: `
		<div style="
			display: flex; 
			flex-direction: column; 
			align-items: center; 
			justify-content: center; 
			height: 100%; 
			font-size: 14px; 
			color: #909399;
		">
			<div style="
				width: 64px; 
				height: 64px; 
				background: #f5f7fa; 
				border-radius: 50%; 
				display: flex; 
				align-items: center; 
				justify-content: center; 
				margin-bottom: 16px;
				font-size: 24px;
			">📄</div>
			<div>暂无数据</div>
		</div>
	`,
});

/**
 * 创建行模型配置
 * @description 配置AG Grid的服务端行模型，简化配置专注于分页功能
 */
const createRowModel = () => ({
	rowModelType: 'serverSide' as const, // 使用服务端行模型
	theme: myTheme, // 应用自定义主题
	suppressServerSideFullWidthLoadingRow: true, // 禁用全宽加载行
	getRowId: (params: Record<string, any>) => {
		// 行ID获取逻辑：优先使用ubid，其次使用prefixKey
		return params.data.ubid || params.data[prefixKey];
	},
});

// ========================= AG Grid 模块注册 =========================

import { ModuleRegistry, LocaleModule, NumberEditorModule } from 'ag-grid-community';
import {
	ServerSideRowModelModule,
	MenuModule,
	ColumnsToolPanelModule,
	FiltersToolPanelModule,
	RichSelectModule,
	ClipboardModule,
	IntegratedChartsModule,
	ValidationModule,
	TextEditorModule,
	TextFilterModule,
	SetFilterModule,
	CellStyleModule,
	NumberFilterModule,
	PaginationModule,
} from 'ag-grid-enterprise';
import { AgChartsEnterpriseModule } from 'ag-charts-enterprise';

/**
 * 注册AG Grid模块
 * @description 注册必需的AG Grid模块，确保分页和其他功能正常工作
 */
const registerAgGridModules = () => {
	ModuleRegistry.registerModules([
		CellStyleModule,
		LocaleModule,
		ServerSideRowModelModule,
		MenuModule,
		ColumnsToolPanelModule,
		FiltersToolPanelModule,
		RichSelectModule,
		TextEditorModule,
		NumberEditorModule,
		TextFilterModule,
		SetFilterModule,
		NumberFilterModule,
		ClipboardModule,
		ValidationModule,
		PaginationModule,
		IntegratedChartsModule.with(AgChartsEnterpriseModule),
	]);
};

// 注册模块
registerAgGridModules();

// ========================= 主要Hook函数 =========================

/**
 * 日前市场Grid Hook
 * @param table_key 表格配置键名
 * @param uuid 唯一标识符，用于监听刷新事件
 * @param queryParams 查询参数
 * @returns 返回Grid配置和业务逻辑方法
 * 
 * @description
 * 专门为日前市场设计的Grid Hook，包含：
 * - AG Grid内置分页功能
 * - 服务端数据源
 * - CRUD操作（保存、刷新）
 * - 申报相关操作（初始化、取消）
 * - 事件监听和状态管理
 */
export function useDayAheadMarketGrid(table_key: keyof typeof config, uuid: string, queryParams: Recordable) {
	// ========================= 基础配置 =========================
	
	/** AG Grid基础配置对象 */
	const baseConfig = {
		localeText,
		gridOptions: createGridOptions(),
		rowModel: createRowModel(),
		sideBar,
		defaultColDef,
		prefixKey,
	};

	// ========================= 状态管理 =========================
	
	/** 注入的数据包，包含当前案例的基本信息 */
	const dataPacket = inject('dataPacket') as ComputedRef<Recordable>;
	
	/** AG Grid API实例引用 */
	const gridApi = ref<GridApi>();
	
	/** 列定义配置，从配置文件中获取 */
	const columnDefs = computed(() => config[table_key].columns);
	
	/** 行数据缓存（用于本地状态管理） */
	const rowData = ref<Record<string, any>[]>([]);
	
	/** 表格加载状态 */
	const tableLoading = ref(false);
	
	/** 保存操作加载状态 */
	const saveLoading = ref(false);
	
	/** 初始化申报加载状态 */
	const initDeclarationLoading = ref(false);
	
	/** 取消申报加载状态 */
	const cancelDeclarationLoading = ref(false);
	
	/** 已修改但未保存的记录数组 */
	const updatedRecords = ref<Recordable[]>([]);

	// ========================= 服务端数据源配置 =========================

	/**
	 * 服务端数据源配置对象
	 * @description 定义如何从服务器获取数据的逻辑，使用AG Grid内置分页
	 */
	const dataSource = {
		getRows: async (params: Record<string, any>) => {
			const { startRow, endRow, filterModel, sortModel } = params.request;
			const agPageSize = endRow - startRow;
			const agPage = Math.floor(startRow / agPageSize) + 1;
			
			try {
				const response = await request({
					url: config[table_key].api.list,
					method: 'post',
					data: {
						page: agPage,
						limit: agPageSize,
						filterModel: {
							...filterModel,
							bb_case_id: {
								filterType: 'number',
								type: 'exact',
								filter: `${dataPacket.value.id}`,
							},
							// 合并查询参数到过滤器
							...Object.keys(queryParams || {}).reduce((acc: Record<string, any>, key) => {
								acc[key] = {
									filterType: 'text',
									type: 'contains',
									filter: `${queryParams[key]}`,
								};
								return acc;
							}, {} as Record<string, any>),
						},
						sortModel,
					},
				});
				
				const { code, data, total } = response;
				if (code === 2000) {
					const rowData = data || [];
					
					params.success({ 
						rowData, 
						rowCount: total || 0 
					});
					
					// 根据数据情况显示相应的覆盖层
					if (rowData.length === 0 && total === 0) {
						setTimeout(() => gridApi.value?.showNoRowsOverlay(), 100);
					} else {
						gridApi.value?.hideOverlay();
					}
				} else {
					params.fail();
					console.error('数据请求失败:', response.msg || '未知错误');
				}
			} catch (error) {
				console.error('加载数据失败:', error);
				params.fail();
			} finally {
				tableLoading.value = false;
			}
		},
	};

	// ========================= Grid事件处理方法 =========================
	
	/**
	 * Grid初始化完成事件处理
	 * @param params AG Grid传递的初始化参数
	 * @description 当AG Grid组件准备就绪时调用，设置数据源
	 */
	const onReady = (params: Record<string, any>) => {
		gridApi.value = params.api;
		tableLoading.value = true;
		
		// 设置AG Grid服务端数据源
		if (gridApi.value) {
			gridApi.value.setGridOption('serverSideDatasource', dataSource);
		}
	};

	/**
	 * 单元格值变更事件处理
	 * @param event AG Grid传递的变更事件对象
	 * @description 当用户编辑单元格内容时调用，用于跟踪变更
	 */
	const onCellValueChanged = (event: Record<string, any>) => {
		const index = updatedRecords.value.findIndex((item: Recordable) => item.ubid === event.data.ubid);
		
		if (index > -1) {
			// 更新已存在的记录
			updatedRecords.value[index] = event.data;
		} else {
			// 添加新的变更记录
			updatedRecords.value.push(event.data);
		}
	};

	// ========================= 业务逻辑方法 =========================
	
	/**
	 * 刷新数据操作
	 * @description 直接刷新数据，不进行确认
	 */
	const refreshData = () => {
		if (gridApi.value) {
			tableLoading.value = true;
			gridApi.value.refreshServerSide({ purge: true });
			gridApi.value.setGridOption('serverSideDatasource', dataSource);
		}
	};

	/**
	 * 刷新数据操作（带确认）
	 * @description 刷新表格数据，如果有未保存的变更会提示用户确认
	 */
	const refresh = () => {
		if (gridApi.value) {
			if (updatedRecords.value.length > 0) {
				ElMessageBox.confirm(
					`刷新会丢失未保存的${updatedRecords.value.length}条数据，是否继续？`,
					'温馨提示',
					{
						confirmButtonText: '确认',
						cancelButtonText: '取消',
						type: 'warning',
					}
				).then(() => {
					updatedRecords.value = [];
					refreshData();
				});
			} else {
				refreshData();
			}
		}
	};

	/**
	 * 保存数据操作
	 * @description 将所有变更的数据保存到服务器
	 */
	const handleSave = async () => {
		try {
			saveLoading.value = true;
			
			const { code } = await request({
				url: config[table_key].api.save,
				method: 'put',
				data: updatedRecords.value,
			});

			if (code === 2000) {
				successNotification(`成功保存${updatedRecords.value.length}条记录`);
				gridApi.value?.refreshCells({ force: true });
				updatedRecords.value = [];
			}
		} finally {
			saveLoading.value = false;
		}
	};

	/**
	 * 初始化申报操作
	 * @description 初始化申报信息
	 */
	const initDeclaration = async () => {
		initDeclarationLoading.value = true;
		try {
			const { code, msg } = await request({
				url: config[table_key].api.init,
				method: 'post',
				data: { bb_case_id: dataPacket.value.id },
			});
			// 可以在这里添加成功处理逻辑
		} finally {
			initDeclarationLoading.value = false;
		}
	};

	/**
	 * 取消申报操作
	 * @description 取消申报信息
	 */
	const cancelDeclaration = async () => {
		cancelDeclarationLoading.value = true;
		try {
			const { code, msg } = await request({
				url: config[table_key].api.cancel,
				method: 'post',
				data: { bb_case_id: dataPacket.value.id },
			});
			// 可以在这里添加成功处理逻辑
		} finally {
			cancelDeclarationLoading.value = false;
		}
	};

	// ========================= 计算属性 =========================
	
	/** 是否有未保存的数据 */
	const isSave = computed(() => updatedRecords.value.length > 0);
	
	/** 是否已初始化申报（基于是否有数据） */
	const isInitDeclaration = computed(() => rowData.value.length > 0);

	// ========================= 事件监听 =========================
	
	/**
	 * 监听刷新事件
	 * @description 通过mitt事件总线监听刷新申报的事件
	 */
	mittBus.on('refreshDeclaration', (data) => {
		if (uuid === data) {
			refreshData();
		}
	});

	// ========================= 返回值 =========================
	
	return {
		// AG Grid基础配置
		...baseConfig,
		
		// 状态管理
		gridApi,
		columnDefs,
		rowData,
		tableLoading,
		saveLoading,
		initDeclarationLoading,
		cancelDeclarationLoading,
		updatedRecords,
		
		// 计算属性
		isSave,
		isInitDeclaration,
		
		// 事件处理方法
		onReady,
		onCellValueChanged,
		
		// 业务逻辑方法
		refresh,
		refreshData,
		handleSave,
		initDeclaration,
		cancelDeclaration,
	};
}
