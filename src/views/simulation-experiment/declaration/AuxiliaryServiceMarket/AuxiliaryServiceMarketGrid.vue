<script setup lang="ts">
import { AgGridVue } from 'ag-grid-vue3';
import { useAuxiliaryServiceMarketGrid } from './useAuxiliaryServiceMarketGrid';
import config from './AuxiliaryServiceMarketConfig';
import { Refresh ,Check,RefreshRight,Remove} from '@element-plus/icons-vue';


// ========================= 组件属性定义 =========================

/**
 * 组件属性接口
 * @description 定义辅助服务市场Grid组件的属性
 */
interface Props {
	/** 模式类型：system = 系统级操作，unit = 机组级操作 */
	mode: 'system' | 'unit';
	/** 查询参数对象 */
	param: Recordable;
	/** 表格配置键名，用于获取对应的列配置和API配置 */
	tableKey: keyof typeof config;
	/** 组件唯一标识 */
	componentKey: string;
	/** UUID标识符，用于事件监听 */
	uuid: string;
}

const props = defineProps<Props>();

// ========================= 使用Hook获取Grid配置和方法 =========================

/**
 * 使用辅助服务市场Grid Hook
 * @description 获取AG Grid配置和所有业务逻辑方法
 */
const {
	// AG Grid基础配置
	gridOptions,
	rowModel,
	localeText,
	defaultColDef,
	sideBar,

	// 状态管理
	gridApi,
	columnDefs,
	tableLoading,
	saveLoading,
	initDeclarationLoading,
	cancelDeclarationLoading,

	// 计算属性
	isSave,
	isInitDeclaration,

	// 事件处理方法
	onReady,
	onCellValueChanged,

	// 业务逻辑方法
	refresh,
	handleSave,
	initDeclaration,
	cancelDeclaration,
} = useAuxiliaryServiceMarketGrid(props.tableKey, props.uuid, props.param);
</script>

<template>
	<!-- 主容器：使用flex布局，包含加载状态 -->
	<div class="flex flex-col flex-1 h-full bg-white rounded-md" v-loading="tableLoading" element-loading-text="数据加载中...">
		<!-- 操作按钮栏：顶部固定，包含刷新、申报操作、保存等按钮 -->
		<div class="flex items-center justify-start p-2 border-b border-gray-200">
			<el-space wrap :size="10">

<!-- 刷新按钮：始终可用，但需要Grid API初始化完成 -->
<el-tooltip content="刷新" placement="top">
					<el-button circle size="small" :icon="Refresh" @click="refresh" :disabled="!gridApi">  </el-button>
				</el-tooltip>

					<!-- 初始化申报按钮：当未初始化申报时显示 -->
					<el-tooltip content="初始化申报" placement="top"  v-if="mode === 'system'&&!isInitDeclaration">
					<el-button circle :icon="RefreshRight" size="small" @click="initDeclaration" :loading="initDeclarationLoading">  </el-button>
</el-tooltip>
					<!-- 取消申报按钮：当已初始化申报时显示 -->
					<el-tooltip content="取消申报" placement="top"  v-if="mode === 'system'&&isInitDeclaration">
					<el-button circle :icon="Remove" size="small" @click="cancelDeclaration" :loading="cancelDeclarationLoading" > 取消申报 </el-button>
					</el-tooltip>


				<!-- 保存按钮：只有当有未保存的数据时才可用 -->
				<el-tooltip content="保存" placement="top">
					<el-button circle size="small" :icon="Check" :disabled="!isSave" @click="handleSave" :loading="saveLoading"> </el-button>
				</el-tooltip>



				
			</el-space>
		</div>

		<!-- AG Grid表格组件：占据剩余空间 -->
		<ag-grid-vue
			class="w-full flex-1"
			v-bind="rowModel"
			:gridOptions="gridOptions"
			:columnDefs="columnDefs"
			:defaultColDef="defaultColDef"
			:localeText="localeText"
			:sideBar="sideBar"
			@grid-ready="onReady"
			@cell-value-changed="onCellValueChanged"
		/>
	</div>
</template>

<style lang="scss" scoped>
// 可以在这里添加组件特定的样式
</style>
