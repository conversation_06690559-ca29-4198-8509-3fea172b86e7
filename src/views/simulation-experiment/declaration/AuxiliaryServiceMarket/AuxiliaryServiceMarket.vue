<template>
	<div class="flex flex-col h-full">
		<div class="flex flex-1 h-0 overflow-hidden">
			<KeepAlive>
				<component :mode="route.query.type" :is="componentName" :table-key="tableKey" :key="tableKey" :uuid="`${tableKey}`"></component>
			</KeepAlive>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import type { ComputedRef } from 'vue';
import config from './AuxiliaryServiceMarketConfig';
import { type SiMu } from '/@/views/config/api';
import AuxiliaryServiceMarketTree from './AuxiliaryServiceMarketTree.vue';
import AuxiliaryServiceMarketGrid from './AuxiliaryServiceMarketGrid.vue';
import { useRoute } from 'vue-router';
const route = useRoute();

const CONFIG = {
	// 调频
	frequency_modulation: {
		system: 'bb_system_freq',
		unit: 'bb_unit_freq',
	},
	// 备用
	backup_modulationt: {
		system: 'bb_system_reserve_capacity',
		unit: 'bb_unit_reserve_capacity',
	},
	// 爬坡
	ramp_modulation: {
		system: 'bb_system_ramp',
		unit: 'bb_unit_ramp_capability',
	},
};

const componentName = computed(() => (route.query.type === 'system' ? AuxiliaryServiceMarketGrid : AuxiliaryServiceMarketTree));

const configType = computed(() => {
	return ['frequency_modulation', 'backup_modulationt', 'backup_modulationt_unit', 'backup_modulationt_system'].includes(route.query.page as string) ? (route.query.page as string) : 'frequency_modulation';
});

const tableKey = computed(() => CONFIG[configType.value as keyof typeof CONFIG][route.query.type as 'system' | 'unit']) as ComputedRef<keyof typeof config>;

const { siMuInfo } = defineProps<{
	siMuInfo: SiMu;
}>();
</script>
