<template>
	<div class="flex flex-col h-full">
		<div class="flex flex-1 h-0 overflow-hidden">
			<component
				:mode="'system'"
				:is="AuxiliaryServiceMarketGrid"
				:table-key="'bb_system_ramp'"
				:key="'bb_system_ramp'"
				:uuid="'bb_system_ramp'"
			></component>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { type SiMu } from '/@/views/config/api';
import AuxiliaryServiceMarketGrid from './AuxiliaryServiceMarketGrid.vue';
const { siMuInfo } = defineProps<{
	siMuInfo: SiMu;
}>();
</script>
