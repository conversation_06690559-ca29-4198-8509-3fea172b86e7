export const bb_system_freq = {
	api: {
		list: '/api/pm/bb_system_freq/query_list/',
		save: '/api/pm/bb_system_freq/batch_create_update/',
		init: '/api/pm/bb_system_freq/init_declaration/',
		cancel: '/api/pm/bb_system_freq/cancel_declaration/',
	},
	columns: [
		{
			headerName: '时刻',
			field: 'period_id',
			editable: false,
			filter: 'agTextColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '所需容量',
			field: 'freq_capacity',
			editable: true,
			filter: 'agNumberColumnFilter',
			cellEditor: 'agNumberCellEditor',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
	],
};
export const bb_unit_freq = {
	api: {
		tree: '/api/pm/bb_sgen/build_unit_classification/?classification=F',
		list: '/api/pm/bb_unit_freq/query_list/',
		save: '/api/pm/bb_unit_freq/batch_create_update/',
		init: '/api/pm/bb_unit_freq/init_declaration/',
		cancel: '/api/pm/bb_unit_freq/cancel_declaration/',
	},
	columns: [
		{
			headerName: '时刻',
			field: 'period_id',
			editable: false,
			filter: 'agTextColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '机组ID',
			field: 'unit_id',
			filter: false,
			sortable: false,
		},
		{
			headerName: '机组名称',
			field: 'unit_name',
			editable: false,
			filter: 'agTextColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '调频容量',
			field: 'freq_capacity',
			editable: true,
			filter: 'agNumberColumnFilter',
			cellEditor: 'agNumberCellEditor',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '调频里程报价',
			field: 'freq_mileage',
			editable: true,
			filter: 'agNumberColumnFilter',
			cellEditor: 'agNumberCellEditor',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '调频性能指标',
			field: 'freq_performance_metrics',
			editable: true,
			filter: 'agNumberColumnFilter',
			cellEditor: 'agNumberCellEditor',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '申报价格',
			field: 'price',
			editable: true,
			filter: 'agNumberColumnFilter',
			cellEditor: 'agNumberCellEditor',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '系统单位历史容量最小里程',
			field: 'min_capacity_mileage',
			editable: true,
			filter: 'agNumberColumnFilter',
			cellEditor: 'agNumberCellEditor',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '预估机会成本',
			field: 'opportunity_cost',
			editable: true,
			filter: 'agNumberColumnFilter',
			cellEditor: 'agNumberCellEditor',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
	],
};

export const bb_unit_reserve_capacity = {
	api: {
		tree: '/api/pm/bb_sgen/build_unit_classification/?classification=R',
		list: '/api/pm/bb_unit_reserve_capacity/query_list/',
		save: '/api/pm/bb_unit_reserve_capacity/batch_create_update/',
		init: '/api/pm/bb_unit_reserve_capacity/init_declaration/',
		cancel: '/api/pm/bb_unit_reserve_capacity/cancel_declaration/',
	},
	columns: [
		{
			headerName: '时刻',
			field: 'period_id',
			editable: false,
			filter: 'agTextColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '机组ID',
			field: 'unit_id',
			filter: false,
			sortable: false,
		},
		{
			headerName: '机组名称',
			field: 'unit_name',
			editable: false,
			filter: 'agTextColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '申报容量',
			field: 'reserve_capacity',
			editable: true,
			filter: 'agNumberColumnFilter',
			cellEditor: 'agNumberCellEditor',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '申报价格',
			field: 'price',
			editable: true,
			filter: 'agNumberColumnFilter',
			cellEditor: 'agNumberCellEditor',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
	],
};

export const bb_system_reserve_capacity = {
	api: {
		list: '/api/pm/bb_system_reserve_capacity/query_list/',
		save: '/api/pm/bb_system_reserve_capacity/batch_create_update/',
		init: '/api/pm/bb_system_reserve_capacity/init_declaration/',
		cancel: '/api/pm/bb_system_reserve_capacity/cancel_declaration/',
	},
	columns: [
		{
			headerName: '时刻',
			field: 'period_id',
			editable: false,
			filter: 'agTextColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '所需容量',
			field: 'reserve_capacity',
			editable: true,
			filter: 'agNumberColumnFilter',
			cellEditor: 'agNumberCellEditor',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
	],
};

export const bb_unit_ramp_capability = {
	api: {
		tree: '/api/pm/simu_info/20/get_auxiliary_service_market_unit_tree/?market_type=AS&object_type=unit',
		list: '/api/pm/bb_unit_ramp_capability/query_list/',
		save: '/api/pm/bb_unit_ramp_capability/batch_create_update/',
		init: '/api/pm/bb_unit_ramp_capability/init_declaration/',
		cancel: '/api/pm/bb_unit_ramp_modulation/cancel_declaration/',
	},
	columns: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '机组ID',
			field: 'unit_id',
			filter: false,
			sortable: false,
		},
		{
			headerName: '正常上升速率',
			field: 'inc_rate',
			filter: 'agTextColumnFilter',
			editable: true,
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '正常下降速率',
			field: 'dec_rate',
			filter: 'agTextColumnFilter',
			editable: true,
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '紧急上升速率',
			field: 'inc_rate_emg',
			filter: 'agTextColumnFilter',
			editable: true,
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '紧急下降速率',
			field: 'dec_rate_emg',
			filter: 'agTextColumnFilter',
			editable: true,
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: 'AGC上升速率',
			field: 'inc_rate_agc',
			filter: 'agTextColumnFilter',
			editable: true,
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: 'AGC下降速率',
			field: 'dec_rate_agc',
			filter: 'agTextColumnFilter',
			editable: true,
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '机组调峰上升速率',
			field: 'unit_peak_ramp_up',
			filter: 'agTextColumnFilter',
			editable: true,
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '机组调峰下降速率',
			field: 'unit_peak_ramp_down',
			filter: 'agTextColumnFilter',
			editable: true,
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
	],
};

export const bb_system_ramp = {
	api: {
		list: '/api/pm/bb_system_ramp/query_list/',
		save: '/api/pm/bb_system_ramp/batch_create_update/',
		init: '/api/pm/bb_system_ramp/init_declaration/',
		cancel: '/api/pm/bb_system_ramp/cancel_declaration/',
	},
	columns: [
		{
			headerName: '时刻',
			field: 'period_id',
			editable: false,
			filter: 'agTextColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '向上爬坡率',
			field: 'upward_ramp_rate',
			editable: true,
			filter: 'agNumberColumnFilter',
			cellEditor: 'agNumberCellEditor',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '向下爬坡率',
			field: 'downward_ramp_rate',
			editable: true,
			filter: 'agNumberColumnFilter',
			cellEditor: 'agNumberCellEditor',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
	],
};

export default {
	bb_system_freq,
	bb_unit_freq,
	bb_system_reserve_capacity,
	bb_unit_reserve_capacity,
	bb_system_ramp,
	bb_unit_ramp_capability,
};
