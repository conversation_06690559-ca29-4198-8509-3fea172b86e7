<template>
	<Split :left-size="15" class="rounded-md flex row-col h-full overflow-hidden">
		<template #left>
			<div class="h-full p-2 bg-white flex flex-col overflow-hidden el-tree-bg-color" v-loading="loading" element-loading-text="数据加载中...">
				<div class="">
					<el-input class="mb-2" v-model="searchValue" placeholder="搜索" :suffix-icon="Search"> </el-input>
				</div>
				<el-tree
					class="tree-bg-color flex-1 flex flex-col overflow-auto"
					ref="treeRef"
					:height="treeHeight"
					:data="resultList"
					:props="{
						label: 'name',
						children: 'children',
					}"
					default-expand-all
					:expand-on-click-node="false"
					@node-click="handleNodeClick"
					:filter-node-method="filterMethod"
				>
					<template #default="{ node, data }">
						<div
							class="flex group overflow-hidden justify-between items-center px-2 w-full relative"
							:class="selection.model && selection.model === data.model && selection.cal_type === node?.parent?.data?.id ? 'select-bg-color' : ''"
						>
							<div class="flex items-center w-full">
								{{ data.name }}
							</div>
						</div>
					</template>
				</el-tree>
			</div>
		</template>
		<template #right>
			<div class="flex flex-col flex-1 h-full overflow-hidden bg-white rounded-md">
				<component v-if="selection.cal_type" :is="LogGrid" :key="`${selection.cal_type}-${selection.model}`" :param="selection" />
				<el-empty v-else class="flex items-center justify-center w-full h-full" :description="`暂无数据`" :image-style="{ height: '100%' }" />
			</div>
		</template>
	</Split>
</template>

<script setup lang="ts">
import { ref, watch, inject, ComputedRef, computed } from 'vue';
import { Search } from '@element-plus/icons-vue';
import Split from '/@/components/split/index.vue';
import { ElTree } from 'element-plus';
import XEUtils from 'xe-utils';
import { queryResultTree } from '/@/views/config/api';
import { useResizeObserver } from '@vueuse/core';
import LogGrid from './LogGrid.vue';
const treeRef = ref<InstanceType<typeof ElTree>>();
const treeHeight = ref(0);
useResizeObserver(treeRef, (entries) => {
	const entry = entries[0];
	const { height } = entry.contentRect;
	treeHeight.value = height;
});

const searchValue = ref('');

watch(
	() => searchValue.value,
	(val) => {
		debouncedFilter(treeRef.value, val);
	}
);

const filterMethod = (value: string, node: any) => {
	return node['name'].includes(value);
};

const debouncedFilter = XEUtils.debounce((ref, val) => {
	ref.filter(val);
}, 300);

const dataPacket = inject('dataPacket') as ComputedRef<Recordable>;

const loading = ref(false);
const resultList = ref<Recordable[]>([]);
const getResultTree = async () => {
	try {
		loading.value = true;
		const res = await queryResultTree(dataPacket.value.id);
		resultList.value = res.data;
	} finally {
		loading.value = false;
	}
};

getResultTree();
const selection = ref<Recordable>({});
function handleNodeClick(node: Recordable, { parent: { data } }: Recordable) {
	if (node.model) {
		selection.value = {
			model: node.model,
			cal_type: data.id,
			name: node.name,
		};
		console.log(selection.value);
	}
}
</script>
