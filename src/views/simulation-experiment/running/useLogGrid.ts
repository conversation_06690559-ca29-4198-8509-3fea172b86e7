import { myTheme, localeText, prefixKey, defaultColDef, sideBar } from '/@/config/ag-grid';

// 图表工具面板的定义，默认为设置面板  https://www.ag-grid.com/vue-data-grid/integrated-charts/
const chartToolPanelsDef = {
	defaultToolPanel: 'settings',
};

// https://www.ag-grid.com/vue-data-grid/grid-options/#
const gridOptions = {
	singleClickEdit: false, // 禁用单击编辑
	cellSelection: true, //配置单元格选择
	enableCharts: true, //启用图表
	suppressAggFuncInHeader: true, // 禁用列头中的聚合函数显示
};

// 行模型配置  https://www.ag-grid.com/vue-data-grid/grid-options/#reference-serverSideRowModel
const rowModel = {
	rowModelType: 'serverSide',
	theme: myTheme, // 使用自定义主题
	cacheBlockSize: 1000, // store 中每个块的行数，即一次从服务器返回的行数。
	serverSideInitialRowCount: 50, // 设置为根级别组向用户显示多少个加载行。
	suppressServerSideFullWidthLoadingRow: true,
	getRowId: (params: Record<string, any>) => {
		return params.data.id || params.data[prefixKey];
	},
};

// 使用 AG Grid 的配置函数
export const useTableChartConfig = () => {
	return {
		localeText,
		gridOptions,
		rowModel,
		sideBar, // 侧边栏配置
		chartToolPanelsDef, // 图表工具面板定义
		defaultColDef, // 默认列配置
		prefixKey, // 前缀键
	};
};

// 导入 AG Grid 模块
import { ModuleRegistry, LocaleModule } from 'ag-grid-community';
import {
	ServerSideRowModelModule,
	MenuModule,
	RowGroupingModule,
	ColumnsToolPanelModule,
	FiltersToolPanelModule,
	RichSelectModule,
	ClipboardModule,
	IntegratedChartsModule,
	ValidationModule,
	TextEditorModule,
	TextFilterModule,
	MultiFilterModule,
	SetFilterModule,
	NumberFilterModule,
} from 'ag-grid-enterprise';
import { AgChartsEnterpriseModule } from 'ag-charts-enterprise';
// 注册 AG Grid 模块的函数
ModuleRegistry.registerModules([
	LocaleModule,
	ServerSideRowModelModule, // 服务器端行模型模块
	MenuModule, // 菜单模块
	RowGroupingModule, // 行分组模块
	ColumnsToolPanelModule, // 列工具面板模块
	FiltersToolPanelModule, // 过滤器工具面板模块
	RichSelectModule, // 选择模块
	ClipboardModule, // 剪切板模块
	IntegratedChartsModule.with(AgChartsEnterpriseModule), // 图表模块
	ValidationModule, // 验证模块
	TextEditorModule, // 文本编辑器模块
	TextFilterModule, // 文本过滤器模块
	MultiFilterModule, // 多过滤器模块
	SetFilterModule, // 设置过滤器模块
	NumberFilterModule, // 数字过滤器模块
]);
