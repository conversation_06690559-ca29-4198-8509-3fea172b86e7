<script setup lang="ts">
import { computed, onActivated, ref, inject, ComputedRef } from 'vue';
import { AgGridVue } from 'ag-grid-vue3';
import { type GridApi } from 'ag-grid-community';
import { useTableChartConfig } from './useLogGrid';
const { gridOptions, rowModel, localeText, defaultColDef, chartToolPanelsDef, sideBar } = useTableChartConfig();
import { queryClearEngineResult } from '/@/views/config/api';
const { param } = defineProps<{ param: Recordable }>();
const api = ref<GridApi>();
const rowData = ref<Record<string, any>[]>([]);
const columnDefs = computed(() => configColumns[param.model as keyof typeof configColumns] || []);
const configColumns = {
	UnitState: [
		{
			headerName: '时刻',
			field: 'period_id',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '机组ID',
			field: 'unit_id',
			filter: 'agTextColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '开停机状态',
			field: 'state',
			filter: 'agBooleanColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
			valueFormatter: function (params: any) {
				if (params.value === true) return '停机';
				else if (params.value === false) return '开机';
				return params.value;
			},
		},
		{
			headerName: '开机操作',
			field: 'on_val',
			filter: 'agBooleanColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
			valueFormatter: function (params: any) {
				if (params.value === true) return '否';
				else if (params.value === false) return '是';
				return params.value;
			},
		},
		{
			headerName: '停机操作',
			field: 'off_val',
			filter: 'agBooleanColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
			valueFormatter: function (params: any) {
				if (params.value === true) return '否';
				else if (params.value === false) return '是';
				return params.value;
			},
		},
	],
	FlowModel: [
		{
			headerName: '时刻',
			field: 'period_id',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '极限',
			field: 'pos_trans_limit',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '极限',
			field: 'neg_trans_limit',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '潮流值',
			field: 'flow_value',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '越限值',
			field: 'slack_value',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '断面ID',
			field: 'section_id',
			filter: 'agTextColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
	],
	LmpModel: [
		{
			headerName: '时刻',
			field: 'period_id',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '节点ID',
			field: 'bus_id',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '节点名称',
			field: 'bus_name',
			filter: 'agTextColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '节点价格',
			field: 'lmp',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
	],
	UserLmpModel: [
		{
			headerName: '时刻',
			field: 'period_id',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '统一结算价格',
			field: 'lmp',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
	],
	UnitResult: [
		{
			headerName: '时刻',
			field: 'period_id',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '机组ID',
			field: 'unit_id',
			filter: 'agTextColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '中标出力',
			field: 'xc',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '节点价格',
			field: 'lmp',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
	],
	UserResult: [
		{
			headerName: '时刻',
			field: 'period_id',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '用户ID',
			field: 'user_id',
			filter: 'agTextColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '中标出力',
			field: 'xc',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '统一结算价格',
			field: 'lmp',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
	],
	CecFreqPrice: [
		{
			headerName: '时刻',
			field: 'period_id',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '价格',
			field: 'price',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '里程单价',
			field: 'mileagePrice',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '小时里程',
			field: 'hourMileage',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '容量单价',
			field: 'capacityPrice',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '容量报价',
			field: 'cop',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
	],
	CecFreqUint: [
		{
			headerName: '时刻',
			field: 'period_id',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '机组ID',
			field: 'unit_id',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
		{
			headerName: '容量单价',
			field: 'capacity',
			filter: 'agNumberColumnFilter',
			filterParams: {
				buttons: ['apply', 'reset'],
				closeOnApply: true,
			},
		},
	],
};
const loading = ref(false);
function onReady(params: Record<string, any>) {
	api.value = params.api;
	loading.value = true;
	api.value!.setGridOption('serverSideDatasource', dataSource);
}
//标记
const mark = ref(0);
onActivated(() => {
	mark.value++;
	if (columnDefs.value.length === 0 && mark.value > 1) {
		loading.value = true;
		api.value!.setGridOption('serverSideDatasource', dataSource);
	}
});
const dataPacket = inject('dataPacket') as ComputedRef<Recordable>;

const dataSource = {
	getRows: async (params: Record<string, any>) => {
		const { startRow, endRow, filterModel, sortModel } = params.request;
		const pageSize = endRow - startRow;
		const page = startRow / pageSize + 1;
		try {
			const { code, data } = await queryClearEngineResult(dataPacket.value.id, {
				...param,
				page: page,
				limit: pageSize,
				filterModel,
				sortModel,
			});
			if (code === 2000 && data.length > 0) {
				data.forEach((item: Recordable, index: number) => {
					item.id = pageSize * (page - 1) + index + 1;
				});
				page === 1 ? (rowData.value = data) : rowData.value.push(...data);
				params.success({ rowData: data });
			} else {
				params.success({ rowData: [] });
			}
		} finally {
			loading.value = false;
		}
	},
};
</script>

<template>
	<div class="flex flex-col flex-1 h-full" v-loading="loading" element-loading-text="数据加载中...">
		<ag-grid-vue
			v-show="columnDefs.length > 0"
			:class="`w-full flex-1`"
			v-bind="{ ...rowModel }"
			:gridOptions="gridOptions"
			:columnDefs="columnDefs"
			:defaultColDef="defaultColDef"
			:localeText="localeText"
			:sideBar="sideBar"
			:chartToolPanelsDef="chartToolPanelsDef"
			@grid-ready="onReady"
		/>
		<el-empty
			class="flex items-center justify-center w-full h-full"
			:description="`暂无${param?.name || ''}数据`"
			v-show="rowData.length === 0"
			:image-style="{ height: '100%' }"
		/>
	</div>
</template>
