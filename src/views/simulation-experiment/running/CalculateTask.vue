<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, onDeactivated, onActivated } from 'vue';
import { AgGridVue } from 'ag-grid-vue3';
import { type GridApi } from 'ag-grid-community';
import { useTableChartConfig } from '../config/useTableChartConfigCalculateLog';
import ButtonRenderer from '../custom/ButtonRenderer.vue';
import { type SiMu } from '/@/views/config/api';
import { postCalculate, getQueryTaskList, postQueryLog } from '/@/views/config/api';
import { ElMessage, ElMessageBox } from 'element-plus';
import _ from 'lodash';
let refreshTimer: NodeJS.Timeout | null = null; // 定义定时器变量
const { siMuInfo } = defineProps<{
	siMuInfo: SiMu;
}>();
const api = ref<GridApi>();
//每行的运行按钮方法，被当成Params传到自定义组件里
const runButtonClick = async (res: any) => {
	ElMessageBox.confirm('是否启用计算？', '提示', {
		confirmButtonText: '启用',
		cancelButtonText: '不启用',
		type: 'warning',
	})
		.then(async () => {
			// 执行计算任务
			let resData = await runPostCalculate(siMuInfo.id, {
				cal_type: res.task_type,
				extraParam: {},
			});

			// 检查 API 响应，并显示提示框
			if (resData.status) {
				ElMessage({
					message: resData.res.msg,
					type: 'success',
					duration: 3000, // 提示框显示时间（毫秒）
				});
				api.value?.refreshServerSide();
			} else {
				ElMessage({
					message: `操作失败：${resData.res.msg || '未知错误'}`,
					type: 'error',
					duration: 3000,
				});
			}
		})
		.catch(() => {
			// 用户点击了“不启用”或关闭了对话框
		});
};

onDeactivated(() => {
	clearRefreshTimer();
});

// 清除计时器的函数
const clearRefreshTimer = () => {
	if (refreshTimer) {
		clearInterval(refreshTimer);
		refreshTimer = null;
	}
};

const startRefreshTimer = () => {
	refreshTimer = setInterval(async () => {
		api.value?.refreshServerSide();
	}, 1000 * 10);
};

onActivated(() => {
	api.value && startRefreshTimer();
});

// 在组件卸载前清除计时器
onBeforeUnmount(() => {
	clearRefreshTimer();
});

const { registerAgGridModules, gridOptions, rowModel, localeText, defaultColDef } = useTableChartConfig();
registerAgGridModules();

// 计算任务类型
const paramsList = [
	{
		taskType: 'MA_UC',
		taskName: '电能_UC',
		extraParam: {
			FreqMode: 'none',
		},
	},
	{
		taskType: 'MA_ED',
		taskName: '电能_ED',
		extraParam: {
			FreqMode: 'independent',
		},
	},
	{
		taskType: 'MA+F_UC',
		taskName: '电能+调频_UC',
		extraParam: {
			FreqMode: 'joint',
		},
	},
	{
		taskType: 'MA+F_ED',
		taskName: '电能+调频_ED',
		extraParam: {
			FreqMode: 'none',
		},
	},
	{
		taskType: 'MA+R_UC',
		taskName: '电能+备用_UC',
		extraParam: {
			FreqMode: 'independent',
		},
	},
	{
		taskType: 'MA+R_ED',
		taskName: '电能+备用_ED',
		extraParam: {
			FreqMode: 'joint',
		},
	},
	{
		taskType: 'MA+C_UC',
		taskName: '电能+爬坡_UC',
		extraParam: {
			FreqMode: 'none',
		},
	},
	{
		taskType: 'MA+C_ED',
		taskName: '电能+爬坡_ED',
		extraParam: {
			FreqMode: 'none',
		},
	},
];

//调用运行任务
const runPostCalculate = async (id: any, data: any) => {
	let res = await postCalculate(id, data);
	return { status: res.code == 2000, res };
};

const colDefs = ref([
	{ field: 'taskName', headerName: '任务名称' },
	{ field: 'task_type', headerName: '任务类型' },
	{
		field: 'status',
		valueFormatter: (params: any) => {
			const statusMap = {
				1: '已完成',
				2: '执行中',
				3: '失败',
			};
			return statusMap[params.value] || '~';
		},
		headerName: '任务状态',
		autoHeight: true,
	},
	{
		field: 'exec_time',
		headerName: '执行时间',
		valueFormatter: (params) => {
			if (!params.value) return '~';
			const date = new Date(params.value);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			const seconds = String(date.getSeconds()).padStart(2, '0');
			return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
		},
		autoHeight: true,
	},
	{
		field: 'button',
		cellRenderer: ButtonRenderer,
		cellRendererParams: {
			onClick: runButtonClick,
			logClick: logButtonClick,
		},
		headerName: '操作',
	},
]);
const loading = ref(false);

function onReady(params: Record<string, any>) {
	api.value = params.api;
	loading.value = true;
	api.value!.setGridOption('serverSideDatasource', dataSource);
	startRefreshTimer();
}

const dataSource = {
	getRows: async (params: Record<string, any>) => {
		try {
			const response = await getQueryTaskList({ simu_info_id: siMuInfo.id });
			if (response.code === 2000) {
				params.success({ rowData: response.data });
			} else {
				params.fail();
			}
		} catch (error) {
			params.fail();
		} finally {
			loading.value = false;
		}
	},
};
const dialogVisible = ref(false);

const taskLogSetInterval = ref<NodeJS.Timeout | null>(null);

//每行的日志按钮方法，被当成Params传到自定义组件里
async function logButtonClick(res: Recordable) {
	dialogVisible.value = true;
	taskLogSetInterval.value && clearInterval(taskLogSetInterval.value);
	taskLogSetInterval.value = setInterval(async () => {
		await fetchLogData(res);
	}, 1000 * 3);
}
const logList = ref([]);
// 获取日志数据
// TODO - 日志数据定时请求
async function fetchLogData(data: Recordable) {
	const res = await postQueryLog({ simu_info_id: siMuInfo.id, task_type: data.task_type });
	if (res.code == 2000) {
		console.log(res.data);
		if (!_.isEmpty(res.data)) {
			logList.value = res.data;
		} else {
			logList.value = [{ domain: '', message: '暂无日志', taskId: '' }];
		}
	} else {
		logList.value = [{ domain: '', message: '数据错误', taskId: '' }];
	}
}

watch(
	() => dialogVisible.value,
	(val) => {
		if (!val) {
			taskLogSetInterval.value && clearInterval(taskLogSetInterval.value);
		}
	},
	{ deep: true }
);
</script>

<template>
	<div class="flex flex-col flex-1 h-full overflow-hidden">
		<!-- 任务列表 -->
		<div class="flex flex-col flex-1" v-loading="loading" element-loading-text="数据加载中...">
			<ag-grid-vue
				:class="`w-full flex-1`"
				v-bind="{ ...rowModel }"
				:gridOptions="gridOptions"
				:columnDefs="colDefs"
				:defaultColDef="defaultColDef"
				:localeText="localeText"
				@grid-ready="onReady"
			/>
		</div>
		<!-- 日志弹窗 -->
		<el-dialog :close-on-click-modal="false" v-model="dialogVisible" title="日志" width="80vw">
			<div class="p-4 overflow-y-auto text-white bg-black rounded-md h-[80vw]">
				<p v-for="i in logList" :key="i.taskId">{{ i.message || '错误' }}</p>
			</div>
		</el-dialog>
	</div>
</template>
