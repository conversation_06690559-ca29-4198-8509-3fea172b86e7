<script setup lang="ts">
import { AgGridVue } from 'ag-grid-vue3';
import { useConfiguredGrid } from './useConfiguredGrid';
import { Refresh, Plus, Delete, Check } from '@element-plus/icons-vue';

// 组件属性定义
const { param } = defineProps<{ param: { table_key: string; table_name: string } }>();

const {
	// AG Grid基础配置
	gridOptions,
	rowModel,
	localeText,
	defaultColDef,
	chartToolPanelsDef,
	sideBar,
	prefixKey,

	// 业务逻辑状态
	api,
	columnDefs,
	rowData,
	isRefreshing,
	isSaving,
	isDeleting,
	updatedRecords,
	selectedRows,

	// 事件处理方法
	onReady,
	onCellValueChanged,
	onSelectionChanged,

	// CRUD操作方法
	addRow,
	handleSave,
	handleDelete,
	refresh,
} = useConfiguredGrid(param);
</script>

<template>
	<div class="flex flex-1 flex-col h-full">
		<div class="border-b border-gray-200 p-2 flex items-center justify-start">
			<el-space wrap :size="10">
				<el-tooltip content="刷新" placement="top">
					<el-button circle size="small" :icon="Refresh" @click="refresh" :disabled="!api" :loading="isRefreshing"></el-button>
				</el-tooltip>
				<el-tooltip content="新增" placement="top">
					<el-button circle size="small" :icon="Plus" :disabled="!columnDefs.length" @click="addRow"></el-button>
				</el-tooltip>
				<el-tooltip content="删除" placement="top">
					<el-button circle size="small" :icon="Delete" @click="handleDelete" :disabled="selectedRows.length === 0" :loading="isDeleting"></el-button>
				</el-tooltip>
				<el-tooltip content="保存" placement="top">
					<el-button circle size="small" :icon="Check" @click="handleSave" :disabled="updatedRecords.length === 0" :loading="isSaving"></el-button>
				</el-tooltip>
			</el-space>
		</div>
		<ag-grid-vue
			class="w-full flex-1"
			v-bind="rowModel"
			:gridOptions="gridOptions"
			:rowData="rowData"
			:columnDefs="columnDefs"
			:defaultColDef="defaultColDef"
			:localeText="localeText"
			:sideBar="sideBar"
			:defaultToolPanel="chartToolPanelsDef"
			:rowSelection="{
				mode: 'multiRow',
				selectAll: 'filtered',
			}"
			:selectionColumnDef="{
				width: 50,
				maxWidth: 50,
				pinned: 'left',
				cellStyle: { textAlign: 'center' },
			}"
			:context="{ onCellValueChanged }"
			@grid-ready="onReady"
			@cell-value-changed="onCellValueChanged"
			@selection-changed="onSelectionChanged"
		/>
	</div>
</template>
