import { ref, inject, ComputedRef } from 'vue';
import { type GridApi } from 'ag-grid-community';
import { myTheme, localeText, prefixKey, sideBar, defaultColDef } from '/@/config/ag-grid';
import { request } from '/@/utils/service';
import { generateUUID } from '/@/utils/index';
import { successNotification } from '/@/utils/message';
import { ElMessageBox } from 'element-plus';
import { debounce } from 'lodash-es';
import { configColumns, dataApiConfig } from '/@/config/SimulationConfig';

// ========================= AG Grid 基础配置 =========================

/**
 * 创建Grid选项配置
 */
const createGridOptions = () => ({
	singleClickEdit: false,
	cellSelection: true,
	enableCharts: true,
	suppressAggFuncInHeader: true,
	// AG Grid内置分页配置
	pagination: true,
	paginationPageSize: 100,
	paginationPageSizeSelector: [100, 200, 300, 400, 500],
	// 自定义覆盖层模板
	overlayLoadingTemplate: `
		<div style="
			display: flex; 
			flex-direction: column; 
			align-items: center; 
			justify-content: center; 
			height: 100%; 
			font-size: 14px; 
			color: #666;
		">
			<div style="
				width: 32px; 
				height: 32px; 
				border: 3px solid #f3f3f3; 
				border-top: 3px solid #409eff; 
				border-radius: 50%; 
				animation: spin 1s linear infinite; 
				margin-bottom: 12px;
			"></div>
			<div>数据加载中...</div>
			<style>
				@keyframes spin {
					0% { transform: rotate(0deg); }
					100% { transform: rotate(360deg); }
				}
			</style>
		</div>
	`,
	overlayNoRowsTemplate: `
		<div style="
			display: flex; 
			flex-direction: column; 
			align-items: center; 
			justify-content: center; 
			height: 100%; 
			font-size: 14px; 
			color: #909399;
		">
			<div style="
				width: 64px; 
				height: 64px; 
				background: #f5f7fa; 
				border-radius: 50%; 
				display: flex; 
				align-items: center; 
				justify-content: center; 
				margin-bottom: 16px;
				font-size: 24px;
			">📄</div>
			<div>暂无数据</div>
		</div>
	`,
});

/**
 * 创建行模型配置
 */
const createRowModel = () => ({
	rowModelType: 'serverSide' as const,
	theme: myTheme,
	suppressServerSideFullWidthLoadingRow: true,
	getRowId: (params: Record<string, any>) => {
		return params.data.ubid || params.data[prefixKey];
	},
});

// ========================= AG Grid 模块注册 =========================

import { ModuleRegistry, LocaleModule, NumberEditorModule } from 'ag-grid-community';
import {
	ServerSideRowModelModule,
	MenuModule,
	ColumnsToolPanelModule,
	FiltersToolPanelModule,
	RichSelectModule,
	ClipboardModule,
	IntegratedChartsModule,
	ValidationModule,
	TextEditorModule,
	TextFilterModule,
	SetFilterModule,
	CellStyleModule,
	NumberFilterModule,
	PaginationModule,
} from 'ag-grid-enterprise';
import { AgChartsEnterpriseModule } from 'ag-charts-enterprise';

/**
 * 注册AG Grid模块
 */
const registerAgGridModules = () => {
	ModuleRegistry.registerModules([
		CellStyleModule,
		LocaleModule,
		ServerSideRowModelModule,
		MenuModule,
		ColumnsToolPanelModule,
		FiltersToolPanelModule,
		RichSelectModule,
		TextEditorModule,
		NumberEditorModule,
		TextFilterModule,
		SetFilterModule,
		NumberFilterModule,
		ClipboardModule,
		ValidationModule,
		PaginationModule,
		IntegratedChartsModule.with(AgChartsEnterpriseModule),
	]);
};

registerAgGridModules();

// ========================= 主要Hook函数 =========================

/**
 * 通用配置化Grid Hook
 */
export function useConfiguredGrid(param: { table_key: string; table_name: string }) {
	// ========================= 基础配置 =========================

	const baseConfig = {
		localeText,
		gridOptions: createGridOptions(),
		rowModel: createRowModel(),
		sideBar,
		chartToolPanelsDef: { defaultToolPanel: 'settings' },
		defaultColDef,
		prefixKey,
	};

	// ========================= 业务逻辑状态 =========================

	const dataPacket = inject('dataPacket') as ComputedRef<Recordable>;
	const api = ref<GridApi>();
	const columnDefs = ref<Record<string, any>[]>([]);
	const rowData = ref<Record<string, any>[]>([]);

	const isRefreshing = ref(false);
	const isSaving = ref(false);
	const isDeleting = ref(false);

	const updatedRecords = ref<Recordable[]>([]);
	const selectedRows = ref<Recordable[]>([]);

	// ========================= 服务端数据源配置 =========================

	const dataSource = {
		getRows: async (params: Record<string, any>) => {
			const { startRow, endRow, filterModel, sortModel } = params.request;
			const agPageSize = endRow - startRow;
			const agPage = Math.floor(startRow / agPageSize) + 1;

			try {
				const response = await request({
					url: dataApiConfig[param.table_key as keyof typeof dataApiConfig].list,
					method: 'post',
					data: {
						page: agPage,
						limit: agPageSize,
						filterModel: {
							...filterModel,
							bb_case_id: {
								filterType: 'number',
								type: 'exact',
								filter: `${dataPacket.value.id}`,
							},
						},
						sortModel,
					},
				});

				const { code, data, total } = response;
				if (code === 2000) {
					const rowData = data || [];
					params.success({
						rowData,
						rowCount: total || 0,
					});

					if (rowData.length === 0 && total === 0) {
						setTimeout(() => api.value?.showNoRowsOverlay(), 100);
					} else {
						api.value?.hideOverlay();
					}
				} else {
					params.fail();
					console.error('数据请求失败:', response.msg || '未知错误');
				}
			} catch (error) {
				console.error('加载数据失败:', error);
				params.fail();
			}
		},
	};

	// ========================= Grid事件处理方法 =========================

	/**
	 * Grid初始化完成事件处理
	 * @param params AG Grid传递的初始化参数
	 * @description 当AG Grid组件准备就绪时调用
	 */
	function onReady(params: Record<string, any>) {
		api.value = params.api;

		if (columnDefs.value.length === 0) {
			columnDefs.value = [...configColumns[param.table_key as keyof typeof configColumns]];
		}

		if (api.value) {
			api.value.setGridOption('serverSideDatasource', dataSource);
		}
	}

	/**
	 * 单元格值变更事件处理
	 * @param event AG Grid传递的变更事件对象
	 * @description 当用户编辑单元格内容时调用，用于跟踪变更
	 */
	function onCellValueChanged(event: Record<string, any>) {
		const data = event.data;
		const key = data.ubid ? 'ubid' : prefixKey;
		const keyValue = data[key];

		if (!keyValue) return;

		const index = updatedRecords.value.findIndex((item) => item[key] === keyValue);

		if (index > -1) {
			updatedRecords.value[index] = data;
		} else {
			updatedRecords.value.push(data);
		}
	}

	/**
	 * 行选择变更事件处理（防抖处理）
	 * @param event AG Grid传递的选择事件对象
	 * @description 当用户选择/取消选择行时调用，使用防抖避免频繁触发
	 */
	const onSelectionChanged = debounce((event: Record<string, any>) => {
		if (api.value) {
			selectedRows.value = api.value.getSelectedRows();
		}
	}, 100);

	// ========================= CRUD操作方法 =========================

	/**
	 * 新增行操作
	 * @description 在表格顶部添加一个新行，并标记为待保存状态
	 */
	function addRow() {
		const newRow = { [prefixKey]: generateUUID() };
		updatedRecords.value.push(newRow);

		if (api.value) {
			const transaction = {
				addIndex: 0,
				add: [newRow],
			};
			api.value.applyServerSideTransaction(transaction);
		}
	}

	/**
	 * 保存数据操作
	 * @description 将所有变更的数据保存到服务器
	 */
	async function handleSave() {
		try {
			isSaving.value = true;

			updatedRecords.value.forEach((record: Recordable) => {
				record.bb_case_id = dataPacket.value.id;
			});

			const records = {
				update: updatedRecords.value.filter((record) => record.ubid),
				add: updatedRecords.value.filter((record) => !record.ubid && record[prefixKey]),
			};

			const results = {
				update: records.update.length ? await addOrUpdateData('update', records.update) : null,
				add: records.add.length ? await addOrUpdateData('add', records.add) : null,
			};

			const successful = [];
			const failed = [];

			if (results.update && results.update.code === 2000) successful.push(...records.update);
			else if (results.update) failed.push(...records.update);

			if (results.add && results.add.code === 2000) successful.push(...records.add);
			else if (results.add) failed.push(...records.add);

			if (successful.length) {
				const successfulKeys = successful.map((record) => record.ubid || record[prefixKey]);
				updatedRecords.value = updatedRecords.value.filter((record) => {
					const key = record.ubid || record[prefixKey];
					return !successfulKeys.includes(key);
				});
			}

			if (successful.length && !failed.length) {
				successNotification(`成功保存${successful.length}条记录`);
				api.value?.refreshCells({ force: true });
				refreshData();
			} else if (failed.length) {
				ElMessageBox.alert(`有${failed.length}条记录保存失败，${successful.length}条保存成功`, '部分保存失败', {
					confirmButtonText: '确定',
					type: 'warning',
				});
			}
		} catch (error) {
			console.error('保存操作失败:', error);
			ElMessageBox.alert(error instanceof Error ? error.message : '操作失败，请重试', '操作失败', {
				confirmButtonText: '确定',
				type: 'error',
			});
		} finally {
			isSaving.value = false;
		}
	}

	/**
	 * 新增或更新数据的内部方法
	 * @param type 操作类型：'add' | 'update'
	 * @param data 要操作的数据
	 * @returns Promise<响应数据>
	 * @description 根据操作类型调用相应的API接口
	 */
	async function addOrUpdateData(type: 'add' | 'update', data: Recordable) {
		try {
			const response = await request({
				url: dataApiConfig[param.table_key as keyof typeof dataApiConfig].update,
				method: type === 'add' ? 'post' : 'put',
				data,
			});

			if (response.code === 2000) {
				return response;
			} else {
				throw new Error(response.msg || `${type === 'add' ? '新增' : '更新'}失败`);
			}
		} catch (error) {
			console.error(`${type === 'add' ? '新增' : '更新'}数据失败:`, error);
			throw error;
		}
	}

	/**
	 * 删除选中数据操作
	 * @description 删除用户选中的行数据
	 */
	async function handleDelete() {
		if (!selectedRows.value.length) return;

		try {
			const result = await ElMessageBox.confirm(`确定要删除选中的 ${selectedRows.value.length} 行数据吗？`, '删除确认', {
				confirmButtonText: '确认',
				cancelButtonText: '取消',
				type: 'warning',
			});

			if (result !== 'confirm') return;

			isDeleting.value = true;

			const savedRows = selectedRows.value.filter((row) => row.ubid);
			const unsavedRows = selectedRows.value.filter((row) => !row.ubid);

			if (savedRows.length) {
				const { success } = await apiRequest(
					dataApiConfig[param.table_key as keyof typeof dataApiConfig].delete,
					'delete',
					{ keys: savedRows.map((row) => row.ubid) },
					''
				);

				if (!success) throw new Error('删除已保存记录失败');
			}

			if (unsavedRows.length) {
				updatedRecords.value = updatedRecords.value.filter((record) => {
					return !unsavedRows.some((row) => row[prefixKey] && row[prefixKey] === record[prefixKey]);
				});
			}

			selectedRows.value = [];
			api.value?.deselectAll();
			refreshData();
		} catch (error) {
			console.error('删除失败:', error);
			ElMessageBox.alert(error instanceof Error ? error.message : '删除操作失败，请重试', '删除失败', {
				confirmButtonText: '确定',
				type: 'error',
			});
		} finally {
			isDeleting.value = false;
		}
	}

	// ========================= 数据刷新方法 =========================

	/**
	 * 刷新数据操作（带确认）
	 * @description 刷新表格数据，如果有未保存的变更会提示用户确认
	 */
	function refresh() {
		if (updatedRecords.value.length > 0) {
			ElMessageBox.confirm(`刷新会丢失未保存的${updatedRecords.value.length}条数据，是否继续？`, '温馨提示', {
				confirmButtonText: '确认',
				cancelButtonText: '取消',
				type: 'warning',
			}).then(() => {
				updatedRecords.value = [];
				refreshData();
			});
		} else {
			refreshData();
		}
	}

	/**
	 * 执行数据刷新的内部方法
	 * @description 直接刷新数据，不进行确认
	 */
	function refreshData() {
		isRefreshing.value = true;

		if (api.value) {
			api.value.refreshServerSide({ purge: true });
			api.value.setGridOption('serverSideDatasource', dataSource);
		}
		isRefreshing.value = false;
	}

	// ========================= 通用API请求方法 =========================

	/**
	 * 通用API请求方法
	 * @param url 请求URL
	 * @param method 请求方法
	 * @param data 请求数据
	 * @param successMessage 成功提示消息
	 * @returns Promise<{success: boolean, data?: any, error?: any}>
	 * @description 封装通用的API请求逻辑，包含错误处理和状态管理
	 */
	async function apiRequest(url: string, method: string, data: any, successMessage: string) {
		try {
			const response = await request({
				url,
				method,
				data,
			});

			if (response.code === 2000) {
				successMessage && successNotification(successMessage || response.msg);
				return { success: true, data: response.data };
			} else {
				throw new Error(response.msg || '操作失败');
			}
		} catch (error) {
			console.error('API请求失败:', error);
			ElMessageBox.alert(error instanceof Error ? error.message : '操作失败，请重试', '操作失败', {
				confirmButtonText: '确定',
				type: 'error',
			});
			return { success: false, error };
		}
	}

	// ========================= 返回值 =========================

	/**
	 * 返回完整的配置和业务逻辑
	 * 包含AG Grid配置和所有CRUD操作方法
	 */
	return {
		// AG Grid基础配置
		...baseConfig,

		// 状态管理
		/** AG Grid API实例 */
		api,
		/** 列定义配置 */
		columnDefs,
		/** 行数据缓存 */
		rowData,
		/** 刷新操作加载状态 */
		isRefreshing,
		/** 保存操作加载状态 */
		isSaving,
		/** 删除操作加载状态 */
		isDeleting,
		/** 已修改但未保存的记录 */
		updatedRecords,
		/** 当前选中的行数据 */
		selectedRows,

		// 事件处理方法
		/** Grid初始化事件处理 */
		onReady,
		/** 单元格值变更事件处理 */
		onCellValueChanged,
		/** 行选择变更事件处理 */
		onSelectionChanged,

		// CRUD操作方法
		/** 新增行 */
		addRow,
		/** 保存数据 */
		handleSave,
		/** 删除选中数据 */
		handleDelete,

		// 数据刷新方法
		/** 刷新数据（带确认） */
		refresh,
		/** 直接刷新数据 */
		refreshData,
	};
}
