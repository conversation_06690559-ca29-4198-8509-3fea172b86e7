import { myTheme, localeText, selectionColumnDef, sideBar, defaultColDef } from '/@/config/ag-grid';

// https://www.ag-grid.com/vue-data-grid/grid-options/#
const gridOptions = {
	theme: myTheme,
	localeText,
};

// 使用 AG Grid 的配置函数
export const useTableConfig = () => {
	return {
		gridOptions,
		sideBar, // 侧边栏配置
		defaultColDef, // 默认列配置
		selectionColumnDef, // 选择列配置
	};
};

import { ModuleRegistry, ClientSideRowModelModule, RowSelectionModule, RenderApiModule } from 'ag-grid-community';
import { FiltersToolPanelModule, ColumnsToolPanelModule } from 'ag-grid-enterprise';
ModuleRegistry.registerModules([ClientSideRowModelModule, RowSelectionModule, ColumnsToolPanelModule, FiltersToolPanelModule, RenderApiModule]);
