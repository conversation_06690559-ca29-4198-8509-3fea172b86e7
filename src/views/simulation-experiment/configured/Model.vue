<template>
	<div class="flex flex-col h-full relative">
		<KeepAlive>
			<component :is="componentName"></component>
		</KeepAlive>
	</div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import Configured from './Configured.vue';

const componentName = computed(() => {
	return Configured;
});

const value = ref('list');
</script>

<style lang="scss" scoped>
// 页面特定样式可以在这里添加
</style>
