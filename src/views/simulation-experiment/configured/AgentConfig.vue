<template>
	<div class="flex row-col h-full gap-4 overflow-hidden">
		<div class="bg-white rounded-md p-4 !w-[280px]">
			<div class="text-lg font-bold mb-4">智能体</div>
			<el-scrollbar class="">
				<div
					class="hover:bg-[#f3f4f6] p-2 rounded-sm cursor-pointer"
					v-for="agent in agentList"
					:key="agent.id"
					@click="handleNodeClick(agent)"
					:class="{ 'bg-[#f3f4f6]': selectedAgent.id === agent.id }"
				>
					<div class="font-bold text-lg">{{ agent.name }}</div>
					<el-text class="!my-2" tag="div">{{ agent.description }}</el-text>
					<el-tag type="info">{{ typeOptions.find((item) => item.value === agent.type)?.label }}</el-tag>
				</div>
			</el-scrollbar>
		</div>
		<div class="flex flex-col bg-white rounded-md h-full flex-1">
			<div class="flex flex-1 flex-col h-full" v-loading="loading" element-loading-text="数据加载中...">
				<div v-show="columnDefs.length > 0" class="border-b border-gray-200 p-2 flex items-center justify-end">
					<el-space wrap :size="10">
						<el-button size="small" @click="handleSave" :loading="saveLoading">保存</el-button>
					</el-space>
				</div>
				<ag-grid-vue
					v-show="columnDefs.length > 0"
					:class="`w-full flex-1`"
					v-bind="{ ...gridOptions }"
					:side-bar="sideBar"
					:rowData="rowData"
					:rowSelection="{
						mode: 'multiRow',
						selectAll: 'filtered',
					}"
					:columnDefs="columnDefs"
					@grid-ready="onReady"
					:selectionColumnDef="{
						width: 80,
						maxWidth: 80,
						pinned: 'left',
						cellStyle: { textAlign: 'center' },
					}"
				/>
				<!-- 
				
				-->
				<el-empty class="w-full h-full flex justify-center items-center" v-show="columnDefs.length === 0" :image-style="{ height: '100%' }" />
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { AgGridVue } from 'ag-grid-vue3';
import { type GridApi } from 'ag-grid-community';
import { useTableConfig } from './useAgentConfig';
const { gridOptions, selectionColumnDef, sideBar } = useTableConfig();
import { successNotification } from '/@/utils/message';
import { GetList, typeOptions } from '/@/views/agent/api';
import { dictConfig } from '/@/config/SimulationConfig';
import {
	getAgentUnitInfo,
	saveAgentUnitInfo,
	queryAgentUnitInfo,
	type SiMu,
	getAgentUserInfo,
	saveAgentUserInfo,
	queryAgentUserInfo,
} from '/@/views/config/api';
const api = ref<GridApi>();
const rowData = ref<Record<string, any>[]>([]);
const columnDefs = ref<Record<string, any>[]>([]);
import { getDictConfig } from '/@/config/ag-grid';

const UnitBasic = [
	{
		headerName: '机组ID',
		field: 'UnitID',
		editable: true,
		filter: 'agTextColumnFilter',
	},
	{
		headerName: '机组名称',
		field: 'UnitName',
		editable: true,
		filter: 'agTextColumnFilter',
	},
	{
		headerName: 'CIM模型ID',
		field: 'CIM_ID',
		editable: true,
		filter: 'agTextColumnFilter',
	},
	{
		headerName: '所属电厂ID',
		field: 'PlantID',
		editable: false,
		filter: 'agTextColumnFilter',
	},
	{
		headerName: '电厂名称',
		field: 'PlantName',
		filter: 'agTextColumnFilter',
	},
	{
		headerName: '机组所属区域',
		field: 'AreaID',
		editable: true,
		filter: 'agTextColumnFilter',
	},
	{
		headerName: '机组类型',
		field: 'Type',
		editable: true,
	},
	{
		headerName: '额定容量',
		field: 'MaxCapacity',
		editable: true,
		filter: 'agNumberColumnFilter',
		type: 'number',
	},
	{
		headerName: '最小技术出力',
		field: 'MinCapacity',
		editable: true,
		filter: 'agNumberColumnFilter',
		type: 'number',
	},
	{
		headerName: '厂用电率',
		field: 'SelfUseRatio',
		editable: true,
		filter: 'agNumberColumnFilter',
		type: 'number',
	},
	{
		headerName: '是否建模',
		field: 'IfModel',
		editable: true,
		...getDictConfig(dictConfig, 'IfModel'),
	},
	{
		headerName: '厂用电负荷',
		field: 'SelfUseLoad',
		editable: true,
		filter: 'agNumberColumnFilter',
		type: 'number',
	},
	{
		headerName: '所属发电集团',
		field: 'Group',
		editable: true,
		filter: 'agTextColumnFilter',
	},
	{
		headerName: '机组所属甘肃分区',
		field: 'Region',
		editable: true,
		filter: 'agTextColumnFilter',
	},
];

columnDefs.value = UnitBasic.map((item) => ({
	...item,
	editable: false,
}));
const loading = ref(false);
function onReady(params: Record<string, any>) {
	api.value = params.api;
}

const { siMuInfo } = defineProps<{
	siMuInfo: SiMu;
}>();
const agentList = ref<Recordable[]>([]);
const getAgentList = async () => {
	const { data } = await GetList();
	agentList.value = data;
};
const apiConfig = computed(() => {
	return {
		getRows: selectedAgent.value.type === 5 ? getAgentUserInfo : getAgentUnitInfo,
		query: selectedAgent.value.type === 5 ? queryAgentUserInfo : queryAgentUnitInfo,
		save: selectedAgent.value.type === 5 ? saveAgentUserInfo : saveAgentUnitInfo,
	};
});
const getDataRows = async () => {
	const {
		data: { data, headers },
	} = await apiConfig.value.getRows({ scene_model_id: siMuInfo.scene_model_id, limit: 10000, page: 1 });
	// columnDefs.value =
	// 	headers.length !== 0
	// 		? [
	// 				...headers.map((data: Recordable) => ({
	// 					headerName: data.field_ch_name,
	// 					field: data.field,
	// 					editable: true,
	// 					filter: 'agTextColumnFilter',
	// 					filterParams: { buttons: ['apply', 'reset'], closeOnApply: true },
	// 				})),
	// 		  ]
	// 		: [];
	rowData.value = data;

	setTimeout(() => {
		api.value!.forEachNode((node) => {
			node.setSelected(false);
			if (queryIds.value.includes(node.data[selectedAgent.value.type === 5 ? 'UserID' : 'UnitID'])) {
				node.setSelected(true);
			}
		});
		loading.value = false;
	}, 10);
};
const queryIds = ref<string[]>([]);

const getQueryIds = async () => {
	const { data } = await apiConfig.value.query(selectedAgent.value.id, { simulation_id: siMuInfo.id });
	queryIds.value = data;
};
getAgentList();
const selectedAgent = ref<Recordable>({});
async function handleNodeClick(data: Recordable) {
	if (selectedAgent.value.id === data.id) {
		return;
	}
	selectedAgent.value = data;
	loading.value = true;
	await getQueryIds();
	await getDataRows();
}
const saveLoading = ref(false);
async function handleSave() {
	try {
		saveLoading.value = true;
		const selectedRows = api.value!.getSelectedRows();
		const ids = selectedAgent.value.type !== 5 ? selectedRows.map((item) => item.UnitID) : selectedRows.map((item) => item.UserID);
		const { code, msg } = await apiConfig.value.save(selectedAgent.value.id, { id_list: ids, simulation_id: siMuInfo.id });
		if (code === 2000) {
			api.value!.refreshCells({ force: true });
		}
		successNotification(msg);
	} catch (error) {
	} finally {
		saveLoading.value = false;
	}
}
</script>

<style lang="scss" scoped></style>
