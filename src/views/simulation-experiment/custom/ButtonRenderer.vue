<template>
	<div class="flex items-center h-full">
		<el-button :loading="params?.data?.status == 2" :icon="VideoPlay" @click="handleClick" class="custom-button" circle type="primary"> </el-button>
		<el-button @click="props.params.logClick(props.params.data)" class="custom-button" type="primary"> 日志 </el-button>
	</div>
</template>

<script setup lang="ts">
import { ElButton, ElIcon } from 'element-plus';
import { VideoPlay } from '@element-plus/icons-vue';

// 定义 props
const props = defineProps(['params']);

// 定义 emits
const emit = defineEmits(['buttonClicked']);

// 点击事件处理函数
function handleClick() {
	props.params.onClick(props.params.data);
}
</script>

<style scoped>
.custom-button {
	font-size: 24px;
	color: #4c80ed;
	cursor: pointer;
}

.custom-button .el-icon {
	font-size: 24px;
}
</style>
