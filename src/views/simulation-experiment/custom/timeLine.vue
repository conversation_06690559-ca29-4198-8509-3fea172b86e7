<template>
	<div class="flex items-center">
		<div v-if="params?.data?.status">
			<div v-for="(node, index) in params.status" :key="index" class="flex items-center">
				<div class="flex flex-col items-center">
					<div class="flex items-center justify-center h-5">{{ node.name }}</div>
					<div class="w-5 h-5 rounded-full" :style="{ backgroundColor: node.color }"></div>
				</div>

				<div v-if="index < params.status.length - 1" class="flex flex-col items-center">
					<div class="h-5"></div>

					<div class="w-12 h-0.5 bg-gray-300"></div>
				</div>
			</div>
		</div>
		<div>错误</div>
	</div>
</template>

<script setup lang="ts">
import { defineProps, withDefaults, onBeforeMount } from 'vue';

const color = ['c7c7c7', '4c80ed'];

// 定义 props 并设置默认值
const props = defineProps(['params']);

// 在 onBeforeMount 生命周期钩子中查看传入的参数
onBeforeMount(() => {
	console.log('父组件传入的参数:', props.params);
});
</script>

<style scoped>
/* No additional styles needed, Tailwind CSS handles it */
</style>
