import { myTheme, localeText, selectionColumnDef, defaultColDef, sideBar } from '/@/config/ag-grid';

// https://www.ag-grid.com/vue-data-grid/grid-options/#
const gridOptions = {
	theme: myTheme,
	localeText,
};

// 使用 AG Grid 的配置函数
export const useTableConfig = () => {
	return {
		gridOptions,
		sideBar, // 侧边栏配置
		defaultColDef, // 默认列配置
		selectionColumnDef, // 选择列配置
		registerAgGridModules, // 注册 AG Grid 模块函数
	};
};

// 导入 AG Grid 模块
import { ModuleRegistry, ClientSideRowModelModule } from 'ag-grid-community';
import { FiltersToolPanelModule, ColumnsToolPanelModule } from 'ag-grid-enterprise';
// 注册 AG Grid 模块的函数
export const registerAgGridModules = () => {
	ModuleRegistry.registerModules([ClientSideRowModelModule, ColumnsToolPanelModule, FiltersToolPanelModule]);
};
