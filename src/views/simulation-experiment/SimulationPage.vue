<script setup lang="ts">
import { ref, computed, watch, onMounted, provide, reactive, nextTick } from 'vue';
import { useTitle } from '@vueuse/core'
import { useRoute, useRouter } from 'vue-router';
/* 配置 */
// 智能体配置
import AgentConfig from './configured/AgentConfig.vue';
// 市场规则参数
import MarketRuleParams from './configured/MarketRuleParams.vue';
// 模型配置
// import Configured from './configured/Configured.vue';
import Model from './configured/Model.vue';

/* 申报 */
// 日前市场
import DayAheadMarket from './declaration/DayAheadMarket/DayAheadMarket.vue';
// 辅助服务市场-调频-备用
import AuxiliaryServiceMarket from './declaration/AuxiliaryServiceMarket/AuxiliaryServiceMarket.vue';
// 辅助服务市场-爬坡
import RampModulation from './declaration/AuxiliaryServiceMarket/RampModulation.vue';

/* 验证 */
// 计算任务
import CalculateTask from './running/CalculateTask.vue';
// 出清结果
import ClearResult from './running/ClearResult.vue';
// 验证
import Verification from './running/Verification.vue';

/* 结算 */
import Results from './results/Results.vue';

import { getCaseInfo, type SiMu, exportBbModel } from '/@/views/config/api';
import { NextLoading } from '/@/utils/loading';
import { successNotification } from '/@/utils/message';

const title = useTitle()

onMounted(() => NextLoading.done(600));
const route = useRoute();
const router = useRouter();
const siMuInfo = ref<SiMu>({ id: '', market_rule_id: '', name: '', scene_model_id: '' });
const current = reactive({
	type: 'model',
	page: 'model',
});
watch(
	() => route.query,
	(newVal) => {
		current.type = (newVal.type as string) || 'model';
		current.page = (newVal.page as string) || 'model';
	},
	{
		immediate: true,
	}
);
async function getSiMuInfo() {
	const { id } = route.query;
	const res = await getCaseInfo(Number(id));
	siMuInfo.value = res.data;
	title.value = `模型-${res.data.name}`;
}
getSiMuInfo();

// 导航配置数据
const navConfig = ref<NavGroup[]>([
	{
		title: '配置',
		type: 'simple',
		items: [
			{
				key: 'model',
				label: '模型',
				icon: 'iconfont icon-a-weibiaoti-2_huaban1fuben',
				type: 'page'
			},
			{
				key: 'market-rule',
				label: '市场规则',
				icon: 'iconfont icon-a-weibiaoti-2_huaban1fuben5',
				type: 'action'
			},
			{
				key: 'agent-config',
				label: '智能体配置',
				icon: 'iconfont icon-a-weibiaoti-2_huaban1fuben4',
				type: 'page'
			}
		]
	},
	{
		title: '申报',
		type: 'composite',
		items: [
			{
				key: 'day-ahead-market',
				label: '日前市场',
				icon: '',
				subItems: [
					{
						key: 'user-day-ahead-market',
						label: '用户',
						icon: 'iconfont icon-riqianshichangyonghu',
						iconSize: 14
					},
					{
						key: 'unit-day-ahead-market',
						label: '机组',
						icon: 'iconfont icon-riqianshichangjizu',
						iconSize: 14
					}
				]
			},
			{
				key: 'auxiliary-service-market',
				label: '辅助服务市场',
				icon: '',
				subItems: [
					{
						key: 'frequency_modulation',
						label: '调频',
						icon: 'iconfont icon-tiaoping',
						iconSize: 14,
						hasDropdown: true,
						dropdownItems: [
							{
								key: 'unit',
								label: '机组',
								icon: 'iconfont icon-riqianshichangjizu',
								type: 'action',
								iconSize: 12
							},
							{
								key: 'system',
								label: '系统',
								icon: 'iconfont icon-xitongguanli',
								iconSize: 12
							}
						]
					},
					{
						key: 'backup_modulationt',
						label: '备用',
						icon: 'iconfont icon-beiyong',
						iconSize: 14,
						hasDropdown: true,
						dropdownItems: [
							{
								key: 'unit',
								label: '机组',
								icon: 'iconfont icon-riqianshichangjizu',
								type: 'action',
								iconSize: 12
							},
							{
								key: 'system',
								label: '系统',
								icon: 'iconfont icon-xitongguanli',
								iconSize: 12
							}
						]
					},
					{
						key: 'ramp_modulation',
						label: '爬坡',
						icon: 'iconfont icon-papo3',
						iconSize: 14
					}
				]
			}
		]
	},
	{
		title: '运行',
		type: 'simple',
		items: [
			{
				key: 'verification',
				label: '验证',
				icon: 'iconfont icon-yanzhengma',
				type: 'page'
			},
			{
				key: 'load-file',
				label: '加载文件',
				icon: 'iconfont icon-jiazaiwenjian',
				type: 'action'
			},
			{
				key: 'calculate-task',
				label: '计算',
				icon: 'iconfont icon-jisuan',
				type: 'page'
			},
			{
				key: 'clear-result',
				label: '结果',
				icon: 'iconfont icon-a-weibiaoti-2_huaban1fuben3',
				type: 'page'
			}
		]
	},
	{
		title: '结算',
		type: 'simple',
		items: [
			{
				key: 'settlement',
				label: '结算',
				icon: 'iconfont icon-a-weibiaoti-2_huaban1fuben6',
				iconSize: 20,
				type: 'page'
			}
		]
	}
]);

const currentComponent = computed(() => {
	// 配置 模型 市场规则
	if (['model'].includes(current.page)) return Model;
	// 智能体配置
	if (['agent-config'].includes(current.page)) return AgentConfig;
	// 申报 用户日前市场 机组日前市场 辅助服务市场 调频
	if (['frequency_modulation', 'backup_modulationt', 'backup-unit', 'backup-system'].includes(current.page)) return AuxiliaryServiceMarket;
	if (['user-day-ahead-market', 'unit-day-ahead-market'].includes(current.page)) return DayAheadMarket;
	if (['ramp_modulation'].includes(current.page)) return RampModulation;
	// 运行 计算 验证
	if (['verification'].includes(current.page)) return Verification;
	if (['calculate-task'].includes(current.page)) return CalculateTask;
	// 出清结果
	if (['clear-result'].includes(current.page)) return ClearResult;
	// 结算
	if (['settlement'].includes(current.page)) return Results;
});
const marketRuleParamsVisible = ref(false);
function handleTabs({ page, type }: { page?: string; type?: string }) {
	router.replace({ name: 'SimulationPage', query: { id: siMuInfo.value.id, page: page || current.page, type: type } });
	marketRuleParamsVisible.value = type === 'market-rule';

	if (type === 'load-file') {
		exportBbModel(Number(siMuInfo.value.id)).then((res: AxiosResponse) => {
			successNotification(res.msg);
		});
	}
}

function handleBack() {
	router.back();
}
const dataPacket = computed((): Recordable => {
	return {
		...siMuInfo.value,
	};
});
provide('dataPacket', dataPacket);

// 下拉菜单状态管理
const activeDropdown = ref<string | null>(null);

// 切换下拉菜单显示状态
function toggleDropdown(key: string, event: Event) {
	event.stopPropagation();
	activeDropdown.value = activeDropdown.value === key ? null : key;
}

// 关闭下拉菜单
function closeDropdown() {
	activeDropdown.value = null;
}

// 点击下拉菜单项
function handleDropdownItem(parentKey: string, itemKey: string) {
	handleTabs({ type: itemKey, page: parentKey });
	closeDropdown();
}

// 点击外部区域关闭下拉菜单
onMounted(() => {
	document.addEventListener('click', closeDropdown);
});

// 判断下拉菜单项是否激活
const isDropdownItemActive = (parentKey: string, itemKey: string) => {
	const combinedPage = `${parentKey}-${itemKey}`;
	return current.page+'-'+current.type === combinedPage;
};
</script>
<template>
	<div class="model-container bg-[#f7f7f7] h-full flex flex-col overflow-hidden">
		<div class="select-none bg-[#DFEBF9] p-2 flex rounded-md shadow-sm text-[#15428B] gap-2">
			<div v-for="group in navConfig" :key="group.title" class="flex items-center bg-[#C8D8ED] rounded-md border-[#567DB1] border-solid border-[1px] p-1 gap-2">
				<div class="py-1 bg-[#BACBE1] h-full w-[20px] text-[#003180] flex items-center text-center rounded-md">{{ group.title }}</div>
				
				<div v-if="group.type === 'simple'" class="flex gap-2">
					<div
						v-for="item in group.items"
						:key="item.key"
						@click="handleTabs({ page: item.type === 'page' ? item.key : undefined, type: item.type === 'action' ? item.key : undefined })"
						class="btn"
						:class="{ 'btn-active': current.page === item.key }"
					>
						<div class="icon">
							<SvgIcon :name="item.icon" :size="item.iconSize || 20" />
							<div class="text-[12px]">{{ item.label }}</div>
						</div>
					</div>
				</div>
				
				<div v-else-if="group.type === 'composite'" class="flex gap-2">
					<div
						v-for="item in group.items"
						:key="item.key"
						class="rounded-lg border-[#BACBE1] border-solid border-[1px] cursor-pointer hover:bg-[#F8FCFF]"
						:class="{ 'btn-active': item.subItems?.some(sub => current.page === sub.key) }"
					>
						<div class="flex items-center justify-center gap-2 px-3 py-2">
							<div
								v-for="subItem in item.subItems"
								:key="subItem.key"
								class="rounded-md icon hover-shadow flex items-center px-1 relative"
								:class="{ 'btn2-active': current.page === subItem.key }"
							>
								<!-- 主按钮 -->
								<div 
									@click="subItem.hasDropdown ? toggleDropdown(subItem.key, $event) : handleTabs({ page: subItem.key })"
									class="flex items-center cursor-pointer"
								>
									<SvgIcon :name="subItem.icon" :size="subItem.iconSize || 14" />
									<div class="text-[12px] px-1">{{ subItem.label }}</div>
									<!-- 下拉箭头 -->
									<div 
										v-if="subItem.hasDropdown" 
										class="ml-1 w-0 h-0 border-l-[3px] border-r-[3px] border-t-[4px] border-l-transparent border-r-transparent border-t-current transition-transform duration-200"
										:class="{ 'rotate-180': activeDropdown === subItem.key }"
									></div>
								</div>
								
								<!-- 下拉菜单 -->
								<div 
									v-if="subItem.hasDropdown && activeDropdown === subItem.key"
									class="dropdown-menu absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 min-w-[80px]"
								>
									<div 
										v-for="dropItem in subItem.dropdownItems"
										:key="dropItem.key"
										@click="handleDropdownItem(subItem.key, dropItem.key)"
										class="dropdown-item flex items-center px-3 py-2 text-[12px] hover:bg-gray-100 cursor-pointer"
										:class="{ 'dropdown-item-active': isDropdownItemActive(subItem.key, dropItem.key) }"
									>
										<SvgIcon 
											v-if="dropItem.icon" 
											:name="dropItem.icon" 
											:size="dropItem.iconSize || 12" 
											class="mr-2"
										/>
										<span>{{ dropItem.label }}</span>
									</div>
								</div>
							</div>
						</div>
						<div class="bg-[#BACBE1] text-center rounded-b-md text-[12px]" :class="{ 'px-4': item.key === 'auxiliary-service-market' }">
							{{ item.label }}
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="flex-1 overflow-auto">
			<template v-if="siMuInfo.id">
				<KeepAlive>
					<component :is="currentComponent" :siMuInfo="siMuInfo" />
				</KeepAlive>
			</template>
			<template v-else>
				<div>加载中...</div>
			</template>
		</div>
		<MarketRuleParams :ruleId="Number(siMuInfo.id)" v-model="marketRuleParamsVisible" />
	</div>
</template>
<style scoped>
.btn {
		@apply px-2 py-1 rounded-lg cursor-pointer hover:bg-[#F8FCFF] flex items-center justify-center;
	}

	.btn-active {
		@apply bg-[#F8FCFF];
	}

	.icon {
		@apply text-center min-w-[56px];
	}

	.hover-shadow {
		@apply hover:shadow-[0px_0px_5px_2px_rgba(175,217,255,0.63)];
	}

	.btn2-active {
		@apply shadow-[0px_0px_5px_2px_rgba(175,217,255,0.63)];
	}

	/* 下拉菜单样式 */
	.dropdown-menu {
		animation: dropdown-fade-in 0.15s ease-out;
	}

	.dropdown-item {
		transition: background-color 0.15s ease;
	}

	.dropdown-item-active {
		background-color: var(--el-color-primary-light-8) !important;
		color: var(--el-color-primary) !important;
	}

	.dropdown-item:first-child {
		border-radius: 0.375rem 0.375rem 0 0;
	}

	.dropdown-item:last-child {
		border-radius: 0 0 0.375rem 0.375rem;
	}

	@keyframes dropdown-fade-in {
		from {
			opacity: 0;
			transform: translateY(-4px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
</style>
