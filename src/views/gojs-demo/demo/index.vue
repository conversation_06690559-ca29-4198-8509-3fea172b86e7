<template>
	<div class="w-full h-full relative">
		<div ref="diagram" class="w-full h-full bg-white"></div>
		<div class="absolute top-10 left-10 z-10">
			<el-button @click="saveDiagram">保存</el-button>
			<el-button @click="loadDiagram">加载</el-button>
			<el-button @click="exportCoordinates" type="success">导出坐标</el-button>
			<el-button @click="exportImage" type="primary">导出图片</el-button>
		</div>
	</div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import * as go from 'gojs';
import { ElMessage } from 'element-plus';
import { ParallelRouteLink } from './ParallelRouteLink';
import { NodeData, LinkData, Point } from './types';
import { mockNodeData, mockLinkData } from './mock';

// 引用图表容器元素
const diagram = ref<HTMLDivElement | null>(null);
// 图表实例
let myDiagram: go.Diagram | null = null;
// GraphObject工厂函数
let $: any = null;

/**
 * 初始化图表
 * 主函数，负责协调各个初始化步骤
 */
const initDiagram = () => {
	if (!diagram.value) return;

	$ = go.GraphObject.make;

	// 创建图表实例并进行基础配置
	myDiagram = $(go.Diagram, diagram.value, {
		initialContentAlignment: go.Spot.Center,
		'undoManager.isEnabled': true,
		// 禁用创建新节点时的布局重新计算
		'toolManager.mouseWheelBehavior': go.ToolManager.WheelZoom,
		'clickCreatingTool.archetypeNodeData': {
			text: '新节点',
			color: '#00A4EF',
			// 确保新节点的位置是由鼠标点击位置决定，而不是布局
			// 这些属性会被实际的模板覆盖
			x: 0,
			y: 0,
		},
		// 禁用网格对齐以保持精确位置
		'draggingTool.isGridSnapEnabled': false,
		// 使用固定布局
		layout: $(go.GridLayout, {
			wrappingColumn: 1,
			cellSize: new go.Size(1, 1),
			spacing: new go.Size(1, 1),
			arrangement: go.GridLayout.LeftToRight,
			// 禁用GridLayout的位置计算，完全使用节点自身坐标
			isInitial: false,
			isOngoing: false,
			isViewportSized: false,
		}),
		// 禁用自动调整图表大小，保持节点精确位置
		InitialLayoutCompleted: (e: go.DiagramEvent) => {
			// 布局完成后，确保禁用自动布局
			if (e.diagram && e.diagram.layout) {
				e.diagram.layout.isInitial = false;
				e.diagram.layout.isOngoing = false;
			}
		},
		// 加载模型后禁用布局
		ModelChanged: (e: go.ChangedEvent) => {
			if (e.isTransactionFinished && e.diagram && e.diagram.layout) {
				e.diagram.layout.isInitial = false;
				e.diagram.layout.isOngoing = false;
			}
		},
	});

	// 设置节点模板
	setupNodeTemplates();

	// 设置连接线模板
	setupLinkTemplate();

	// 设置布局参数
	setupLayout();

	// 加载图表数据
	loadGraphData();
};

/**
 * 设置节点模板
 * 创建变电站和发电厂两种类型的节点模板
 */
const setupNodeTemplates = () => {
	if (!myDiagram || !$) return;

	// 变电站节点模板
	const stationTemplate = $(
		go.Node,
		'Spot',
		{
			locationSpot: go.Spot.Center,
			// 添加位置绑定
			locationObjectName: 'SHAPE',
		},
		// 使用单独的绑定设置位置
		new go.Binding('location', '', (data: NodeData) => {
			if (data.x !== undefined && data.y !== undefined) {
				return new go.Point(data.x, data.y);
			}
			return new go.Point(0, 0);
		}),
		$(go.Shape, 'Circle', {
			name: 'SHAPE', // 为了locationObjectName引用
			width: 70,
			height: 70,
			fill: 'white',
			strokeWidth: 2,
			stroke: '#1890ff',
		}),
		$(
			go.Panel,
			'Vertical',
			{ defaultAlignment: go.Spot.Center },
			$(
				go.TextBlock,
				{
					margin: new go.Margin(4, 4, 2, 4),
					font: 'bold 13px sans-serif',
					stroke: '#1890ff',
				},
				new go.Binding('text', 'name')
			),
			$(go.Shape, 'TriangleUp', {
				width: 8,
				height: 8,
				fill: '#1890ff',
				stroke: null,
			}),
			$(
				go.TextBlock,
				{
					margin: new go.Margin(2, 4),
					font: '11px sans-serif',
					stroke: '#1890ff',
				},
				new go.Binding('text', '', (data) => `P(M):${data.powerM}`)
			),
			$(
				go.TextBlock,
				{
					margin: new go.Margin(2, 4),
					font: '11px sans-serif',
					stroke: '#1890ff',
				},
				new go.Binding('text', '', (data) => `P:${data.powerP}`)
			)
		)
	);

	// 发电厂模板
	const plantTemplate = $(
		go.Node,
		'Spot',
		{
			locationSpot: go.Spot.Center,
			// 添加位置绑定
			locationObjectName: 'SHAPE',
		},
		// 使用单独的绑定设置位置
		new go.Binding('location', '', (data: NodeData) => {
			if (data.x !== undefined && data.y !== undefined) {
				return new go.Point(data.x, data.y);
			}
			return new go.Point(0, 0);
		}),
		$(go.Shape, 'Circle', {
			name: 'SHAPE', // 为了locationObjectName引用
			width: 70,
			height: 70,
			fill: 'white',
			strokeWidth: 1.5,
			stroke: '#333',
		}),
		$(go.Shape, {
			geometryString: 'M -15 0 C -5 -10 5 10 15 0',
			stroke: '#333',
			strokeWidth: 1.5,
			width: 30,
			height: 15,
			fill: null,
		}),
		$(
			go.Panel,
			'Vertical',
			{
				position: new go.Point(0, 25),
				defaultAlignment: go.Spot.Center,
			},
			$(
				go.TextBlock,
				{
					margin: new go.Margin(2, 4),
					font: 'bold 13px sans-serif',
					stroke: '#333',
				},
				new go.Binding('text', 'name')
			),
			$(
				go.TextBlock,
				{
					margin: new go.Margin(2, 4),
					font: '11px sans-serif',
					stroke: '#666',
				},
				new go.Binding('text', '', (data) => `P(M):${data.powerM}`)
			),
			$(
				go.TextBlock,
				{
					margin: new go.Margin(2, 4),
					font: '11px sans-serif',
					stroke: '#666',
				},
				new go.Binding('text', '', (data) => `P:${data.powerP}`)
			)
		)
	);

	// 创建模板映射
	const templmap = new go.Map<string, go.Part>();
	templmap.add('', stationTemplate); // 默认模板为变电站
	templmap.add('plant', plantTemplate); // 发电厂模板
	myDiagram.nodeTemplateMap = templmap;
};

/**
 * 设置连接线模板
 * 创建可视化电力连接线的样式
 */
const setupLinkTemplate = () => {
	if (!myDiagram || !$) return;

	// 使用 ParallelRouteLink 创建连接线模板
	myDiagram.linkTemplate = new ParallelRouteLink({
		relinkableFrom: true,
		relinkableTo: true,
		reshapable: true,
		// 下面设置确保线条不会有折线
		curve: go.Link.None,
		layerName: 'Background',
		// 移除这里的路径点绑定，它将被分别处理
	})
		.add(
			// 主线条
			new go.Shape({
				strokeWidth: 2,
				stroke: 'red',
				name: 'SHAPE',
				strokeDashArray: [0], // 实线
			})
				.bind('stroke', '', (data: LinkData) => {
					// 基于通道类型选择基础颜色
					let baseColor = 'red'; // 默认为红色
					if (data.channelType === 'backup') baseColor = 'green';
					if (data.channelType === 'emergency') baseColor = 'blue';

					// 基于状态调整颜色
					if (data.status === 'warning') return 'orange';
					if (data.status === 'offline') return 'gray';
					if (data.status === 'maintenance') return 'purple';

					// 如果有自定义颜色，优先使用
					return data.color || baseColor;
				})
				.bind('strokeDashArray', '', (data: LinkData) => {
					if (data.status === 'maintenance') return [4, 4]; // 维护状态显示虚线
					if (data.status === 'offline') return [2, 2]; // 离线状态显示短虚线
					return [0]; // 默认为实线
				})
				.bind('strokeWidth', '', (data: LinkData) => {
					// 主要线路可以稍微粗一些
					return data.channelType === 'normal' ? 2 : 1.5;
				})
		)
		.add(
			// 中间箭头
			new go.Shape({
				toArrow: 'OpenTriangle', // 箭头样式：开放三角形
				stroke: 'red',
				fill: 'red',
				scale: 1.8, // 增大箭头尺寸
				segmentIndex: 0,
				segmentFraction: 0.5, // 确保箭头位于线段中间
				segmentOrientation: go.Link.OrientAlong, // 箭头沿线方向
			})
				.bind('stroke', '', (data: LinkData) => {
					// 基于通道类型选择基础颜色
					let baseColor = 'red'; // 默认为红色
					if (data.channelType === 'backup') baseColor = 'green';
					if (data.channelType === 'emergency') baseColor = 'blue';

					// 基于状态调整颜色
					if (data.status === 'warning') return 'orange';
					if (data.status === 'offline') return 'gray';
					if (data.status === 'maintenance') return 'purple';

					// 如果有自定义颜色，优先使用
					return data.color || baseColor;
				})
				.bind('fill', '', (data: LinkData) => {
					// 基于通道类型选择基础颜色
					let baseColor = 'red'; // 默认为红色
					if (data.channelType === 'backup') baseColor = 'green';
					if (data.channelType === 'emergency') baseColor = 'blue';

					// 基于状态调整颜色
					if (data.status === 'warning') return 'orange';
					if (data.status === 'offline') return 'gray';
					if (data.status === 'maintenance') return 'purple';

					// 如果有自定义颜色，优先使用
					return data.color || baseColor;
				})
				.bind('scale', '', (data: LinkData) => {
					// 调整箭头大小
					return data.status === 'offline' ? 1.5 : 1.8;
				})
				// 添加对 segmentIndex 的动态绑定，以适应多线路情况
				.bind('segmentIndex', '', (data: LinkData) => {
					// 获取线条数量
					const lineCount = data.lineCount || 1;
					// 对于多条线路，ParallelRouteLink 会插入额外控制点
					// 单线时不需要特殊处理，segmentIndex 保持为 0
					// 多线时需要将 segmentIndex 设为1，因为 computePoints 会在索引1处插入控制点
					return lineCount > 1 ? 1 : 0;
				})
				// 添加对 segmentFraction 的动态绑定
				.bind('segmentFraction', '', (data: LinkData) => {
					// 获取线条数量和索引
					const lineCount = data.lineCount || 1;
					const lineIndex = data.lineIndex || 0;

					// 对于多线路情况，需要根据曲率调整分数以保持箭头在视觉上的中央位置
					if (lineCount > 1) {
						if (lineCount === 2) {
							// 双线情况
							// 由于曲率为 ±20，箭头需要适当调整
							return lineIndex === 0 ? 1 : 1; // 左右线需要稍微偏向下游
						} else if (lineCount === 3) {
							// 三线情况
							if (lineIndex === 0) {
								// 左侧线，曲率更大(-30)，需要更多调整
								return 1;
							} else if (lineIndex === 1) {
								// 中间线，曲率为0，保持中央
								return 0.05;
							} else {
								// 右侧线，曲率更大(+30)，需要更多调整
								return 1;
							}
						}
					}

					// 单线情况保持默认0.5
					return 0.5;
				})
		)
		.add(
			// 数值标签
			new go.TextBlock({
				segmentOffset: new go.Point(0, -15), // 标签相对于线的垂直偏移
				font: '12px sans-serif',
				stroke: '#333',
				background: 'rgba(255, 255, 255, 0.9)', // 半透明白色背景提高可读性
				segmentOrientation: go.Link.OrientUpright, // 保持文本正向显示
				segmentIndex: 0,
				segmentFraction: 0.5, // 文字位置在线段中间
			})
				.bind('text', 'value')
				// 添加与箭头相同的 segmentIndex 动态绑定
				.bind('segmentIndex', '', (data: LinkData) => {
					// 获取线条数量
					const lineCount = data.lineCount || 1;
					// 与箭头使用相同的逻辑
					return lineCount > 1 ? 1 : 0;
				})
				// 添加与箭头相同的 segmentFraction 动态绑定
				.bind('segmentFraction', '', (data: LinkData) => {
					// 获取线条数量和索引
					const lineCount = data.lineCount || 1;
					const lineIndex = data.lineIndex || 0;

					// 使用与箭头相同的位置计算逻辑
					if (lineCount > 1) {
						if (lineCount === 2) {
							// 双线情况
							return lineIndex === 0 ? 1 : 1;
						} else if (lineCount === 3) {
							// 三线情况
							if (lineIndex === 0) {
								// 左侧线
								return 1;
							} else if (lineIndex === 1) {
								// 中间线
								return 0.05;
							} else {
								// 右侧线
								return 1;
							}
						}
					}

					// 单线情况
					return 0.5;
				})
		);
};

/**
 * 设置图表布局
 * 确保禁用自动布局，完全使用节点自身坐标
 */
const setupLayout = () => {
	if (!myDiagram) return;

	// 确保布局不会自动计算节点位置
	if (myDiagram.layout instanceof go.GridLayout) {
		const layout = myDiagram.layout as go.GridLayout;
		// 完全禁用布局算法
		layout.isInitial = false; // 禁止初始布局
		layout.isOngoing = false; // 禁止持续布局

		// 移除任何可能的布局计算
		layout.wrappingColumn = 1;
		layout.cellSize = new go.Size(1, 1);
		layout.spacing = new go.Size(1, 1);
	}

	// 禁用任何可能的自动布局
	myDiagram.layout.isInitial = false;
	myDiagram.layout.isOngoing = false;
};

/**
 * 将后端API数据转换为GoJS可用的节点数据
 * @param apiData API返回的原始数据
 * @returns 转换后的NodeData数组
 */
const convertToNodeData = (apiData: any[]): NodeData[] => {
	if (!apiData || !Array.isArray(apiData)) return [];

	return apiData.map((item) => {
		// 确定节点类型
		const nodeType = determineNodeType(item.sub_type);

		// 创建节点数据
		const nodeData: NodeData = {
			key: item.id || item.graph_node_id || '', // 使用ID作为唯一标识
			name: item.label || '', // 节点名称
			type: nodeType, // 节点类型
			category: nodeType === 'plant' ? 'plant' : '', // 类别，用于选择模板
			powerM: parseFloat(item.dt_p_mw || item.p_mw || 0), // 有功功率
			powerP: parseFloat(item.dt_q_mvar || item.q_mvar || 0), // 无功功率
			x: parseFloat(item.x || 0), // x坐标
			y: parseFloat(item.y || 0), // y坐标
			subType: item.sub_type || '', // 子类型
			voltageLevel: item.basevoltage_name || '', // 电压等级
			zoneName: item.zone_name || '', // 区域名称
			rawData: item, // 保存原始数据
		};

		return nodeData;
	});
};

/**
 * 确定节点类型
 * @param subType 子类型字符串
 * @returns 节点类型
 */
const determineNodeType = (subType: string): 'station' | 'plant' => {
	if (!subType) return 'station';

	// 根据子类型确定节点类型
	const lowerType = subType.toLowerCase();
	if (lowerType.includes('厂') || lowerType.includes('plant')) {
		return 'plant';
	}
	return 'station';
};

/**
 * 将后端API数据转换为GoJS可用的连接线数据
 * @param apiData API返回的原始数据
 * @returns 转换后的LinkData数组
 */
const convertToLinkData = (apiData: any[]): LinkData[] => {
	if (!apiData || !Array.isArray(apiData)) return [];

	return apiData.map((item) => {
		// 处理lineList字符串，转换为点数组
		let points: Point[] = [];
		if (item.lineList) {
			try {
				// 尝试解析字符串为JSON
				const lineListData = JSON.parse(item.lineList);
				// 如果是数组，直接使用
				if (Array.isArray(lineListData)) {
					points = lineListData.map((point) => ({
						x: parseFloat(point.x || 0),
						y: parseFloat(point.y || 0),
					}));
				}
			} catch (error) {
				console.error('解析lineList失败:', error);
			}
		}

		// 创建连接线数据
		const linkData: LinkData = {
			from: item.source || item.from_bus || '', // 起点ID
			to: item.target || item.to_bus || '', // 终点ID
			value: item.dt_p_from_mw || item.p_from_mw || '0', // 显示值
			color: determineLineColor(item), // 线路颜色
			direction: item.p_direction > 0 ? 'forward' : 'backward', // 方向
			lineCount: 1, // 默认为单线
			lineIndex: 0, // 默认索引
			isOutbound: true, // 默认为出站线路
			channelType: 'normal', // 默认通道类型
			status: 'normal', // 默认状态
			lineId: item.line_id || item.graph_line_id || '', // 线路ID
			lineName: item.line_name || '', // 线路名称
			fromName: item.from_substation_name || '', // 起点名称
			toName: item.to_substation_name || '', // 终点名称
			points: points, // 路径点
			rawData: item, // 保存原始数据
		};

		return linkData;
	});
};

/**
 * 确定线路颜色
 * @param item 线路数据项
 * @returns 颜色值
 */
const determineLineColor = (item: any): string => {
	// 这里可以根据实际需求添加颜色判断逻辑
	// 例如可以根据电压等级、负载率等因素决定颜色

	// 默认返回蓝色
	return '#1890ff';
};

/**
 * 加载图表数据
 * 可以从API获取数据或使用模拟数据
 * @param useApi 是否使用API获取数据
 * @param nodeData 可选的节点数据，如果不提供则使用模拟数据
 * @param linkData 可选的连接线数据，如果不提供则使用模拟数据
 */
const loadGraphData = async (useApi = false, nodeData?: NodeData[], linkData?: LinkData[]) => {
	if (!myDiagram) return;

	nodeData = nodeData || mockNodeData;
	linkData = linkData || mockLinkData;

	// 设置图表模型
	myDiagram.model = new go.GraphLinksModel(nodeData, linkData);
};

/**
 * 保存图表数据到本地存储
 */
const saveDiagram = () => {
	if (!myDiagram) return;
	const json = myDiagram.model.toJson();
	localStorage.setItem('diagramData', json);
	ElMessage.success('图表已保存');
};

/**
 * 从本地存储加载图表数据
 */
const loadDiagram = () => {
	if (!myDiagram) return;
	const json = localStorage.getItem('diagramData');
	if (json) {
		myDiagram.model = go.Model.fromJson(json);
		ElMessage.success('图表已加载');
	} else {
		ElMessage.warning('没有找到保存的图表数据');
	}
};

/**
 * 导出坐标数据为可替换 mock.ts 文件的格式
 */
const exportCoordinates = () => {
	if (!myDiagram) {
		ElMessage.warning('图表未初始化');
		return;
	}

	// 使用非空断言，告诉TypeScript此处myDiagram不为null
	const diagram = myDiagram as go.Diagram;

	// 获取当前所有节点数据
	const nodeArray = diagram.model.nodeDataArray;

	// 构建格式化的输出
	let output = `import { NodeData, LinkData } from './types';\n\n`;
	output += `// 节点模拟数据\n`;
	output += `export const mockNodeData: NodeData[] = [\n`;

	// 对每个节点，格式化为与 mock.ts 相同的格式
	nodeArray.forEach((node: any, index: number) => {
		output += `\t{\n`;
		output += `\t\tkey: '${node.key}',\n`;
		output += `\t\tname: '${node.name}',\n`;
		output += `\t\ttype: '${node.type}',\n`;
		output += `\t\tpowerM: ${node.powerM},\n`;
		output += `\t\tpowerP: ${node.powerP},\n`;
		output += `\t\tcategory: '${node.category}',\n`;

		// 获取节点在图表中的当前位置
		const nodeObj = diagram.findNodeForData(node);
		if (nodeObj) {
			const loc = nodeObj.location;
			output += `\t\tx: ${loc.x.toFixed(2)},\n`;
			output += `\t\ty: ${loc.y.toFixed(2)},\n`;
		} else {
			// 如果找不到节点，使用原始数据
			output += `\t\tx: ${node.x || 0},\n`;
			output += `\t\ty: ${node.y || 0},\n`;
		}

		output += `\t}${index < nodeArray.length - 1 ? ',' : ''}\n`;
	});

	output += `];\n\n`;

	// 保留连接线数据的引用
	output += `// 连接线模拟数据部分保持不变\n`;
	output += `// 请从原始文件中复制连接线数据部分\n`;

	// 复制到剪贴板
	navigator.clipboard
		.writeText(output)
		.then(() => {
			ElMessage.success('坐标数据已复制到剪贴板，可粘贴到 mock.ts 文件中');
		})
		.catch((err) => {
			console.error('无法复制到剪贴板:', err);
			// 如果无法复制到剪贴板，则在控制台输出
			console.log('请手动复制以下内容:');
			console.log(output);
			ElMessage.warning('无法复制到剪贴板，请查看控制台输出');
		});
};

/**
 * 导出图表为PNG图片
 */
const exportImage = () => {
	if (!myDiagram) {
		ElMessage.warning('图表未初始化');
		return;
	}

	try {
		// 使用GoJS的makeImageData方法生成图片数据
		const imgData = myDiagram.makeImageData({
			background: 'white', // 设置背景色
			scale: 1.0, // 设置图片缩放比例
			maxSize: new go.Size(2000, 2000), // 设置最大尺寸
			type: 'image/png', // 设置图片类型
		});

		// 确保imgData不为null且为字符串类型（data URL）
		if (imgData && typeof imgData === 'string') {
			// 创建虚拟下载链接并触发点击
			const downloadLink = document.createElement('a');
			downloadLink.download = `电力网络图_${new Date().toLocaleDateString().replace(/\//g, '-')}.png`;
			downloadLink.href = imgData;
			document.body.appendChild(downloadLink);
			downloadLink.click();
			document.body.removeChild(downloadLink);

			ElMessage.success('图片导出成功');
		} else {
			console.error('生成的图片数据格式不正确:', imgData);
			ElMessage.warning('生成图片数据失败');
		}
	} catch (error) {
		console.error('导出图片失败:', error);
		ElMessage.error('导出图片失败，请查看控制台了解详情');
	}
};

/**
 * 重新加载图表数据
 * 可用于刷新数据或从API加载数据
 */
const refreshData = (useApi = true) => {
	loadGraphData(useApi);
};

// 组件挂载时初始化图表
onMounted(() => {
	initDiagram();
});
</script>
