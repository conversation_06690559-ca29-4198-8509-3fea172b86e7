// 节点数据接口
export interface NodeData {
	// 基本属性
	key: string; // 节点唯一标识，对应后端的id或graph_node_id
	name: string; // 节点名称，对应后端的label
	type: 'station' | 'plant'; // 节点类型
	category: string; // 节点分类，用于模板选择

	// 电力相关属性
	powerM: number; // 有功功率，对应后端的dt_p_mw或p_mw
	powerP: number; // 无功功率，对应后端的dt_q_mvar或q_mvar
	subType?: string; // 子类型，对应后端的sub_type
	voltageLevel?: string; // 电压等级，对应后端的basevoltage_name
	zoneName?: string; // 区域名称，对应后端的zone_name

	// 坐标位置
	x: number; // x坐标
	y: number; // y坐标

	// 原始数据，可选
	rawData?: any; // 保存原始数据，方便后续使用
}

// 坐标点接口
export interface Point {
	x: number;
	y: number;
}

// 连接线数据接口
export interface LinkData {
	// 基本属性
	from: string; // 起点节点id，对应后端的source
	to: string; // 终点节点id，对应后端的target
	value: string; // 显示值
	color: string; // 线条颜色
	direction: string; // 方向

	// 线路特性
	lineCount: number; // 线路数量
	lineIndex: number; // 当前线路索引
	isOutbound: boolean; // 是否为出站线路
	channelType: 'normal' | 'backup' | 'emergency'; // 通道类型
	status: 'normal' | 'warning' | 'offline' | 'maintenance'; // 线路状态

	// 线路信息
	lineId?: string; // 线路ID，对应后端的line_id或graph_line_id
	lineName?: string; // 线路名称，对应后端的line_name
	fromName?: string; // 起点名称，对应后端的from_substation_name
	toName?: string; // 终点名称，对应后端的to_substation_name

	// 路径点，可选
	points?: Point[]; // 路径点数组，用于绘制弯曲线路

	// 原始数据，可选
	rawData?: any; // 保存原始数据，方便后续使用
}
