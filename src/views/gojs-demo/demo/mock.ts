import { NodeData, LinkData } from './types';

// 节点模拟数据
export const mockNodeData: NodeData[] = [
	{
		key: 'mohaiStation',
		name: '莫河站',
		type: 'station',
		powerM: 286.19,
		powerP: 272.82,
		category: 'station',
		x: -610.0,
		y: 40.0,
	},
	{
		key: 'chaoyangStation',
		name: '朝阳站',
		type: 'station',
		powerM: -69.72,
		powerP: -62.54,
		category: 'station',
		x: -200.0,
		y: -150.0,
	},
	{
		key: 'pengdeStation',
		name: '彭德站',
		type: 'station',
		powerM: -52.16,
		powerP: -44.29,
		category: 'station',
		x: 180.0,
		y: -120.0,
	},
	{
		key: 'fenghePlant',
		name: '丰鹤厂',
		type: 'plant',
		powerM: -222.45,
		powerP: -222.46,
		category: 'plant',
		x: -750.0,
		y: 420.0,
	},
	{
		key: 'luanStation',
		name: '滦安站',
		type: 'station',
		powerM: 98.86,
		powerP: 95.4,
		category: 'station',
		x: 50.0,
		y: 520.0,
	},
	{
		key: 'hebeiStation',
		name: '河北站',
		type: 'station',
		powerM: 0,
		powerP: 0,
		category: 'station',
		x: 220.0,
		y: 0.0,
	},
];

// 连接线模拟数据
export const mockLinkData: LinkData[] = [
	// 1. 单通道正常情况 - 彭德站到莫河站（以水蓝色显示）
	{
		from: 'pengdeStation',
		to: 'mohaiStation',
		value: '61.39',
		color: '#1890ff', // 使用水蓝色
		direction: 'forward',
		lineCount: 1, // 单线
		lineIndex: 0,
		isOutbound: true,
		channelType: 'normal',
		status: 'normal',
	},

	// 2. 双通道正常情况 - 朝阳站到莫河站（以红色显示）
	{
		from: 'chaoyangStation',
		to: 'mohaiStation',
		value: '61.44',
		color: 'red',
		direction: 'forward',
		lineCount: 2, // 双线
		lineIndex: 0, // 第一条线
		isOutbound: true,
		channelType: 'normal',
		status: 'normal',
	},
	{
		from: 'chaoyangStation',
		to: 'mohaiStation',
		value: '61.46',
		color: 'red',
		direction: 'forward',
		lineCount: 2, // 双线
		lineIndex: 1, // 第二条线
		isOutbound: true,
		channelType: 'normal',
		status: 'normal',
	},

	// 3. 三通道正常情况 - 莫河站到丰鹤厂（以绿色显示）
	{
		from: 'mohaiStation',
		to: 'fenghePlant',
		value: '73.82',
		color: 'green',
		direction: 'forward',
		lineCount: 3, // 三线
		lineIndex: 0, // 第一条线（左侧）
		isOutbound: true,
		channelType: 'normal',
		status: 'normal',
	},
	{
		from: 'mohaiStation',
		to: 'fenghePlant',
		value: '73.85',
		color: 'green',
		direction: 'forward',
		lineCount: 3, // 三线
		lineIndex: 1, // 第二条线（中间）
		isOutbound: true,
		channelType: 'normal',
		status: 'normal',
	},
	{
		from: 'mohaiStation',
		to: 'fenghePlant',
		value: '73.80',
		color: 'green',
		direction: 'forward',
		lineCount: 3, // 三线
		lineIndex: 2, // 第三条线（右侧）
		isOutbound: true,
		channelType: 'normal',
		status: 'normal',
	},

	// 4. 丰鹤厂到莫河站（单线橙色告警状态）
	// {
	// 	from: 'fenghePlant',
	// 	to: 'mohaiStation',
	// 	value: '40.00',
	// 	color: 'orange',
	// 	direction: 'forward',
	// 	lineCount: 1,
	// 	lineIndex: 0,
	// 	isOutbound: true,
	// 	channelType: 'normal',
	// 	status: 'warning',
	// },

	// 5. 双通道 - 莫河站到滦安站（正常绿色）
	{
		from: 'mohaiStation',
		to: 'luanStation',
		value: '102.39',
		color: 'green',
		direction: 'forward',
		lineCount: 2,
		lineIndex: 0,
		isOutbound: true,
		channelType: 'normal',
		status: 'normal',
	},
	{
		from: 'mohaiStation',
		to: 'luanStation',
		value: '102.37',
		color: 'green',
		direction: 'forward',
		lineCount: 2,
		lineIndex: 1,
		isOutbound: true,
		channelType: 'normal',
		status: 'normal',
	},

	// 6. 滦安站到河北站（单线紫色维护状态）
	{
		from: 'luanStation',
		to: 'hebeiStation',
		value: '0.00',
		color: 'purple',
		direction: 'forward',
		lineCount: 1,
		lineIndex: 0,
		isOutbound: true,
		channelType: 'normal',
		status: 'maintenance',
	},

	// 7. 朝阳站到河北站（单线蓝色紧急通道）
	{
		from: 'chaoyangStation',
		to: 'hebeiStation',
		value: '23.55',
		color: 'blue',
		direction: 'forward',
		lineCount: 1,
		lineIndex: 0,
		isOutbound: true,
		channelType: 'emergency',
		status: 'normal',
	},
];
