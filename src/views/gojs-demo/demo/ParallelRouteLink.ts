﻿/*
 *  Copyright 1998-2025 by Northwoods Software Corporation. All Rights Reserved.
 */

/*
 * This is an extension and not part of the main GoJS library.
 * The source code for this is at extensionsJSM/ParallelRouteLink.ts.
 * Note that the API for this class may change with any version, even point releases.
 * If you intend to use an extension in production, you should copy the code to your own source directory.
 * Extensions can be found in the GoJS kit under the extensions or extensionsJSM folders.
 * See the Extensions intro page (https://gojs.net/latest/intro/extensions.html) for more information.
 */

import * as go from 'gojs';

/**
 * 扩展的连接线类，用于创建并行路由连接线效果
 * 支持多条平行线路（最多支持三条线）显示在同一对节点之间
 * 特点：
 * 1. 自动计算平行线路的偏移量
 * 2. 根据 lineCount 和 lineIndex 属性调整线条位置
 * 3. 确保箭头在每条线上都处于中央位置
 * 4. 支持多种状态和类型的线条显示
 *
 * 使用方法:
 * 1. 设置连接数据中的 lineCount 属性（1-3）表示总共有几条线
 * 2. 设置 lineIndex 属性（0-2）表示当前是第几条线
 * 3. 其他属性如 color, status 等可用于控制线条样式
 *
 * @category Part Extension
 */
export class ParallelRouteLink extends go.Link {
	constructor(init?: Partial<ParallelRouteLink>) {
		super();
		if (init) Object.assign(this, init);
	}

	/**
	 * 计算连接线路径点
	 * 根据线条总数和当前线条索引计算曲率偏移
	 * @returns true 如果成功计算了路由
	 */
	override computePoints(): boolean {
		const result = super.computePoints();
		if (!this.isOrthogonal && this.curve !== go.Curve.Bezier && this.hasCurviness()) {
			const curv = this.computeCurviness();

			// 如果没有指定曲率，则不执行偏移计算
			if (curv !== 0) {
				// 获取线段的端点
				const num = this.pointsCount;
				let pidx = 0;
				let qidx = num - 1;
				if (num >= 4) {
					pidx++;
					qidx--;
				}

				const frompt = this.getPoint(pidx); // 起始点
				const topt = this.getPoint(qidx); // 终点
				const dx = topt.x - frompt.x; // x方向差值
				const dy = topt.y - frompt.y; // y方向差值

				// 计算线路中间控制点的位置（前1/8处）
				let mx = frompt.x + (dx * 1) / 8;
				let my = frompt.y + (dy * 1) / 8;
				let px = mx;
				let py = my;

				// 处理水平线的特殊情况
				if (-0.01 < dy && dy < 0.01) {
					if (dx > 0) py -= curv;
					else py += curv;
				} else {
					// 非水平线的偏移计算
					const slope = -dx / dy; // 垂直于原线段的斜率
					let e = Math.sqrt((curv * curv) / (slope * slope + 1));
					if (curv < 0) e = -e;
					px = (dy < 0 ? -1 : 1) * e + mx;
					py = slope * (px - mx) + my;
				}

				// 计算线路中间控制点的位置（后7/8处）
				mx = frompt.x + (dx * 7) / 8;
				my = frompt.y + (dy * 7) / 8;
				let qx = mx;
				let qy = my;

				// 与上面相同的逻辑，处理线段终点附近的控制点
				if (-0.01 < dy && dy < 0.01) {
					if (dx > 0) qy -= curv;
					else qy += curv;
				} else {
					const slope = -dx / dy;
					let e = Math.sqrt((curv * curv) / (slope * slope + 1));
					if (curv < 0) e = -e;
					qx = (dy < 0 ? -1 : 1) * e + mx;
					qy = slope * (qx - mx) + my;
				}

				// 插入计算出的控制点，形成曲线路径
				this.insertPointAt(pidx + 1, px, py);
				this.insertPointAt(qidx + 1, qx, qy);
			}
		}
		return result;
	}

	/**
	 * 重写计算曲率方法，根据线条数量和当前线条索引自动调整曲率
	 * 以实现多条平行线路效果
	 * @returns 计算后的曲率值
	 */
	override computeCurviness(): number {
		// 获取连接数据
		const linkdata = this.data;
		if (linkdata) {
			// 线路总数，默认为1
			const lineCount = linkdata.lineCount || 1;
			// 当前线条索引，默认为0（第一条线）
			const lineIndex = linkdata.lineIndex || 0;

			// 基础曲率值，可根据实际需要调整
			const baseCurviness = 20;

			// 根据线条总数和当前索引计算曲率偏移量
			if (lineCount === 1) {
				// 单线情况，居中显示
				return 0;
			} else if (lineCount === 2) {
				// 双线情况，分别偏移
				return lineIndex === 0 ? -baseCurviness : baseCurviness;
			} else if (lineCount === 3) {
				// 三线情况，一条居中，另外两条向两侧偏移
				if (lineIndex === 0) return -baseCurviness * 1.5;
				if (lineIndex === 1) return 0;
				if (lineIndex === 2) return baseCurviness * 1.5;
			}
		}

		// 默认返回父类的计算结果
		return super.computeCurviness();
	}
}
