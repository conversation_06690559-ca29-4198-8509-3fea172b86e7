<template>
	<div class="h-full w-full">
		<SingleLineGraph ref="singleLineGraphRef" :node-info="{ name: '演示数据', key: '113997365567822514' }" @node-selected="handleNodeSelected" />
		<SingleLineNodeEditor :diagram="diagram" :selected-node-data="selectedNodeData" />
	</div>
</template>

<script setup lang="ts">
import { provide, computed, ref } from 'vue';
import SingleLineGraph from '/@/views/power-flow-calculation/graph/single-line/index.vue';
import SingleLineNodeEditor from '/@/components/diagram/SingleLineNodeEditor.vue';
const dataPacket = computed(() => ({ id: '2' }));
provide('dataPacket', dataPacket);
const selectedNodeData = ref<any>(null);
const singleLineGraphRef = ref<InstanceType<typeof SingleLineGraph> | null>(null);
const diagram = computed(() => {
	return singleLineGraphRef.value?.diagram || null;
});

const handleNodeSelected = (node: any) => {
	selectedNodeData.value = {
		...node,
	};
};
</script>

<style lang="scss" scoped></style>
