<template>
	<div class="h-full w-full">
		<SubstationGraph ref="substationGraphRef" :show-edit-node="true" v-model:loading="loading" :auto-initialize="true" />
	</div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import SubstationGraph from '/@/views/components/gojs/substation/index.vue';
import { query_substation_graph } from '/@/views/power-flow-calculation/api';
import { determineNodeType, getVoltageColor } from '/@/config/GraphConfig';
const substationGraphRef = ref<InstanceType<typeof SubstationGraph> | null>(null);
const nodeData = ref<SubstationNode[]>([]);
const linkData = ref<SubstationLink[]>([]);
const loading = ref(true);
loadSubstationData();
/**
 * 加载变电站图表数据
 * 由于启用了自动预初始化，图表容器已经显示，这里只需要加载数据
 */
async function loadSubstationData() {
	try {
		const { data } = await query_substation_graph({
			bb_case_id: '1',
			bb_diagram_layer_id: '34',
		});

		nodeData.value = convertToNodeData(data.nodes);
		linkData.value = convertToLinkData(data.edges);

		substationGraphRef.value?.loadGraphData(nodeData.value, linkData.value);
	} catch (error) {
		console.error('获取变电站数据失败:', error);
	} finally {
	}
}

/**
 * 转换节点数据
 */
const convertToNodeData = (data: any[]): SubstationNode[] => {
	return data.map(
		(item): SubstationNode => ({
			key: item.id,
			name: item.name,
			type: item.type,
			category: determineNodeType(item.type),
			voltage: item.voltage || '',
			properties: item.properties,
			color: getVoltageColor(item.voltage),
			zone: item.zone,
		})
	);
};

/**
 * 转换连接线数据
 */
const convertToLinkData = (data: any[]): SubstationLink[] => {
	return data.map((item) => {
		return {
			name: item.name,
			source: item.source,
			target: item.target,
			color: getVoltageColor(item.voltage),
			voltage: item.voltage,
			properties: item.properties,
		};
	});
};
</script>

<style lang="scss" scoped></style>
