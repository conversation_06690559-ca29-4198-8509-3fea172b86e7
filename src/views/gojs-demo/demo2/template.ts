import * as go from 'gojs';
import { GraphObject, Node, Part, Panel } from 'gojs';
import logo from '/@/assets/img/station_images/srb.png';
import jlxl from '/@/assets/img/station_images/jlxl.png';
import bldkq from '/@/assets/img/station_images/bldkq.png';
import bldrq from '/@/assets/img/station_images/bldrq.png';
import dz from '/@/assets/img/station_images/dz.png';
import dzdww from '/@/assets/img/station_images/dzdww.png';
import erzbyq from '/@/assets/img/station_images/erzbyq.png';
import fdj from '/@/assets/img/station_images/fdj.png';
import fh from '/@/assets/img/station_images/fh.png';
import kgdlq from '/@/assets/img/station_images/kgdlq.png';
import mx from '/@/assets/img/station_images/mx.png';
import { _ShapeData as shapeData, _ShapeDataTop as shapeDataTop } from './shape-data';

/**
 * Shape默认配置
 * 定义节点连接点的基本样式和属性
 * @param overrides 可选的覆盖配置
 */
const _ShapeData = (overrides = {}) => {
	const defaults = {
		width: 10, // 连接点宽度
		height: 10, // 连接点高度
		fill: 'blue', // 连接点填充颜色
		portId: 'default', // 连接点ID
		fromLinkable: true, // 允许作为连接起点
		toLinkable: true, // 允许作为连接终点
		alignment: new go.Spot(0.5, 0.5), // 连接点对齐方式（中心）
		fromMaxLinks: 1, // 最大引出连接数
		toMaxLinks: 1, // 最大接收连接数
	};
	return { ...defaults, ...overrides };
};

/**
 * 左节点ShapeData
 * 定义左侧连接点的配置
 */
const _ShapeDataLeft = (overrides = {}) => {
	const defaults = {
		portId: 'left',
		alignment: new go.Spot(0, 0.3), // 左侧30%位置
		fromSpot: go.Spot.Left,
		toSpot: go.Spot.Left,
	};
	return { ...defaults, ...overrides };
};

/**
 * 上节点ShapeData
 * 定义顶部连接点的配置
 */
const _ShapeDataTop = (overrides = {}) => {
	const defaults = {
		portId: 'top',
		alignment: new go.Spot(0.5, 0), // 顶部中间位置
		fromSpot: go.Spot.Top,
		toSpot: go.Spot.Top,
	};
	return { ...defaults, ...overrides };
};

/**
 * 右节点ShapeData
 * 定义右侧连接点的配置
 */
const _ShapeDataRight = (overrides = {}) => {
	const defaults = {
		portId: 'right',
		alignment: new go.Spot(1, 0.3), // 右侧30%位置
		fromSpot: go.Spot.Right,
		toSpot: go.Spot.Right,
	};
	return { ...defaults, ...overrides };
};

// 定义节点数据类型
interface NodeData {
	key: string;
	name: string;
	pos: [number, number];
	rotation?: number;
	customParam?: any;
	width?: number;
}

// 定义回调函数接口
interface NodeCallbacks {
	showNodeInfo?: (data: NodeData) => void;
	editNode?: (data: NodeData) => void;
	deleteNode?: (data: NodeData) => void;
	handleNodeClick?: (data: NodeData) => void;
}

// 扩展 GoJS 的 Part 类型以包含 data 属性
interface ExtendedPart extends Part {
	data: NodeData;
}

// 扩展 GoJS 的 GraphObject 类型以包含 part 属性
interface ExtendedGraphObject extends GraphObject {
	part: ExtendedPart;
}

// 扩展 GoJS 的 Panel 类型以包含 part 属性
interface ExtendedPanel extends Panel {
	part: ExtendedPart;
}

/**
 * 三绕变节点模板
 * 创建一个三绕变变压器的节点模板
 * @param callbacks 回调函数对象，包含各种事件处理函数
 */
export const logoTemplate = (callbacks: NodeCallbacks = {}) => {
	const $ = go.GraphObject.make;

	// 定义右键菜单
	const contextMenu = $(
		go.Adornment,
		'Vertical',
		$(
			go.Panel,
			'Auto',
			{ background: '#f9f9f9', padding: 5 },
			$(go.Shape, 'Rectangle', { fill: '#f9f9f9', stroke: '#ccc' }),
			$(go.Placeholder, { padding: 5 })
		),
		$(
			go.Panel,
			'Auto',
			{
				click: (e: go.InputEvent, obj: go.GraphObject) => {
					const panel = obj.panel as ExtendedPanel;
					callbacks.showNodeInfo?.(panel.part.data);
				},
			},
			$(go.TextBlock, '节点信息', { margin: 5, background: '#f9f9f9' })
		),
		$(
			go.Panel,
			'Auto',
			{
				click: (e: go.InputEvent, obj: go.GraphObject) => {
					const panel = obj.panel as ExtendedPanel;
					callbacks.editNode?.(panel.part.data);
				},
			},
			$(go.TextBlock, '编辑', { margin: 5 })
		),
		$(
			go.Panel,
			'Auto',
			{
				click: (e: go.InputEvent, obj: go.GraphObject) => {
					const panel = obj.panel as ExtendedPanel;
					callbacks.deleteNode?.(panel.part.data);
				},
			},
			$(go.TextBlock, '删除', { margin: 5 })
		)
	);

	// 创建节点
	return (
		new go.Node('Spot', {
			// 使用 "Spot" 布局
			selectable: true, // 允许选中
			click: (e, node) => callbacks.handleNodeClick?.(node.data as NodeData), // 点击事件
			cursor: 'pointer', // 鼠标指针样式
			contextMenu: contextMenu, // 右键菜单
			locationSpot: go.Spot.Center, // 位置参考点
		})
			// 绑定位置属性
			.bind(
				new go.Binding('location', 'pos', (data) => {
					return new go.Point(data[0], data[1]);
				}).makeTwoWay((modelProp) => {
					return [modelProp.x, modelProp.y];
				})
			)
			// 绑定旋转角度
			.bind(new go.Binding('angle', 'rotation').makeTwoWay())
			// 添加垂直布局的面板
			.add(
				$(
					go.Panel,
					'Vertical',
					{
						defaultAlignment: go.Spot.Center,
						margin: 5,
					},
					// 添加图片
					$(go.Picture, {
						source: logo,
						width: 80,
						height: 80,
						alignment: go.Spot.Center,
					}),
					// 添加文本标签
					$(go.TextBlock, {
						font: 'bold 14px sans-serif',
						isMultiline: false,
						editable: true,
						textAlign: 'center',
						margin: 5,
						maxSize: new go.Size(100, NaN),
					}).bind(new go.Binding('text', 'name'))
				)
			)
			// 添加连接点
			.add(new go.Shape('Circle', _ShapeData(_ShapeDataLeft()))) // 左侧连接点
			.add(new go.Shape('Circle', _ShapeData(_ShapeDataTop()))) // 顶部连接点
			.add(new go.Shape('Circle', _ShapeData(_ShapeDataRight())))
	); // 右侧连接点
};

/**
 * 发电机节点模板
 * 创建一个发电机设备的节点模板
 * @param callbacks 回调函数对象，包含各种事件处理函数
 */
export const fdjTemplate = (callbacks: NodeCallbacks = {}) => {
	const $ = go.GraphObject.make;

	// 定义右键菜单
	const contextMenu = $(
		go.Adornment,
		'Vertical',
		$(
			go.Panel,
			'Auto',
			{ background: '#f9f9f9', padding: 5 },
			$(go.Shape, 'Rectangle', { fill: '#f9f9f9', stroke: '#ccc' }),
			$(go.Placeholder, { padding: 5 })
		),
		$(
			go.Panel,
			'Auto',
			{
				click: (e: go.InputEvent, obj: go.GraphObject) => {
					const panel = obj.panel as ExtendedPanel;
					callbacks.showNodeInfo?.(panel.part.data);
				},
			},
			$(go.TextBlock, '节点信息', { margin: 5, background: '#f9f9f9' })
		),
		$(
			go.Panel,
			'Auto',
			{
				click: (e: go.InputEvent, obj: go.GraphObject) => {
					const panel = obj.panel as ExtendedPanel;
					callbacks.editNode?.(panel.part.data);
				},
			},
			$(go.TextBlock, '编辑', { margin: 5 })
		),
		$(
			go.Panel,
			'Auto',
			{
				click: (e: go.InputEvent, obj: go.GraphObject) => {
					const panel = obj.panel as ExtendedPanel;
					callbacks.deleteNode?.(panel.part.data);
				},
			},
			$(go.TextBlock, '删除', { margin: 5 })
		)
	);

	return new go.Node('Spot', {
		selectable: true,
		click: (e: go.InputEvent, node: go.GraphObject) => {
			const part = node.part as ExtendedPart;
			callbacks.handleNodeClick?.(part.data);
		},
		cursor: 'pointer',
		contextMenu: contextMenu,
		locationSpot: go.Spot.Center,
	})
		.add(
			$(
				go.Panel,
				'Vertical',
				{
					defaultAlignment: go.Spot.Center,
					margin: 5,
				},
				$(go.Picture, {
					source: fdj,
					width: 50,
					height: 50,
					alignment: go.Spot.Center,
				}),
				$(go.TextBlock, {
					font: 'bold 14px sans-serif',
					isMultiline: false,
					editable: true,
					textAlign: 'center',
					margin: 5,
					maxSize: new go.Size(100, NaN),
				}).bind(new go.Binding('text', 'name'))
			)
		)
		.bind(
			new go.Binding('location', 'pos', (data) => {
				return new go.Point(data[0], data[1]);
			}).makeTwoWay((modelProp) => {
				return [modelProp.x, modelProp.y];
			})
		)
		.bind(new go.Binding('angle', 'rotation').makeTwoWay())
		.add(new go.Shape('Circle', shapeData(shapeDataTop({ alignment: new go.Spot(0.5, 0) }))));
};

/**
 * 交流线路节点模板
 * 创建一个交流线路设备的节点模板
 * @param callbacks 回调函数对象，包含各种事件处理函数
 */
export const jlxlTemplate = (callbacks: NodeCallbacks = {}) => {
	const $ = go.GraphObject.make;

	// 定义右键菜单
	const contextMenu = $(
		go.Adornment,
		'Vertical',
		$(
			go.Panel,
			'Auto',
			{ background: '#f9f9f9', padding: 5 },
			$(go.Shape, 'Rectangle', { fill: '#f9f9f9', stroke: '#ccc' }),
			$(go.Placeholder, { padding: 5 })
		),
		$(
			go.Panel,
			'Auto',
			{ click: (e, obj) => callbacks.showNodeInfo?.(obj.part?.data as NodeData) },
			$(go.TextBlock, '节点信息', { margin: 5, background: '#f9f9f9' })
		),
		$(go.Panel, 'Auto', { click: (e, obj) => callbacks.editNode?.(obj.part?.data as NodeData) }, $(go.TextBlock, '编辑', { margin: 5 })),
		$(go.Panel, 'Auto', { click: (e, obj) => callbacks.deleteNode?.(obj.part?.data as NodeData) }, $(go.TextBlock, '删除', { margin: 5 }))
	);

	return new go.Node('Auto', {
		selectable: true,
		click: (e, node) => callbacks.handleNodeClick?.(node.data as NodeData),
		cursor: 'pointer',
		contextMenu: contextMenu,
	})
		.add(new go.Picture({ source: jlxl, width: 50, height: 50 }))
		.add(new go.Shape('Circle', _ShapeData(_ShapeDataLeft({ alignment: new go.Spot(0, 0.7) }))))
		.add(new go.Shape('Circle', _ShapeData(_ShapeDataRight({ alignment: new go.Spot(1, 0.7) }))));
};

/**
 * 电阻器节点模板
 * 创建一个电阻器设备的节点模板
 * @param callbacks 回调函数对象，包含各种事件处理函数
 */
export const bldkqTemplate = (callbacks: NodeCallbacks = {}) => {
	const $ = go.GraphObject.make;

	// 定义右键菜单
	const contextMenu = $(
		go.Adornment,
		'Vertical',
		$(
			go.Panel,
			'Auto',
			{ background: '#f9f9f9', padding: 5 },
			$(go.Shape, 'Rectangle', { fill: '#f9f9f9', stroke: '#ccc' }),
			$(go.Placeholder, { padding: 5 })
		),
		$(
			go.Panel,
			'Auto',
			{ click: (e, obj) => callbacks.showNodeInfo?.(obj.part?.data as NodeData) },
			$(go.TextBlock, '节点信息', { margin: 5, background: '#f9f9f9' })
		),
		$(go.Panel, 'Auto', { click: (e, obj) => callbacks.editNode?.(obj.part?.data as NodeData) }, $(go.TextBlock, '编辑', { margin: 5 })),
		$(go.Panel, 'Auto', { click: (e, obj) => callbacks.deleteNode?.(obj.part?.data as NodeData) }, $(go.TextBlock, '删除', { margin: 5 }))
	);

	return new go.Node('Auto', {
		selectable: true,
		click: (e, node) => callbacks.handleNodeClick?.(node.data as NodeData),
		cursor: 'pointer',
		contextMenu: contextMenu,
	})
		.add(new go.Picture({ source: bldkq, width: 50, height: 50 }))
		.add(
			new go.Shape('Circle', {
				width: 10,
				height: 10,
				fill: 'green',
				portId: 'top',
				fromSpot: go.Spot.Top,
				toSpot: go.Spot.Top,
				fromLinkable: true,
				toLinkable: true,
				alignment: new go.Spot(0.5, 0),
				fromMaxLinks: 1,
				toMaxLinks: 1,
			})
		);
};

// 电容器
export const bldrqTemplate = () => {
	const $ = go.GraphObject.make;

	// 定义右键菜单
	const contextMenu = $(
		go.Adornment,
		'Vertical',
		$(
			go.Panel,
			'Auto',
			{ background: '#f9f9f9', padding: 5 },
			$(go.Shape, 'Rectangle', { fill: '#f9f9f9', stroke: '#ccc' }),
			$(go.Placeholder, { padding: 5 })
		),
		$(go.Panel, 'Auto', { click: (e, obj) => showNodeInfo(obj.part?.data) }, $(go.TextBlock, '节点信息', { margin: 5, background: '#f9f9f9' })),
		$(go.Panel, 'Auto', { click: (e, obj) => editNode(obj.part?.data) }, $(go.TextBlock, '编辑', { margin: 5 })),
		$(go.Panel, 'Auto', { click: (e, obj) => deleteNode(obj.part?.data) }, $(go.TextBlock, '删除', { margin: 5 }))
	);

	return new go.Node('Auto', {
		selectable: true,
		click: (e, node) => handleNodeClick(node.data),
		cursor: 'pointer',
		contextMenu: contextMenu, // 绑定右键菜单
	})
		.add(new go.Picture({ source: bldrq, width: 50, height: 50 }))
		.add(
			new go.Shape('Circle', {
				width: 10,
				height: 10,
				fill: 'green',
				portId: 'top',
				fromSpot: go.Spot.Top,
				toSpot: go.Spot.Top,
				fromLinkable: true,
				toLinkable: true,
				alignment: new go.Spot(0.5, 0),
				fromMaxLinks: 1, // 限制引出连接数
				toMaxLinks: 1, // 限制接收连接数
			})
		);
};

// 开关
export const dzTemplate = () => {
	const $ = go.GraphObject.make;

	// 定义右键菜单
	const contextMenu = $(
		go.Adornment,
		'Vertical',
		$(
			go.Panel,
			'Auto',
			{ background: '#f9f9f9', padding: 5 },
			$(go.Shape, 'Rectangle', { fill: '#f9f9f9', stroke: '#ccc' }),
			$(go.Placeholder, { padding: 5 })
		),
		$(go.Panel, 'Auto', { click: (e, obj) => showNodeInfo(obj.part?.data) }, $(go.TextBlock, '节点信息', { margin: 5, background: '#f9f9f9' })),
		$(go.Panel, 'Auto', { click: (e, obj) => editNode(obj.part?.data) }, $(go.TextBlock, '编辑', { margin: 5 })),
		$(go.Panel, 'Auto', { click: (e, obj) => deleteNode(obj.part?.data) }, $(go.TextBlock, '删除', { margin: 5 }))
	);

	return new go.Node('Auto', {
		selectable: true,
		click: (e, node) => handleNodeClick(node.data),
		cursor: 'pointer',
		contextMenu: contextMenu, // 绑定右键菜单
	})
		.add(new go.Picture({ source: dz, width: 50, height: 50 }))
		.add(
			new go.Shape('Circle', {
				width: 10,
				height: 10,
				fill: 'green',
				portId: 'left',
				fromSpot: go.Spot.Left,
				toSpot: go.Spot.Left,
				fromLinkable: true,
				toLinkable: true,
				alignment: new go.Spot(0, 0.5),
				fromMaxLinks: 1, // 限制引出连接数
				toMaxLinks: 1, // 限制接收连接数
			})
		)
		.add(
			new go.Shape('Circle', {
				width: 10,
				height: 10,
				fill: 'green',
				portId: 'right',
				fromSpot: go.Spot.Right,
				toSpot: go.Spot.Right,
				fromLinkable: true,
				toLinkable: true,
				alignment: new go.Spot(1, 0.5),
				fromMaxLinks: 1, // 限制引出连接数
				toMaxLinks: 1, // 限制接收连接数
			})
		);
};

// 等值外电网
export const dzdwwTemplate = () => {
	const $ = go.GraphObject.make;

	// 定义右键菜单
	const contextMenu = $(
		go.Adornment,
		'Vertical',
		$(
			go.Panel,
			'Auto',
			{ background: '#f9f9f9', padding: 5 },
			$(go.Shape, 'Rectangle', { fill: '#f9f9f9', stroke: '#ccc' }),
			$(go.Placeholder, { padding: 5 })
		),
		$(go.Panel, 'Auto', { click: (e, obj) => showNodeInfo(obj.part?.data) }, $(go.TextBlock, '节点信息', { margin: 5, background: '#f9f9f9' })),
		$(go.Panel, 'Auto', { click: (e, obj) => editNode(obj.part?.data) }, $(go.TextBlock, '编辑', { margin: 5 })),
		$(go.Panel, 'Auto', { click: (e, obj) => deleteNode(obj.part?.data) }, $(go.TextBlock, '删除', { margin: 5 }))
	);

	return new go.Node('Auto', {
		selectable: true,
		click: (e, node) => handleNodeClick(node.data),
		cursor: 'pointer',
		contextMenu: contextMenu, // 绑定右键菜单
	})
		.add(new go.Picture({ source: dzdww, width: 50, height: 50 }))
		.add(
			new go.Shape('Circle', {
				width: 10,
				height: 10,
				fill: 'green',
				portId: 'top',
				fromSpot: go.Spot.Top,
				toSpot: go.Spot.Top,
				fromLinkable: true,
				toLinkable: true,
				alignment: new go.Spot(0.5, 0),
				fromMaxLinks: 1, // 限制引出连接数
				toMaxLinks: 1, // 限制接收连接数
			})
		);
};

// 双绕变
export const erzbyqTemplate = (handleNodeClick, showNodeInfo, editNode, deleteNode) => {
	const $ = go.GraphObject.make;

	// 定义右键菜单
	const contextMenu = $(
		go.Adornment,
		'Vertical',
		$(
			go.Panel,
			'Auto',
			{ background: '#f9f9f9', padding: 5 },
			$(go.Shape, 'Rectangle', { fill: '#f9f9f9', stroke: '#ccc' }),
			$(go.Placeholder, { padding: 5 })
		),
		$(go.Panel, 'Auto', { click: (e, obj) => showNodeInfo(obj.part?.data) }, $(go.TextBlock, '节点信息', { margin: 5, background: '#f9f9f9' })),
		$(go.Panel, 'Auto', { click: (e, obj) => editNode(obj.part?.data) }, $(go.TextBlock, '编辑', { margin: 5 })),
		$(go.Panel, 'Auto', { click: (e, obj) => deleteNode(obj.part?.data) }, $(go.TextBlock, '删除', { margin: 5 }))
	);

	return new go.Node('Auto', {
		selectable: true,
		click: (e, node) => {
			handleNodeClick(node.data);
		},
		cursor: 'pointer',
		contextMenu: contextMenu, // 绑定右键菜单
		rotatable: true, //允许旋转
	})
		.bind(
			new go.Binding('location', 'pos', (data) => {
				return new go.Point(data[0], data[1]);
			}).makeTwoWay((modelProp) => {
				console.log(modelProp);

				return [modelProp.x, modelProp.y];
			})
		)
		.bind(new go.Binding('angle', 'rotation').makeTwoWay())
		.add(
			// 标题
			$(go.TextBlock, {
				margin: 5, // 设置外边距
				font: 'bold 14px sans-serif', // 设置字体样式
				stroke: '#333', // 设置文字颜色
				textAlign: 'center', // 文字居中对齐
				width: 100, // 设置宽度
				overflow: go.TextBlock.OverflowEllipsis, // 超出部分显示省略号
			}).bind(new go.Binding('text', 'name').makeTwoWay())
		)
		.add(new go.Picture({ source: erzbyq, width: 50, height: 50 }))
		.add(
			// 上端口
			new go.Shape('Circle', {
				width: 10,
				height: 10,
				fill: 'green',
				portId: 'left',
				fromSpot: go.Spot.Top,
				toSpot: go.Spot.Top,
				fromLinkable: true,
				toLinkable: true,
				alignment: new go.Spot(0.5, 0), // 端口位置（顶部中间）
				fromMaxLinks: 1, // 限制引出连接数
				toMaxLinks: 1, // 限制接收连接数
			})
		)
		.add(
			// 下端口
			new go.Shape('Circle', {
				width: 10,
				height: 10,
				fill: 'green',
				portId: 'bottom',
				fromSpot: go.Spot.Bottom,
				toSpot: go.Spot.Bottom,
				fromLinkable: true,
				toLinkable: true,
				alignment: new go.Spot(0.5, 1), // 端口位置（底部中间）
				fromMaxLinks: 1, // 限制引出连接数
				toMaxLinks: 1, // 限制接收连接数
			})
		);
};

// 负荷
export const fhTemplate = () => {
	const $ = go.GraphObject.make;

	// 定义右键菜单
	const contextMenu = $(
		go.Adornment,
		'Vertical',
		$(
			go.Panel,
			'Auto',
			{ background: '#f9f9f9', padding: 5 },
			$(go.Shape, 'Rectangle', { fill: '#f9f9f9', stroke: '#ccc' }),
			$(go.Placeholder, { padding: 5 })
		),
		$(go.Panel, 'Auto', { click: (e, obj) => showNodeInfo(obj.part?.data) }, $(go.TextBlock, '节点信息', { margin: 5, background: '#f9f9f9' })),
		$(go.Panel, 'Auto', { click: (e, obj) => editNode(obj.part?.data) }, $(go.TextBlock, '编辑', { margin: 5 })),
		$(go.Panel, 'Auto', { click: (e, obj) => deleteNode(obj.part?.data) }, $(go.TextBlock, '删除', { margin: 5 }))
	);

	return new go.Node('Auto', {
		selectable: true,
		click: (e, node) => handleNodeClick(node.data),
		cursor: 'pointer',
		contextMenu: contextMenu, // 绑定右键菜单
	})
		.add(new go.Picture({ source: fh, width: 50, height: 50 }))
		.add(
			new go.Shape('Circle', {
				width: 10,
				height: 10,
				fill: 'green',
				portId: 'top',
				fromSpot: go.Spot.Top,
				toSpot: go.Spot.Top,
				fromLinkable: true,
				toLinkable: true,
				alignment: new go.Spot(0.5, 0),
				fromMaxLinks: 1, // 限制引出连接数
				toMaxLinks: 1, // 限制接收连接数
			})
		);
};

// 开关_断路器
export const kgdlqTemplate = () => {
	const $ = go.GraphObject.make;

	// 定义右键菜单
	const contextMenu = $(
		go.Adornment,
		'Vertical',
		$(
			go.Panel,
			'Auto',
			{ background: '#f9f9f9', padding: 5 },
			$(go.Shape, 'Rectangle', { fill: '#f9f9f9', stroke: '#ccc' }),
			$(go.Placeholder, { padding: 5 })
		),
		$(go.Panel, 'Auto', { click: (e, obj) => showNodeInfo(obj.part?.data) }, $(go.TextBlock, '节点信息', { margin: 5, background: '#f9f9f9' })),
		$(go.Panel, 'Auto', { click: (e, obj) => editNode(obj.part?.data) }, $(go.TextBlock, '编辑', { margin: 5 })),
		$(go.Panel, 'Auto', { click: (e, obj) => deleteNode(obj.part?.data) }, $(go.TextBlock, '删除', { margin: 5 }))
	);

	return new go.Node('Auto', {
		selectable: true,
		click: (e, node) => handleNodeClick(node.data),
		cursor: 'pointer',
		contextMenu: contextMenu, // 绑定右键菜单
	})
		.add(new go.Picture({ source: kgdlq, width: 50, height: 50 }))
		.add(
			new go.Shape('Circle', {
				width: 10,
				height: 10,
				fill: 'green',
				portId: 'left',
				fromSpot: go.Spot.Left,
				toSpot: go.Spot.Left,
				fromLinkable: true,
				toLinkable: true,
				alignment: new go.Spot(0, 0.5),
				fromMaxLinks: 1, // 限制引出连接数
				toMaxLinks: 1, // 限制接收连接数
			})
		)
		.add(
			new go.Shape('Circle', {
				width: 10,
				height: 10,
				fill: 'green',
				portId: 'right',
				fromSpot: go.Spot.Right,
				toSpot: go.Spot.Right,
				fromLinkable: true,
				toLinkable: true,
				alignment: new go.Spot(1, 0.5),
				fromMaxLinks: 1, // 限制引出连接数
				toMaxLinks: 1, // 限制接收连接数
			})
		);
};

//母线
export const mxTemplate = () => {
	const $ = go.GraphObject.make;

	// 定义右键菜单
	const contextMenu = $(
		go.Adornment,
		'Vertical',
		$(
			go.Panel,
			'Auto',
			{ background: '#f9f9f9', padding: 5 },
			$(go.Shape, 'Rectangle', { fill: '#f9f9f9', stroke: '#ccc' }),
			$(go.Placeholder, { padding: 5 })
		),
		$(go.Panel, 'Auto', { click: (e, obj) => showNodeInfo(obj.part?.data) }, $(go.TextBlock, '节点信息', { margin: 5, background: '#f9f9f9' })),
		$(go.Panel, 'Auto', { click: (e, obj) => editNode(obj.part?.data) }, $(go.TextBlock, '编辑', { margin: 5 })),
		$(go.Panel, 'Auto', { click: (e, obj) => deleteNode(obj.part?.data) }, $(go.TextBlock, '删除', { margin: 5 }))
	);

	return new go.Node('Vertical', {
		// 使用 Vertical 布局
		cursor: 'pointer',
		contextMenu: contextMenu, // 绑定右键菜单
		selectable: true, // 允许选中
		movable: true, // 允许拖动
		locationSpot: go.Spot.Center, // 设置拖动时的位置参考点
		resizable: true,
		rotatable: true, //允许旋转
		width: 80,
		height: 40, // 初始高度
		resizeAdornmentTemplate: $(
			go.Adornment,
			'Spot',
			$(go.Placeholder),
			// 只显示水平调整手柄
			$(go.Shape, {
				alignment: go.Spot.Right, // 调整手柄位于右侧
				cursor: 'col-resize', // 水平调整光标
				desiredSize: new go.Size(6, 10), // 手柄大小
				fill: 'lightblue', // 手柄颜色
				stroke: 'dodgerblue',
			})
		),
	})
		.bind(
			new go.Binding('location', 'pos', (data) => {
				return new go.Point(data[0], data[1]);
			}).makeTwoWay((modelProp) => {
				return [modelProp.x, modelProp.y];
			})
		)
		.bind(new go.Binding('angle', 'rotation').makeTwoWay())
		.bind(new go.Binding('width', 'width').makeTwoWay())
		.add(
			// 添加默认端口
			$(go.Shape, 'Rectangle', {
				portId: '', // 空字符串表示默认端口
				// fromSpot: go.Spot.LeftRightSides, // 允许从左右两侧连接
				// toSpot: go.Spot.LeftRightSides,   // 允许连接到左右两侧
				fromLinkable: true, // 允许引出链接
				toLinkable: true, // 允许接收链接
				fill: 'transparent', // 背景透明
				stroke: null, // 无边框
				// width: 10,                        // 端口宽度与母线一致
				height: 5, // 端口高度与母线一致
				alignment: new go.Spot(0.5, 0), // 水平居中，垂直靠上
			})
		)
		.add(
			// 图片部分：设置初始宽度和高度
			$(go.Picture, {
				source: mx, // 图片源
				height: 10, // 初始高度
				imageStretch: go.ImageStretch.Fill, // 使用 ImageStretch 枚举值
			}).bind(new go.Binding('width', 'size', (size) => (size ? size.width : 80)).makeTwoWay()) // 动态绑定宽度
		)
		.add(
			// 标题部分
			$(go.TextBlock, {
				text: '母线标题', // 标题文本
				margin: 5, // 标题与图片之间的间距
				font: 'bold 12px sans-serif', // 字体样式
				textAlign: 'center', // 文本居中对齐
				maxSize: new go.Size(80, NaN), // 限制标题的最大宽度为 80
			})
		);
};
