<template>
	<div class="container">
		<div ref="diagramRef" class="gojs-container"></div>

		<div class="grid-container">
			<el-row :gutter="20">
				<el-col :span="12" v-for="(item, index) in gridList" :key="index">
					<div @click="toolbarMenuClick(item)" class="grid-item">
						<img :src="item.img" style="width: 50px; height: 50px" />
						<p>{{ item.name }}</p>
					</div>
				</el-col>
			</el-row>
		</div>
		<component :is="currentDialogComponent" v-model="toolDialog" :node-data="componentNode" :title="dialogTitle" @submit="componentSubmit" />

		<button @click="exportDiagramData">获取链接关系和节点</button>

		<el-dialog v-model="dialogVisible" title="节点详情" width="30%">
			<div v-if="currentNode">
				<img :src="currentNode.source" style="width: 100px; height: 100px" />
				<p>名称：{{ currentNode.name }}</p>
			</div>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup>
import * as go from 'gojs';
import { onMounted, ref } from 'vue';
import LogoDialog from './components/LogoDialog/index.vue';
import JlxlDialog from './components/JlxlDialog/index.vue';

import * as api from './api';

import * as goTemplate from './template'; // 导入模板

import logo from '/@/assets/img/station_images/srb.png';
import jlxl from '/@/assets/img/station_images/jlxl.png';
import bldkq from '/@/assets/img/station_images/bldkq.png';
import bldrq from '/@/assets/img/station_images/bldrq.png';
import dz from '/@/assets/img/station_images/dz.png';
import dzdww from '/@/assets/img/station_images/dzdww.png';
import erzbyq from '/@/assets/img/station_images/erzbyq.png';
import fdj from '/@/assets/img/station_images/fdj.png';
import fh from '/@/assets/img/station_images/fh.png';
import kgdlq from '/@/assets/img/station_images/kgdlq.png';
import mx from '/@/assets/img/station_images/mx.png';
const gridList = ref([
	{ img: logo, name: '三绕变', type: 1, component: LogoDialog },
	{ img: jlxl, name: '交流线路', type: 2, component: JlxlDialog },
	{ img: bldkq, name: '电阻器', type: 3, component: LogoDialog },
	{ img: bldrq, name: '电容器', type: 4, component: LogoDialog },
	{ img: dz, name: '开关', type: 5, component: LogoDialog },
	{ img: dzdww, name: '等值外电网', type: 6, component: LogoDialog },
	{ img: erzbyq, name: '双绕变', type: 7, component: LogoDialog },
	{ img: fdj, name: '发电机', type: 8, component: LogoDialog },
	{ img: fh, name: '负荷', type: 9, component: LogoDialog },
	{ img: kgdlq, name: '开关_断路器', type: 10, component: LogoDialog },
	{ img: mx, name: '母线', type: 11, component: LogoDialog },
]);

// const nodeDataArrayRef = ref([
// 	{ key: '1', name: '标题', customParam: {}, category: 'sgen', pos: [200, 500]  },
// 	{ key: '1', name: 'BUS2', customParam: {}, category: 'bus', pos: [200, 500] },
// 	{ key: '2', name: '', customParam: {}, category: 'erzbyq_template' },
// 	{ key: '3', name: '', customParam: {}, category: 'bldkq_template' },
// 	{ key: '4', name: '', customParam: {}, category: 'jlxl_template' },
// 	{ key: '5', name: '', customParam: {}, category: 'bldrq_template' },
// 	{ key: '6', name: '', customParam: {}, category: 'dz_template' },
// 	{ key: '7', name: '', customParam: {}, category: 'fdj_template' },
// 	{ key: '8', name: '', customParam: {}, category: 'fh_template' },
// 	{ key: '9', name: '', customParam: {}, category: 'dzdww_template' },
// 	{ key: '10', name: '', customParam: {}, category: 'kgdlq_template' },
// 	{ key: '11', name: '', customParam: {}, category: 'bus' },
// ]);

const nodeDataArrayRef = ref([
	{
		key: '1',
		name: '标题',
		category: 'sgen',
		pos: [72.80001831054688, 541.5999145507812],
		customParam: {},
	},
	{
		key: '12',
		name: 'BUS2',
		category: 'bus',
		pos: [72.79998779296875, 367.2016456347656],
		customParam: {},
	},
	{
		key: '2',
		name: '绕变',
		category: 'trafo',
		pos: [198.29998779296875, 405.2077362060547],
		rotation: 270,
		customParam: {},
	},
	{
		key: '3',
		name: 'BUS7',
		category: 'bus',
		pos: [487.7137491183811, 340.7999267578125],
		customParam: {},
	},
	{
		key: '4',
		name: 'BUS5',
		category: 'bus',
		pos: [353.59999084472656, 574.4000091552734],
		customParam: {},
	},
	{
		key: '5',
		name: 'BUS8',
		category: 'bus',
		pos: [325.6000061035156, 350.40000915527344],
		rotation: 270,
		customParam: {},
	},
	{
		key: '6',
		name: 'BUS4',
		category: 'bus',
		pos: [489.5773995735857, 725.2365417667949],
		rotation: 0,
		customParam: {},
		width: 389,
	},
]);

// const linkDataArrayRef = ref([
// 	{
// 		from: '123456789003',
// 		to: '6',
// 		fromPortId: 'top',
// 		toPortId: '',
// 	},
// ]);
const linkDataArrayRef = ref([
	{
		from: '123456789003',
		to: '6',
		fromPortId: 'top',
		toPortId: '',
	},
	{
		from: '1',
		to: '12',
		fromPortId: 'top',
		toPortId: '',
	},
	{
		from: '2',
		to: '12',
		fromPortId: 'left',
		toPortId: '',
	},
	{
		from: '2',
		to: '11',
		fromPortId: 'bottom',
		toPortId: '',
	},
	{
		from: '5',
		to: '2',
		fromPortId: '',
		toPortId: 'bottom',
	},
	{
		from: '5',
		to: '4',
		fromPortId: '',
		toPortId: '',
	},
	{
		from: '5',
		to: '3',
		fromPortId: '',
		toPortId: '',
	},
	{
		from: '4',
		to: '6',
		fromPortId: '',
		toPortId: '',
	},
]);

const diagramRef = ref<HTMLDivElement | null>(null);
const currentDialogComponent = ref(LogoDialog);
const dialogTitle = ref('添加节点');
let myDiagram: go.Diagram | null = null;
const dialogVisible = ref(false);
const toolDialog = ref(false);
const currentNode = ref({ source: '', name: '' });
const componentNode = ref({ select: [{}] });

const get_query_node_info = async () => {
	const res = await api.get_query_node_info();
	if (res.code == 2000) {
		// nodeDataArrayRef.value = res.data;
		// nodeDataArrayRef.value.map((item) => {
		// 	item['category'] = item.type;
		// });
	}
};
const get_query_connect_info = async () => {
	const res = await api.get_query_connect_info();
	if (res.code == 2000) {
		// linkDataArrayRef.value = res.data;
		// linkDataArrayRef.value.map((item) => {
		// 	item.to = `${item.to}`;
		// 	item['fromPortId'] = `${item.fromportid}`;
		// 	item['toPortId'] = `${item.toportid}`;
		// });
	}
};

const exportDiagramData = () => {
	if (!myDiagram || !myDiagram.model) {
		console.error('图表未初始化');
		return;
	}
	const nodeDataArray = myDiagram.model.nodeDataArray.map((node) => {
		console.log(node, '节点绑定数据');

		return {
			key: node.key, // 节点唯一标识
			name: node.name, // 节点名称
			source: node.source, // 节点图片资源
			category: node.category, // 节点类型
			pos: node.pos, // 节点位置
			rotation: node.rotation, // 节点旋转角度
			customParam: node.customParam, // 自定义参数（如果有）
			width: node.width,
		};
	});
	const linkDataArray = myDiagram.model.linkDataArray.map((link) => ({
		from: link.from, // 链接起点
		to: link.to, // 链接终点
		fromPortId: link.fromPortId, // 起点端口 ID
		toPortId: link.toPortId, // 终点端口 ID
	}));
	const exportedData = {
		nodeDataArray,
		linkDataArray,
	};
	console.log('导出的节点和链接数据:', exportedData);
};

const handleNodeClick = (nodeData: any) => {
	currentNode.value = nodeData;
	dialogVisible.value = true;
	console.log(nodeData.customParam, '自定义参数');
};

const toolbarMenuClick = (item: any) => {
	console.log(item, '4123');
	toolDialog.value = true;
	currentDialogComponent.value = item.component;
	// addNode('标题', item.img);
};

const init = () => {
	if (diagramRef.value) {
		const $ = go.GraphObject.make;

		myDiagram = $(go.Diagram, diagramRef.value, {
			initialContentAlignment: go.Spot.Center,
		});

		const logoTemplate: go.Node = goTemplate.logoTemplate();
		const jlxlTemplate: go.Node = goTemplate.jlxlTemplate();
		const bldkqTemplate: go.Node = goTemplate.bldkqTemplate();
		const bldrqTemplate: go.Node = goTemplate.bldrqTemplate();
		const dzTemplate: go.Node = goTemplate.dzTemplate();
		const dzdwwTemplate: go.Node = goTemplate.dzdwwTemplate();
		const erzbyqTemplate: go.Node = goTemplate.erzbyqTemplate(handleNodeClick, showNodeInfo, editNode, deleteNode);
		const fdjTemplate: go.Node = goTemplate.fdjTemplate();
		const fhTemplate: go.Node = goTemplate.fhTemplate();
		const kgdlqTemplate: go.Node = goTemplate.kgdlqTemplate();
		const mxTemplate: go.Node = goTemplate.mxTemplate();
		// 注册模板到 nodeTemplateMap
		myDiagram.nodeTemplateMap.add('trafo3w', logoTemplate); //三绕变
		myDiagram.nodeTemplateMap.add('line', jlxlTemplate); //交流线路
		myDiagram.nodeTemplateMap.add('bldkq_template', bldkqTemplate);
		myDiagram.nodeTemplateMap.add('bldrq_template', bldrqTemplate);
		myDiagram.nodeTemplateMap.add('bldrq_template', bldrqTemplate);
		myDiagram.nodeTemplateMap.add('dz_template', dzTemplate);
		myDiagram.nodeTemplateMap.add('dzdww_template', dzdwwTemplate);
		myDiagram.nodeTemplateMap.add('trafo', erzbyqTemplate); //双绕变
		myDiagram.nodeTemplateMap.add('sgen', fdjTemplate); //发电机
		myDiagram.nodeTemplateMap.add('fh_template', fhTemplate);
		myDiagram.nodeTemplateMap.add('kgdlq_template', kgdlqTemplate);
		myDiagram.nodeTemplateMap.add('bus', mxTemplate); //母线

		// 设置默认模板（可选）
		myDiagram.nodeTemplate = logoTemplate;
		myDiagram.nodeTemplate.rotatable = true; // 允许旋转节点

		myDiagram.linkTemplate = new go.Link({
			routing: go.Routing.Orthogonal,
			corner: 5,
		}).add(
			new go.Shape({
				strokeWidth: 3,
				stroke: '#555',
				toArrow: 'Standard',
			})
		);

		console.log(linkDataArrayRef.value, '构建');
		console.log(nodeDataArrayRef.value, '节点');

		myDiagram.model = new go.GraphLinksModel({
			nodeDataArray: nodeDataArrayRef.value,
			linkDataArray: linkDataArrayRef.value,
		});

		(myDiagram.model as go.GraphLinksModel).linkFromPortIdProperty = 'fromPortId';
		(myDiagram.model as go.GraphLinksModel).linkToPortIdProperty = 'toPortId';

		// 初始化时将现有节点数据赋值给 componentNode.select
		if (myDiagram && myDiagram.model) {
			componentNode.value.select = myDiagram.model.nodeDataArray.map((node) => ({
				key: node.key,
				name: node.name,
				source: node.source,
			}));
			console.log('初始化时的节点数据:', componentNode.value.select);
		}
	}
};

// 显示节点信息
const showNodeInfo = (nodeData: any) => {
	if (!nodeData) return;
	console.log('节点信息:', nodeData);
	alert(`节点信息:\n名称: ${nodeData.name}\nKey: ${nodeData.key}`);
};

// 编辑节点
const editNode = (nodeData: any) => {
	if (!nodeData) return;
	console.log('编辑节点:', nodeData);
	dialogTitle.value = '编辑节点';
	toolDialog.value = true;
	currentDialogComponent.value = LogoDialog; // 假设使用 LogoDialog 编辑
	componentNode.value = nodeData;
};

// 删除节点
const deleteNode = (nodeData: any) => {
	if (!nodeData || !myDiagram) return;
	myDiagram.model.removeNodeData(nodeData);
};

const componentSubmit = (res: any) => {
	addNode('123', '', logo);
	addLink('123', res.nodeOne);
	addLink('123', res.nodeThree);
	addLink('123', res.nodeTwo);
};

const addNode = (key: string, name: string, source: string) => {
	if (!myDiagram) {
		console.error('myDiagramRef 未初始化');
		return;
	}
	myDiagram.model.addNodeData({ key, name: '', source, customParam: {} });
};

const addLink = (fromKey: string, toKey: string) => {
	if (!myDiagram) {
		console.error('myDiagramRef 未初始化');
		return;
	}
	myDiagram.model.addLinkData({ from: fromKey, to: toKey });
};

onMounted(async () => {
	await get_query_node_info();
	await get_query_connect_info();
	init();
});
</script>

<style scoped>
.container {
	display: flex;
}

.gojs-container {
	width: 80%;
	height: 600px;
	border: 1px solid #ddd;
}

.grid-container {
	width: 20%;
	padding-left: 10px;
}

.grid-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
	margin-top: 10px;
}

.grid-item p {
	margin: 5px 0 0;
}
</style>
