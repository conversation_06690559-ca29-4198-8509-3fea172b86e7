import * as go from 'gojs';

/**
 * 基础形状数据
 * @param options 形状选项
 */
export const _ShapeData = (options: any = {}) => {
	return {
		...{
			figure: 'Circle',
			fill: '#fff',
			stroke: '#000',
			strokeWidth: 1,
			width: 10,
			height: 10,
		},
		...options,
	};
};

/**
 * 顶部形状数据
 * @param options 形状选项
 */
export const _ShapeDataTop = (options: any = {}) => {
	return {
		...{
			alignment: new go.Spot(0.5, 0),
		},
		...options,
	};
};
