<template>
	<el-dialog v-model="visible" :title="title" width="30%" @close="handleClose">
		<div v-if="nodeData">
			<img :src="nodeData.source" style="width: 100px; height: 100px" />
			<p>名称：{{ nodeData.name }}</p>
		</div>
	</el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';


const props = defineProps({
	modelValue: {
		type: Boolean,
		required: true,
	},
	title: {
		type: String,
		default: '节点详情',
	},
	nodeData: {
		type: Object,
		default: null,
	},
});

const emit = defineEmits(['update:modelValue']);

const visible = ref(props.modelValue);

watch(
	() => props.modelValue,
	(newVal) => {
		visible.value = newVal;
	}
);

const handleClose = () => {
	visible.value = false;
	emit('update:modelValue', false);
};
</script>

<style scoped></style>
