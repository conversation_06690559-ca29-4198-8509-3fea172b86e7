<template>
	<el-dialog v-model="visible" :title="title" width="30%" @close="handleClose">
		<el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
			<!-- 名称 -->
			<el-form-item label="名称" prop="name">
				<el-input v-model="formData.name" placeholder="请输入名称"></el-input>
			</el-form-item>

			<!-- 节点一 -->
			<el-form-item label="节点一" prop="nodeOne">
				<el-select v-model="formData.nodeOne" placeholder="请选择节点一">
					<el-option v-for="(item, index) in nodeData.select" :label="item.name" :value="item.key"></el-option>
				</el-select>
			</el-form-item>

			<!-- 节点二 -->
			<el-form-item label="节点二" prop="nodeTwo">
				<el-select v-model="formData.nodeTwo" placeholder="请选择节点二">
					<el-option v-for="(item, index) in nodeData.select" :label="item.name" :value="item.key"></el-option>
				</el-select>
			</el-form-item>

			<!-- 节点三 -->
			<el-form-item label="节点三" prop="nodeThree">
				<el-select v-model="formData.nodeThree" placeholder="请选择节点三">
					<el-option v-for="(item, index) in nodeData.select" :label="item.name" :value="item.key"></el-option>
				</el-select>
			</el-form-item>
		</el-form>

		<!-- 底部按钮 -->
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="handleClose">取消</el-button>
				<el-button type="primary" @click="handleSubmit">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';

// Props 定义
const props = defineProps({
	modelValue: {
		type: Boolean,
		required: true,
	},
	title: {
		type: String,
		default: '节点详情',
	},
	nodeData: {
		type: Object,
		default: () => {
			return {};
		},
	},
});

// Emits 定义
const emit = defineEmits(['update:modelValue', 'submit']);

// 表单数据
const formData = ref({
	name: '', // 名称
	nodeOne: '', // 节点一
	nodeTwo: '', // 节点二
	nodeThree: '', // 节点三
});

// 表单引用
const formRef = ref<FormInstance | null>(null);

// 校验规则
const rules: FormRules = {
	name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
	nodeOne: [{ required: true, message: '请选择节点一', trigger: 'change' }],
	nodeTwo: [{ required: true, message: '请选择节点二', trigger: 'change' }],
	nodeThree: [{ required: true, message: '请选择节点三', trigger: 'change' }],
};

// 内部状态
const visible = ref(props.modelValue);

// 监听父组件传递的 modelValue 更新内部状态
watch(
	() => props.modelValue,
	(newVal) => {
		visible.value = newVal;
	}
);

// 关闭对话框
const handleClose = () => {
	visible.value = false;
	emit('update:modelValue', false);
};

// 提交表单
const handleSubmit = () => {
	if (!formRef.value) return;

	formRef.value.validate((valid) => {
		if (valid) {
			// 校验通过，通知父组件
			emit('submit', formData.value);
			handleClose(); // 关闭对话框
		} else {
			console.log('表单校验失败');
		}
	});
};
</script>

<style scoped>
.dialog-footer {
	text-align: right;
}
</style>
