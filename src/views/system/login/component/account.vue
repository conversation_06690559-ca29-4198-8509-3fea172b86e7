<template>
	<el-form ref="formRef" size="large" class="login-content-form" :model="state.ruleForm" :rules="rules" @keyup.enter="loginClick">
		<el-form-item class="login-animation1" prop="username">
			<el-input
				class="login-content-input"
				type="text"
				:placeholder="$t('message.account.accountPlaceholder1')"
				v-model="ruleForm.username"
				clearable
				autocomplete="off"
			>
				<template #prefix>
					<img style="width: 20px; height: 23px; margin: 10px 10px 10px 0" :src="userIcon" alt="" srcset="" />
				</template>
			</el-input>
		</el-form-item>
		<el-form-item class="login-animation2" prop="password">
			<el-input
				class="login-content-input"
				type="password"
				show-password
				:placeholder="$t('message.account.accountPlaceholder2')"
				v-model="ruleForm.password"
			>
				<template #prefix>
					<img style="width: 16px; height: 21px; margin: 10px 10px 10px 0" :src="passIcon" alt="" srcset="" />
				</template>
			</el-input>
		</el-form-item>
		<el-form-item class="login-animation3" v-if="isShowCaptcha" prop="captcha">
			<el-col :span="15">
				<el-input
					class="login-content-input"
					type="text"
					maxlength="4"
					:placeholder="$t('message.account.accountPlaceholder3')"
					v-model="ruleForm.captcha"
					autocomplete="off"
				>
				</el-input>
			</el-col>
			<el-col :span="1"></el-col>
			<el-col :span="8">
				<el-button class="login-content-captcha">
					<el-image style="height: 46px" :src="ruleForm.captchaImgBase" @click="refreshCaptcha" />
				</el-button>
			</el-col>
		</el-form-item>
		<el-form-item class="login-animation4">
			<el-button class="login-content-submit" round @click="loginClick" :loading="loading.signIn">
				<span class="text-[#3574F3] text-[20px] font-[500]">{{ $t('message.account.accountBtnText') }}</span>
			</el-button>
		</el-form-item>
	</el-form>
</template>

<script lang="ts">
import { toRefs, reactive, defineComponent, computed, onMounted, onUnmounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, FormInstance, FormRules } from 'element-plus';
import { useI18n } from 'vue-i18n';
import Cookies from 'js-cookie';
import { storeToRefs } from 'pinia';
import { useThemeConfig } from '/@/stores/themeConfig';
import { initFrontEndControlRoutes } from '/@/router/frontEnd';
import { initBackEndControlRoutes } from '/@/router/backEnd';
import { Session } from '/@/utils/storage';
import { formatAxis } from '/@/utils/formatTime';
import { NextLoading } from '/@/utils/loading';
import * as loginApi from '/@/views/system/login/api';
import { useUserInfo } from '/@/stores/userInfo';
import { DictionaryStore } from '/@/stores/dictionary';
import { SystemConfigStore } from '/@/stores/systemConfig';
import { BtnPermissionStore } from '/@/plugin/permission/store.permission';
import { Md5 } from 'ts-md5';
import { errorMessage } from '/@/utils/message';
import userIcon from '/@/assets/img/login-form-icon-u.png';
import passIcon from '/@/assets/img/login-form-icon-p.png';

export default defineComponent({
	name: 'loginAccount',
	setup() {
		const { t } = useI18n();
		const storesThemeConfig = useThemeConfig();
		const { themeConfig } = storeToRefs(storesThemeConfig);
		const { userInfos } = storeToRefs(useUserInfo());
		const route = useRoute();
		const router = useRouter();
		const state = reactive({
			isShowPassword: false,
			ruleForm: {
				username: 'superadmin',
				password: 'admin123456',
				captcha: '',
				captchaKey: '',
				captchaImgBase: '',
			},
			loading: {
				signIn: false,
			},
		});
		const rules = reactive<FormRules>({
			username: [{ required: true, message: '请填写账号', trigger: 'blur' }],
			password: [
				{
					required: true,
					message: '请填写密码',
					trigger: 'blur',
				},
			],
			captcha: [
				{
					required: true,
					message: '请填写验证码',
					trigger: 'blur',
				},
			],
		});
		const formRef = ref();
		// 时间获取
		const currentTime = computed(() => {
			return formatAxis(new Date());
		});
		// 是否关闭验证码
		const isShowCaptcha = computed(() => {
			return SystemConfigStore().systemConfig['base.captcha_state'];
		});

		const getCaptcha = async () => {
			loginApi.getCaptcha().then((ret: any) => {
				state.ruleForm.captchaImgBase = ret.data.image_base;
				state.ruleForm.captchaKey = ret.data.key;
			});
		};
		const refreshCaptcha = async () => {
			state.ruleForm.captcha = '';
			loginApi.getCaptcha().then((ret: any) => {
				state.ruleForm.captchaImgBase = ret.data.image_base;
				state.ruleForm.captchaKey = ret.data.key;
			});
		};
		const loginClick = async () => {
			if (!formRef.value) return;
			await formRef.value.validate((valid: any) => {
				if (valid) {
					loginApi
						.login({ ...state.ruleForm, password: Md5.hashStr(state.ruleForm.password) })
						.then((res: any) => {
							if (res.code === 2000) {
								Session.set('token', res.data.access);
								Cookies.set('username', res.data.name);
								if (!themeConfig.value.isRequestRoutes) {
									// 前端控制路由，2、请注意执行顺序
									initFrontEndControlRoutes();
									loginSuccess();
								} else {
									// 模拟后端控制路由，isRequestRoutes 为 true，则开启后端控制路由
									// 添加完动态路由，再进行 router 跳转，否则可能报错 No match found for location with path "/"
									initBackEndControlRoutes();
									// 执行完 initBackEndControlRoutes，再执行 signInSuccess
									loginSuccess();
								}
							}
						})
						.catch((err: any) => {
							// 登录错误之后，刷新验证码
							refreshCaptcha();
						});
				} else {
					// errorMessage('请填写登录信息');
				}
			});
		};
		const getUserInfo = () => {
			useUserInfo().setUserInfos();
		};

		// 登录成功后的跳转
		const loginSuccess = () => {
			//登录成功获取用户信息,获取系统字典数据
			getUserInfo();
			//获取所有字典
			DictionaryStore().getSystemDictionarys();

			// 初始化登录成功时间问候语
			let currentTimeInfo = currentTime.value;
			// 登录成功，跳到转首页
			// 如果是复制粘贴的路径，非首页/登录页，那么登录成功后重定向到对应的路径中
			if (route.query?.redirect) {
				router.push({
					path: <string>route.query?.redirect,
					query: Object.keys(<string>route.query?.params).length > 0 ? JSON.parse(<string>route.query?.params) : '',
				});
			} else {
				router.push('/');
			}
			// 登录成功提示
			// 关闭 loading
			state.loading.signIn = true;
			const signInText = t('message.signInText');
			ElMessage.success(`${currentTimeInfo}，${signInText}`);
			// 添加 loading，防止第一次进入界面时出现短暂空白
			NextLoading.start();
		};
		onMounted(() => {
			getCaptcha();
			//获取系统配置
			SystemConfigStore().getSystemConfigs();
		});

		return {
			refreshCaptcha,
			loginClick,
			loginSuccess,
			isShowCaptcha,
			state,
			formRef,
			rules,
			passIcon,
			userIcon,
			...toRefs(state),
		};
	},
});
</script>

<style scoped lang="scss">
.login-content-form {
	margin-top: 20px;

	.login-content-input {
		:deep(.el-input__wrapper) {
			padding: 5px 10px;
			border-radius: 8px;
		}
	}

	.login-content-captcha {
		width: 100%;
		height: 48px;
		padding: 0;
		font-weight: bold;
		letter-spacing: 5px;
	}
	.login-content-submit {
		width: 100%;
		letter-spacing: 2px;
		font-weight: 300;
		margin-top: 15px;
		padding: 25px !important;
		border-radius: 100px;
		box-shadow: 0px 3px 5px 0px rgba(26, 57, 179, 0.32);
	}
}
</style>
