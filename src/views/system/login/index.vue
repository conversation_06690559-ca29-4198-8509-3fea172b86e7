<template>
	<div class="login-container flex z-10">
		<div class="login-left">
			<!-- <div class="login-left-logo">
				<img :src="siteLogo" />
				<div class="login-left-logo-text">
					<span>{{ getSystemConfig['login.site_title'] || getThemeConfig.globalViceTitle }}</span>
					<span class="login-left-logo-text-msg">{{ getSystemConfig['login.site_name'] || getThemeConfig.globalViceTitleMsg }}</span>
				</div>
			</div> -->
			<div class="login-left-img">
				<img class="login-left-img-logo" :src="loginLogo" />
				<!-- <img class="login-left-title" :src="loginTitle" /> -->
			</div>
			<!-- <img :src="loginBg" class="login-left-waves" /> -->
		</div>
		<div class="login-right flex z-10">
			<div class="login-right-warp flex-margin">
				<div class="login-right-warp-mian">
					<div class="login-right-warp-main-title text-white">
						<!-- {{ getSystemConfig['login.site_title'] || getThemeConfig.globalTitle }} 欢迎您！ -->
						账号密码登录
					</div>
					<div class="login-right-warp-main-form">
						<Account />
						<!-- <div v-if="!state.isScan">
							<el-tabs v-model="state.tabsActiveName">
								<el-tab-pane :label="$t('message.label.one1')" name="account">
								</el-tab-pane>
							</el-tabs>
						</div> -->
						<!-- <Scan v-if="state.isScan" /> -->
						<!-- <div class="login-content-main-sacn" @click="state.isScan = !state.isScan">
							<i class="iconfont" :class="state.isScan ? 'icon-diannao1' : 'icon-barcode-qr'"></i>
							<div class="login-content-main-sacn-delta"></div>
						</div> -->
					</div>
				</div>
			</div>
		</div>

		<!-- <div class="login-authorization z-10">
			<p>Copyright © {{ getSystemConfig['login.copyright'] || '2021-2024 django-vue-admin.com' }} 版权所有</p>
			<p class="la-other">
				<a href="https://beian.miit.gov.cn" target="_blank">{{ getSystemConfig['login.keep_record'] || '晋ICP备********号-3' }}</a>
				|
				<a :href="getSystemConfig['login.help_url'] ? getSystemConfig['login.help_url'] : 'https://django-vue-admin.com'" target="_blank">帮助</a>
				|
				<a :href="getSystemConfig['login.privacy_url'] ? getBaseURL(getSystemConfig['login.privacy_url']) : '#'">隐私</a>
				|
				<a :href="getSystemConfig['login.clause_url'] ? getBaseURL(getSystemConfig['login.clause_url']) : '#'">条款</a>
			</p>
		</div> -->
	</div>
	<!-- <div v-if="siteBg">
		<img :src="siteBg" class="fixed inset-0 z-1 w-full h-full" />
	</div> -->
</template>

<script setup lang="ts" name="loginIndex">
import { defineAsyncComponent, onMounted, reactive, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useThemeConfig } from '/@/stores/themeConfig';
import { NextLoading } from '/@/utils/loading';
import logoMini from '/@/assets/logo-mini.svg';
import loginMain from '/@/assets/login-main.svg';
import loginBg from '/@/assets/img/login-bg.png';
import loginLogo from '/@/assets/img/logo-login-page.png';
import loginTitle from '/@/assets/img/login-title.png';
import { SystemConfigStore } from '/@/stores/systemConfig';
import { getBaseURL } from '/@/utils/baseUrl';
// 引入组件
const Account = defineAsyncComponent(() => import('/@/views/system/login/component/account.vue'));
const Mobile = defineAsyncComponent(() => import('/@/views/system/login/component/mobile.vue'));
const Scan = defineAsyncComponent(() => import('/@/views/system/login/component/scan.vue'));
import _ from 'lodash-es';

// 定义变量内容
const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);
const state = reactive({
	tabsActiveName: 'account',
	isScan: false,
});

// 获取布局配置信息
const getThemeConfig = computed(() => {
	return themeConfig.value;
});

const systemConfigStore = SystemConfigStore();
const { systemConfig } = storeToRefs(systemConfigStore);
const getSystemConfig = computed(() => {
	return systemConfig.value;
});

const siteLogo = computed(() => {
	if (!_.isEmpty(getSystemConfig.value['login.site_logo'])) {
		return getSystemConfig.value['login.site_logo'];
	}
	return logoMini;
});

const siteBg = computed(() => {
	if (!_.isEmpty(getSystemConfig.value['login.login_background'])) {
		return getSystemConfig.value['login.login_background'];
	}
});

// 页面加载时
onMounted(() => {
	NextLoading.done();
});
</script>

<style scoped lang="scss">
.login-container {
	height: 100%;
	background-image: url('../../../assets/img/login-bg.png');
	background-size: cover;
	display: grid;
	grid-template-columns: repeat(2, 1fr);

	.login-left {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		.login-left-logo {
			animation: logoAnimation 0.3s ease;
		}

		.login-left-img {
			display: flex;
			flex-direction: column; /* 如果想要垂直排列 */
			align-items: center; /* 水平居中 */
			justify-content: center; /* 垂直居中 */
			.login-left-img-logo {
				width: 400px;
			}
			.login-left-title {
				width: 450px;
			}
		}
	}

	.login-right {
		.login-right-warp {
			width: 600px;
			height: 500px;
			position: relative;
			overflow: hidden;

			.login-right-warp-mian {
				display: flex;
				flex-direction: column;
				height: 100%;
				align-items: center;
				justify-content: center;

				.login-right-warp-main-title {
					font-size: 24px;
					text-align: center;
					letter-spacing: 3px;
					font-weight: 500;
					// animation: logoAnimation 0.3s ease;
					// animation-delay: 0.3s;
				}

				.login-right-warp-main-form {
					// flex: 1;
					padding: 0 50px 50px;

					// .login-content-main-sacn {
					// 	position: absolute;
					// 	top: 2px;
					// 	right: 12px;
					// 	width: 50px;
					// 	height: 50px;
					// 	overflow: hidden;
					// 	cursor: pointer;
					// 	transition: all ease 0.3s;
					// 	color: var(--el-color-primary);

					// 	&-delta {
					// 		position: absolute;
					// 		width: 35px;
					// 		height: 70px;
					// 		z-index: 2;
					// 		top: 2px;
					// 		right: 21px;
					// 		background: var(--el-color-white);
					// 		transform: rotate(-45deg);
					// 	}

					// 	&:hover {
					// 		opacity: 1;
					// 		transition: all ease 0.3s;
					// 		color: var(--el-color-primary) !important;
					// 	}

					// 	i {
					// 		width: 47px;
					// 		height: 50px;
					// 		display: inline-block;
					// 		font-size: 48px;
					// 		position: absolute;
					// 		right: 1px;
					// 		top: 0px;
					// 	}
					// }
				}
			}
		}
	}

	.login-authorization {
		position: fixed;
		bottom: 30px;
		left: 0;
		right: 0;
		text-align: center;

		p {
			font-size: 14px;
			color: rgba(0, 0, 0, 0.5);
		}

		a {
			color: var(--el-color-primary);
			margin: 0 5px;
		}
	}
}
</style>
