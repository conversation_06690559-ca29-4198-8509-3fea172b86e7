<template>
	<div>
		<button @click="openPopup">{{ params.value }}</button>
		<el-dialog append-to-body v-model="showPopup" :title="title">
			<ag-grid-vue
				:gridOptions="gridOptions"
				style="width: 100%; height: 400px"
				:class="themeClass"
				:columnDefs="columnDefs"
				@grid-ready="onGridReady"
				:defaultColDef="defaultColDef"
				:rowSelection="rowSelection"
				:rowData="rowData"
				:localeText="localeText"
				@row-selected="onRowSelected"
			/>
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="closePopup">取消</el-button>
					<el-button type="primary" @click="submitPopup">确定</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script setup>
import { computed, ref, onBeforeMount } from 'vue';
import { AgGridVue } from 'ag-grid-vue3';
import { selectData } from './data';
const { params } = defineProps(['params']);

import { useTableChartConfig } from '/@/views/table/useTableChartConfig';
const { gridOptions, localeText } = useTableChartConfig();
import { ModuleRegistry, ClientSideRowModelModule } from 'ag-grid-community';
import { ColumnsToolPanelModule, MenuModule, RowGroupingModule } from 'ag-grid-enterprise';

ModuleRegistry.registerModules([ClientSideRowModelModule, ColumnsToolPanelModule, MenuModule, RowGroupingModule]);

const columnDefs = ref([
	{
		minWidth: 50,
		width: 50,
		checkboxSelection: true,
		sortable: false,
		suppressMenu: true,
		headerCheckboxSelection: true,
		filter: null,
		cellClass: 'flex justify-center items-center',
		headerStyle: { 'text-align': 'center' },
	},
	{ field: 'year', maxWidth: 90 },
	{ field: 'athlete', minWidth: 150 },
	{ field: 'age', maxWidth: 90 },
	{ field: 'sport', minWidth: 150 },
	{ field: 'gold' },
	{ field: 'silver' },
	{ field: 'bronze' },
]);

const defaultColDef = ref({
	flex: 1,
	minWidth: 100,
});

const rowSelection = ref({ mode: 'singleRow' }); // 设置为单选模式

const rowData = ref([]);
const showPopup = ref(false);
const title = computed(() => `选择${params.colDef.headerName}`);
const getValue = () => {
	const rowData = params.node.data;
	return selectedRows.value?.year || rowData.Year;
};
const openPopup = () => {
	showPopup.value = true;
};
const closePopup = () => (showPopup.value = false);
const submitPopup = () => {
	params.api.stopEditing();
	const rowData = params.node.data;
	rowData.Year = selectedRows.value?.year;
	params.api.applyTransaction({ update: [rowData] });
	closePopup();
};
const selectedRows = ref({});
const onRowSelected = ({ data }) => {
	selectedRows.value = data;
};
const gridApi = ref(null);
const onGridReady = (params) => {
	gridApi.value = params.api;
	rowData.value = selectData;
};
</script>

<style></style>
