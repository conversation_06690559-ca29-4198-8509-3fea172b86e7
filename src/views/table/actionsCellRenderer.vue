<script setup>
import { defineProps } from 'vue';
import { Delete } from '@element-plus/icons-vue';

const { params } = defineProps(['params']);

const onRemoveClick = () => {
	const rowData = params.node.data;
	const transaction = { remove: [rowData] };
	params.api.applyServerSideTransaction(transaction);
};

const onStopSellingClick = () => {
	const rowData = params.node.data;

	const isPaused = rowData.status === 'paused';
	const isOutOfStock = rowData.available <= 0;

	// Modify the status property
	rowData.status = !isPaused ? 'paused' : !isOutOfStock ? 'active' : 'outOfStock';

	// Refresh the row to reflect the changes
	params.api.applyServerSideTransaction({ update: [rowData] });
};
</script>

<template>
	<div class="flex justify-center items-center h-full">
		<el-button link :icon="Delete" @click="onRemoveClick" />
	</div>
</template>

<style></style>
