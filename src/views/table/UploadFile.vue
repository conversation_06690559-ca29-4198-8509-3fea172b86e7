<template>
	<div style="display: inline-block">
		<el-button size="default" type="success" @click="handleImport()">
			<slot>导入</slot>
		</el-button>
		<el-dialog :close-on-click-modal="false" title="上传情景包" v-model="uploadShow" width="400px" append-to-body>
			<div v-loading="isUploading">
				<el-upload
					ref="uploadRef"
					:limit="1"
					accept=".zip,.tar.gz"
					:headers="props.upload.headers"
					:action="props.upload.url"
					:disabled="isUploading"
					:on-progress="handleFileUploadProgress"
					:on-success="handleFileSuccess"
					:auto-upload="false"
					drag
					:data="{
						collection_id: '16',
					}"
				>
					<i class="el-icon-upload" />
					<div class="el-upload__text">
						将文件拖到此处，或
						<em>点击上传</em>
					</div>
					<template #tip>
						<div class="el-upload__tip" style="color: red">提示：模型包文件,目前仅支持.zip和.tar.gz两种格式！</div>
					</template>
				</el-upload>
			</div>
			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" :disabled="isUploading" @click="submitFileForm">确 定</el-button>
					<el-button :disabled="isUploading" @click="uploadShow = false">取 消</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup name="importExcel">
import { inject, ref } from 'vue';
import { getBaseURL } from '/@/utils/baseUrl';
import { Session } from '/@/utils/storage';
import { ElMessageBox } from 'element-plus';

let props = defineProps({
	upload: {
		type: Object,
		default() {
			return {
				// 是否显示弹出层
				open: true,
				// 是否禁用上传
				isUploading: false,
				// 设置上传的请求头部
				headers: { Authorization: 'JWT ' + Session.get('token') },
				// 上传的地址
				url: getBaseURL() + 'api/pm/scene/package/',
			};
		},
	},
});

const uploadRef = ref();
const uploadShow = defineModel({ default: false });
const isUploading = ref(false);
/** 导入按钮操作 */
const handleImport = function () {
	uploadShow.value = true;
};
// 文件上传中处理
const handleFileUploadProgress = function (event: any, file: any, fileList: any) {
	isUploading.value = true;
};
// 文件上传成功处理
const handleFileSuccess = function (response: any, file: any, fileList: any) {
	isUploading.value = false;
	uploadRef.value.clearFiles();
};
// 提交上传文件
const submitFileForm = function () {
	uploadRef.value.submit();
};
</script>

<style scoped></style>
