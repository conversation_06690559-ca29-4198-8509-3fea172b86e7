<template>
	<div class="flex flex-col h-full">
		<UploadFile />
		<!-- 按钮区域，提供导出、导入、新增和保存功能 -->
		<el-space wrap class="py-4" :size="10">
			<!-- 导出按钮，点击后执行 handleExport 方法 -->
			<el-button @click="handleExport">导出</el-button>
			<!-- 导入按钮，点击后执行 ImportExcel 组件的逻辑 -->
			<ImportExcel @success="loadDataSuccess" dateFormat="YYYY-MM-DD">
				<el-button>导入</el-button>
			</ImportExcel>
			<!-- 新增按钮，点击后执行 addRow 方法 -->
			<el-button @click="addRow">新增</el-button>
			<!-- 删除按钮，点击后执行 removeRow 方法 -->
			<el-button @click="removeRow">删除</el-button>
			<!-- 保存按钮，点击后执行 handleSave 方法 -->
			<el-button @click="handleSave">保存</el-button>
			<!-- 隐藏/显示列按钮，点击后执行 setColumnsVisible 方法 -->
			<el-button @click="api.setColumnsVisible(['Gold'], false)">隐藏Gold列</el-button>
			<el-button @click="api.setColumnsVisible(['Gold'], true)">显示Gold列</el-button>
			<el-input v-model="quickFilter" @input="onQuickFilterChanged" style="width: 240px" placeholder="搜索输入后看列表变化" />
		</el-space>
		<!-- v-if="showGrid" 条件渲染：仅当 showGrid 为 true 时才渲染表格 -->
		<!-- :gridOptions="gridOptions" 设置表格选项 -->
		<!-- :suppressRowClickSelection="true" 禁用点击行时的选中效果 -->
		<!-- :class="`w-full flex-1`" 设置表格的 CSS 类名，宽度全屏 -->
		<!-- :rowData="rowData" 传递行数据 -->
		<!-- :columnDefs="columnDefs" 传递列定义 -->
		<!-- @grid-ready="onReady" 表格加载完成时的回调 -->
		<!-- :defaultColDef="defaultColDef" 定义列的默认属性 -->
		<!-- :localeText="localeText" 设置本地化文本（中文） -->
		<!-- :suppressAggFuncInHeader="true" 禁用列头中的聚合函数显示 -->
		<!-- :enableRangeSelection="true" 启用范围选择 -->
		<!-- :enableRangeHandle="true" 启用范围选择手柄 -->
		<!-- @cell-value-changed="onCellValueChanged" 单元格值变更的回调 -->
		<!-- :statusBar="statusBar" 设置状态栏 -->
		<!-- :enableCharts="true" 启用图表功能 -->
		<!-- :rowSelection="rowSelection" 设置行选择模式 -->
		<!-- 
			editType="fullRow"
		 -->
		<ag-grid-vue
			:class="`w-full flex-1`"
			v-if="showGrid"
			:gridOptions="gridOptions"
			:rowData="rowData"
			:columnDefs="columnDefs"
			:defaultColDef="defaultColDef"
			:localeText="localeText"
			:suppressRowClickSelection="true"
			:suppressAggFuncInHeader="true"
			:enableRangeSelection="true"
			:enableRangeHandle="true"
			:enableCharts="true"
			:theme="gridOptions.theme"
			:rowSelection="rowSelection"
			:sideBar="sideBar"
			rowModelType="serverSide"
			:getRowId="getRowId"
			:suppressServerSideFullWidthLoadingRow="true"
			@cell-value-changed="onCellValueChanged"
			@grid-ready="onReady"
		/>
	</div>
</template>

<script setup lang="ts">
import UploadFile from './UploadFile.vue';
import { ref, onMounted } from 'vue'; // Vue 的 ref 用于响应式数据
import { AgGridVue } from 'ag-grid-vue3';
import ImportExcel from './ImportExcel.vue'; // 自定义导入组件
// https://www.ag-grid.com/vue-data-grid/integrated-charts-container/
// 图表容器
import { NextLoading } from '/@/utils/loading';
onMounted(() => NextLoading.done(600));

import { useTableChartConfig } from '/@/views/table/useTableChartConfig';
const { gridOptions, localeText, sideBar, defaultColDef } = useTableChartConfig();

// 行选择模式（多行选择）
const rowSelection = {
	mode: 'multiRow',
	selectAll: 'filtered',
};

// 定义表格的 API 引用
const api = ref<any>(null);

// 从外部引入列定义和数据
import { columnData, data } from './data';
const mockData = ref<Record<string, any>[]>([...data]);
const rowData = ref<Record<string, any>[]>([]); // 初始化行数据
const columnDefs = ref<Record<string, any>[]>([...columnData]); // 初始化列定义

const quickFilter = ref(''); // 快速筛选输入框
function onQuickFilterChanged() {
	api.value.setGridOption('quickFilterText', quickFilter.value);
}

function getRowId(params: Record<string, any>) {
	return params.data.id;
}

// 网格准备完成时的回调函数
function onReady(params: Record<string, any>) {
	api.value = params.api; // 获取网格 API
	api.value!.setGridOption('serverSideDatasource', dataSource);
}

// 当单元格值修改时调用的回调函数
function onCellValueChanged(event: Record<string, any>) {
	console.log('数据已修改:', event.data); // 输出修改后的数据
}

// 导出数据为 Excel 文件
function handleExport() {
	api.value.exportDataAsExcel({ fileName: 'test.xlsx', sheetName: 'sheet1' }); // 导出 Excel 文件
}

function addRow() {
	const transaction = {
		addIndex: 0,
		add: [
			{
				序号: null,
				id: Math.random(),
				Athlete: "Susie O'Neill",
				Age: 27,
				Country: '11',
				Year: 2000,
				Date: '01/10/2000',
				Sport: 'Swimming',
				Gold: 1,
				Silver: 3,
			},
		],
	};
	const result = api.value.applyServerSideTransaction(transaction);
	setTimeout(() => {
		updateRowIndexes(); // 更新序号列
	}, 0);
}
function updateRowIndexes() {
	if (!api.value) return; // 确保 api 存在
	api.value.forEachNode((node, index) => {
		console.log('打印日志:index,node=>', index, node);

		node.setDataValue('序号', index + 1); // 更新序号列
	});
}

const removeRow = () => {
	const selectedRows = api.value.getSelectedRows();
	if (selectedRows.length === 0) {
		console.warn('[Example] No row selected.');
		return;
	}
	const transaction = { remove: [...selectedRows] };
	api.value.applyServerSideTransaction(transaction);
};

// 保存表格数据
function handleSave() {
	console.log('保存的数据:', rowData.value); // 输出当前表格数据
}

// 控制表格显示/隐藏
const showGrid = ref(true);

// 处理 Excel 导入成功时的回调函数
function loadDataSuccess(excelDataList: ExcelData[]) {
	showGrid.value = false; // 暂时隐藏表格
	setTimeout(() => {
		showGrid.value = true; // 重新显示表格
	}, 0);

	const { columns, rows } = excelDataList[0];
	columnDefs.value.length = 0;
	rowData.value.length = 0;
	columnDefs.value.push(...columns.map((item) => ({ headerName: item, field: item, filter: 'agTextColumnFilter' })));
	rowData.value.push(...rows);
	api.value.setRowData(rowData.value); // 更新行数据
	api.value.setColumnDefs(columnDefs.value); // 更新列定义
}

const dataSource = {
	getRows: async (params: Record<string, any>) => {
		const { startRow, endRow, filterModel, sortModel } = params.request;
		console.log(params.request, sortModel, '打印日志:filterModel=>', filterModel);

		const pageSize = endRow - startRow;
		const page = startRow / pageSize + 1;
		try {
			const { data, total }: any = await loadDate({
				page: page,
				limit: pageSize,
			});
			page === 1 ? (rowData.value = data) : rowData.value.push(...data);
			params.success({ rowData: data, rowCount: total });
		} catch (error) {
			params.fail();
		} finally {
		}
	},
};

// data  模拟分页加载data数据
function loadDate({ page, limit }: { page: number; limit: number }) {
	return new Promise((resolve, reject) => {
		setTimeout(() => {
			const start = (page - 1) * limit;
			const end = start + limit;
			const total = mockData.value.length;
			const data = mockData.value.slice(start, end).map((item, index) => ({ ...item, id: index }));
			resolve({ data, total });
		}, 1000);
	});
}
</script>

<style></style>
