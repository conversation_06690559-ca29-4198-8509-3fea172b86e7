import { request } from '/@/utils/service';
import { AddReq, EditReq } from '@fast-crud/fast-crud';

export const apiPrefix = '/api/pm/simu_info/';

export interface SiMu {
	/**
	 * Create datetime
	 */
	create_datetime?: Date;
	/**
	 * 创建人，创建人
	 */
	creator?: number | null;
	/**
	 * Creator name
	 */
	creator_name?: string;
	/**
	 * 数据归属部门，数据归属部门
	 */
	dept_belong_id?: null | string;
	/**
	 * 描述，描述
	 */
	description?: null | string;
	/**
	 * Id，Id
	 */
	id: number | string;
	/**
	 * 市场规则
	 */
	market_rule_id: number | string;
	/**
	 * 修改人，修改人
	 */
	modifier?: null | string;
	/**
	 * Modifier name
	 */
	modifier_name?: string;
	/**
	 * 仿真名称，仿真名称
	 */
	name: string;
	/**
	 * 情景包
	 */
	scene_model_id: string;
	/**
	 * Update datetime
	 */
	update_datetime?: Date;
	[property: string]: any;
}

export function GetList(data: Recordable) {
	return request({
		url: apiPrefix,
		method: 'get',
		params: data,
	});
}
export function GetSiMu(id: number) {
	return request({
		url: apiPrefix + id,
		method: 'get',
	});
}

export function AddSiMu(SiMu: AddReq) {
	return request({
		url: apiPrefix,
		method: 'post',
		data: SiMu,
	});
}

export function UpdateSiMu(SiMu: EditReq) {
	return request({
		url: apiPrefix + SiMu.id + '/',
		method: 'put',
		data: SiMu,
	});
}

export function DelSiMu(id: number | string) {
	return request({
		url: apiPrefix + id + '/',
		method: 'delete',
		data: { id },
	});
}

export function GetScenePackage({ model_id, package_key, package_item_key, limit, page }: Recordable) {
	return request({
		url: apiPrefix + model_id + '/get_scene_package/',
		method: 'post',
		data: { package_key, package_item_key, limit, page },
	});
}

export function UpdateScenePackage({ model_id, list }: Recordable) {
	return request({
		url: apiPrefix + model_id + '/update_scene_package/',
		method: 'post',
		data: { list },
	});
}

export function GetRuleConfig(id: number) {
	return request({
		url: apiPrefix + id + '/get_rule_config/',
		method: 'get',
	});
}

export function UpdateRuleConfig(id: number, data: Recordable) {
	return request({
		url: apiPrefix + id + '/update_rule_config/',
		method: 'post',
		data: { params: data },
	});
}
/* 机组树 */
export function GetUnitBasicTree(id: number | string, data: Recordable) {
	return request({
		url: apiPrefix + id + '/get_unit_basic_tree/',
		method: 'get',
		params: data,
	});
}
/* 用户树 */
export function GetUserBasicTree(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/get_user_basic_tree/`,
		method: 'get',
		params: data,
	});
}
/* 系统用户树 */
export function GetAuxiliaryServiceMarketTree(id: number | string, data?: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/get_auxiliary_service_market_tree/`,
		method: 'get',
		params: data,
	});
}
/* 取消申报 */
export function CancelDeclaration(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/cancel_offer/`,
		method: 'post',
		data,
	});
}
/* 初始化申报 */
export function InitDeclaration(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/init_offer/`,
		method: 'post',
		data,
	});
}
export function GetDeclarationUnitData(id: number | string, data: Recordable) {
	return request({
		url: apiPrefix + id + '/unit_data/',
		method: 'post',
		data,
	});
}
export function saveDeclarationUnitData(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/unit_offer/`,
		method: 'post',
		data,
	});
}

export function GetDeclarationUserData(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/user_data/`,
		method: 'post',
		data,
	});
}
export function saveDeclarationUserData(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/user_offer/`,
		method: 'post',
		data,
	});
}

/** 辅助服务市场系统申报数据 */
export function GetFMAuxiliaryServiceMarketData(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/auxiliary_service_market_system_data/`,
		method: 'post',
		data,
	});
}
/** 保存辅助服务市场系统申报数据 */
export function SaveFMAuxiliaryServiceMarketData(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/auxiliary_service_market_system_offer/`,
		method: 'post',
		data,
	});
}
/** 辅助服务市场机组树 */
export function GetFMAuxiliaryServiceMarketUnitTree(id: number | string, params: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/get_auxiliary_service_market_unit_tree/`,
		method: 'get',
		params,
	});
}
/** 初始化辅助服务市场机组申报 */
export function InitFMAuxiliaryServiceMarketUnitDeclaration(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/init_auxiliary_service_market_unit_offer/`,
		method: 'post',
		data,
	});
}
/** 取消辅助服务市场机组申报 */
export function CancelFMAuxiliaryServiceMarketUnitDeclaration(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/cancel_auxiliary_service_market_unit_offer/`,
		method: 'post',
		data,
	});
}
/** 辅助服务市场机组申报查询 */
export function GetFMAuxiliaryServiceMarketUnitData(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/auxiliary_service_market_unit_data/`,
		method: 'post',
		data,
	});
}

/** 保存辅助服务市场机组申报 */
export function SaveFMAuxiliaryServiceMarketUnitOffer(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/auxiliary_service_market_unit_offer/`,
		method: 'post',
		data,
	});
}

/** 辅助市场修改 1次 开始 */

// 仿真推演-辅助服务市场-备用容量系统申报-查询数据
export function get_auxiliary_service_reserve_capacity_system_data(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/auxiliary_service_reserve_capacity_system_data/`,
		method: 'post',
		data,
	});
}
// 仿真推演-辅助服务市场-备用容量系统申报-提交数据
export function post_auxiliary_service_reserve_capacity_system_offer(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/auxiliary_service_reserve_capacity_system_offer/`,
		method: 'post',
		data,
	});
}
// 仿真推演-辅助服务市场-备用容量机组申报-查询数据
export function get_auxiliary_service_reserve_capacity_unit_data(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/auxiliary_service_reserve_capacity_unit_data/`,
		method: 'post',
		data,
	});
}
// 仿真推演-辅助服务市场-备用容量机组申报-提交数据
export function post_auxiliary_service_reserve_capacity_unit_offer(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/auxiliary_service_reserve_capacity_unit_offer/`,
		method: 'post',
		data,
	});
}

// 仿真推演-辅助服务市场-爬坡容量系统申报-查询数据
export function get_auxiliary_service_climbing_capacity_system_data(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/auxiliary_service_climbing_capacity_system_data/`,
		method: 'post',
		data,
	});
}

// 仿真推演-辅助服务市场-爬坡容量系统申报-提交数据
export function post_auxiliary_service_climbing_capacity_system_offer(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/auxiliary_service_climbing_capacity_system_offer/`,
		method: 'post',
		data,
	});
}

// 仿真推演-辅助服务市场-爬坡容量机组申报-查询数据
export function get_auxiliary_service_climbing_capacity_unit_data(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/auxiliary_service_climbing_capacity_unit_data/`,
		method: 'post',
		data,
	});
}
// 仿真推演-辅助服务市场-爬坡容量机组申报-提交数据
export function post_auxiliary_service_climbing_capacity_unit_offer(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/auxiliary_service_climbing_capacity_unit_offer/`,
		method: 'post',
		data,
	});
}

/** 仿真推演-辅助服务市场-备用容量申报-查询数据 */
export function GetBackupModulationServiceMarketUnitData(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/auxiliary_service_reserve_capacity_declaration_data/`,
		method: 'post',
		data,
	});
}
/** 仿真推演-辅助服务市场-备用容量申报-提交数据 */
export function SaveBackupModulationServiceMarketUnitOffer(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/auxiliary_service_reserve_capacity_declaration_offer/`,
		method: 'post',
		data,
	});
}

/** 仿真推演-辅助服务市场-备用容量申报-查询数据 */
export function GetRampServiceMarketUnitData(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/auxiliary_service_climbing_capacity_declaration_data/`,
		method: 'post',
		data,
	});
}
/** 仿真推演-辅助服务市场-备用容量申报-提交数据 */
export function SaveRampionServiceMarketUnitOffer(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/auxiliary_service_climbing_capacity_declaration_offer/`,
		method: 'post',
		data,
	});
}
/** 辅助市场修改 1次 结束 */

/* 智能体 */
export function getAgentUnitInfo(data: Recordable) {
	return request({
		url: `/api/pm/agent/get_unit_info/`,
		method: 'post',
		data,
	});
}
export function saveAgentUnitInfo(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/agent/${id}/associated_unit_info/`,
		method: 'post',
		data,
	});
}
export function queryAgentUnitInfo(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/agent/${id}/query_associated_unit_info/`,
		method: 'post',
		data,
	});
}

export function getAgentUserInfo(data: Recordable) {
	return request({
		url: `/api/pm/agent/get_user_info/`,
		method: 'post',
		data,
	});
}

export function saveAgentUserInfo(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/agent/${id}/associated_user_info/`,
		method: 'post',
		data,
	});
}
export function queryAgentUserInfo(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/agent/${id}/query_associated_user_info/`,
		method: 'post',
		data,
	});
}

/** 出清结果 */
export function queryClearEngineResult(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/query_result/`,
		method: 'post',
		data,
	});
}

/** 计算日志 */
export function calculate(id: number | string, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/calculate/`,
		method: 'post',
		data,
	});
}

export function queryCalculateHistory(data: Recordable) {
	return request({
		url: `/api/pm/clearing_engine_calculate_history/`,
		method: 'get',
		params: data,
	});
}

export function queryCalculateLog(data: Recordable) {
	return request({
		url: `/api/pm/clear_engine/query_log/`,
		method: 'post',
		data,
	});
}

/** 获取计算列表 */
export function getComputingTaskList(data: Recordable) {
	return request({
		url: `/api/pm/computing_task/`,
		method: 'get',
		data,
	});
}

/** 调用计算任务 */
export function postCalculate(id: string | number, data: Recordable) {
	return request({
		url: `/api/pm/simu_info/${id}/calculate/`,
		method: 'post',
		data,
	});
}

/** 获取计算任务日志 */
export function postQueryLog(data: Recordable) {
	return request({
		url: `/api/pm/computing_task/query_log/`,
		method: 'get',
		params: data,
	});
}

/** 获取任务状态列表 */
export function getQueryTaskList(data: Recordable) {
	return request({
		url: `/api/pm/computing_task/query_task_list/`,
		method: 'get',
		params: data,
	});
}

export function queryResultTree(id: number | string) {
	return request({
		url: `/api/pm/simu_info/${id}/query_result_tree/`,
		method: 'post',
	});
}

export function exportBbModel(id: number | string) {
	return request({
		url: `/api/pm/simu_info/${id}/export_bb_model/`,
		method: 'post',
	});
}
// ==================================new==================================
export interface CaseInfo {
	id: number | string;
	modifier_name: null;
	name: string;
	cime_model_id: null;
	status: null;
	market_rule_id: number;
	collection_id: number;
	type: number;
	created_at: string;
	updated_at: string;
}
export function getCaseInfo(id: number | string) {
	return request({
		url: `/api/pm/bb_case/${id}`,
		method: 'get',
	});
}

// 潮流计算
export function getRunPF({ bb_case_id }: { bb_case_id: string | number }) {
	return request({
		url: `/api/pm/pf/runPf/`,
		method: 'post',
		data: {
			bb_case_id,
		},
	});
}
