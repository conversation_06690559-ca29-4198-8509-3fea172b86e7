import { request } from '/@/utils/service';
export interface FileTree {
	name: string;
	id: number | string;
	type: 'collection' | 'scenepackagemodel'; // collection 文件夹 or scenepackagemodel 情景包
	description?: string;
	childrens?: FileTree[];
}

export function GetScenePackageList({
	model_id,
	package_key,
	package_item_key,
	limit,
	page,
	filterModel,
	sortModel,
}: {
	model_id: number | string;
	package_key: string;
	package_item_key: string;
	limit: number;
	page: number;
	filterModel: Recordable;
	sortModel: Recordable;
}) {
	return request({
		url: `/api/pm/scene/package/get_model_data/`,
		method: 'post',
		data: {
			model_id,
			package_key,
			package_item_key,
			limit,
			page,
			filterModel,
			sortModel,
		},
	});
}

export function createScenePackage(data: Recordable) {
	return request({
		url: `/api/pm/scene/package/`,
		method: 'post',
		data,
	});
}

export function saveScenePackage(data: Recordable) {
	return request({
		url: `/api/pm/scene/package/save_model_data/`,
		method: 'post',
		data,
	});
}

interface ResponseData<T> {
	code?: number;
	data: T;
	msg?: string;
}

export function getTree<T>(): Promise<ResponseData<T>> {
	return request({
		url: `/api/pm/pmcollection/scene_tree/`,
		method: 'get',
	});
}

export type Create = { name: string; description?: string; parent_id?: number | string };

export function createTreeChild<T>(data: Create): Promise<ResponseData<T>> {
	return request({
		url: `/api/pm/pmcollection/`,
		method: 'post',
		data,
	});
}

export function updateTreeChild(data: { id: number; name: string; description: string }) {
	return request({
		url: `/api/pm/pmcollection/${data.id}/`,
		method: 'put',
		data: { name: data.name, description: data.description },
	});
}

export function deleteTreeChild(id: number) {
	return request({
		url: `/api/pm/pmcollection/${id}/archive/`,
		method: 'post',
	});
}

export function moveTreeChild({ id, moved_id }: { id: number; moved_id: number }) {
	return request({
		url: `/api/pm/pmcollection/move/`,
		method: 'post',
		data: { moved_id, id },
	});
}

// 复制实例
export function copyInstance(data: { simu_info_id: string | number; collection_id: string | number }) {
	return request({
		url: `/api/pm/model_factory/create_duplicate_instance/`,
		method: 'get',
		params: data,
	});
}
// 创建空模型
export function createBlankInstance(data: { collection_id: string | number }) {
	return request({
		url: `/api/pm/model_factory/create_blank_instance/`,
		method: 'get',
		params: data,
	});
}

export type BBCase = {
	id: number;
	modifier_name: string;
	name: string;
	cime_model_id: string;
	status: string;
	market_rule_id: string;
	collection_id: string;
	type: number;
	created_at: string;
	updated_at: string;
};

// 获取模型列表
export function getCaseList(params: Recordable) {
	return request({
		url: `/api/pm/bb_case/`,
		method: 'get',
		params,
	});
}
