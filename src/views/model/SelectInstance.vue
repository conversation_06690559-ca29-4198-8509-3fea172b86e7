<template>
	<div class="select-instance flex flex-col overflow-hidden h-full">
		<!-- 主体内容区 -->
		<div class="flex-1 flex overflow-hidden">
			<!-- 左侧树结构 -->
			<div class="w-[220px] flex flex-col h-full border-gray-200">
				<div class="p-3 pb-2 border-gray-200">
					<el-input v-model="treeFilterText" placeholder="搜索文件夹" :suffix-icon="Search" size="small" clearable></el-input>
				</div>

				<el-scrollbar class="flex-1">
					<el-tree
						ref="treeRef"
						:data="treeData"
						:props="{
							children: 'childrens',
							label: 'name',
						}"
						node-key="id"
						highlight-current
						:expand-on-click-node="false"
						@node-click="handleNodeClick"
						:filter-node-method="filterNode"
						class="mx-2 mt-2"
					>
						<template #default="{ node, data }">
							<div class="flex items-center">
								<img
									v-if="data.type === 'collection' && !node.expanded"
									src="/@/assets/svg/resources (1).svg"
									alt="folder"
									width="20"
									height="20"
									class="mr-1"
								/>
								<img
									v-if="data.type === 'collection' && node.expanded"
									src="/@/assets/svg/resources.svg"
									alt="folder-open"
									width="20"
									height="20"
									class="mr-1"
								/>
								<img v-if="data.type !== 'collection'" src="/@/assets/svg/resources8.svg" alt="file" width="20" height="20" class="mr-1" />
								<span class="text-sm">{{ data.name }}</span>
							</div>
						</template>
					</el-tree>
				</el-scrollbar>
			</div>

			<!-- 右侧卡片列表 -->
			<div class="flex-1 flex flex-col border-l border-gray-200 overflow-hidden">
				<!-- 模型列表标题 -->
				<div class="px-4 py-3 border-b border-gray-200 flex justify-between items-center">
					<span class="text-sm font-medium text-gray-700">{{ currentFolder?.name || '全部数据' }}</span>
					<div class="flex items-center gap-2">
						<el-input
							v-model="globalSearchKeyword"
							placeholder="搜索模型"
							:prefix-icon="Search"
							clearable
							@input="handleGlobalSearch"
							size="small"
							class="w-48"
						></el-input>
					</div>
				</div>

				<!-- 内容区域 - 使用flex-1让它填充剩余空间 -->
				<div class="flex-1 overflow-hidden">
					<el-scrollbar class="h-full">
						<!-- 加载状态 -->
						<div v-if="loading" class="flex justify-center items-center py-12">
							<el-skeleton :rows="3" animated />
						</div>

						<!-- 空状态 -->
						<div v-else-if="!modelList.length" class="p-6 flex justify-center items-center">
							<el-empty description="暂无可选模型" :image-size="150"></el-empty>
						</div>

						<!-- 模型卡片列表 -->
						<div v-else class="p-6 grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-6">
							<el-card
								v-for="item in modelList"
								:key="item.id"
								class="model-card cursor-pointer transition-all hover:shadow-lg relative"
								:class="{ 'selected-card': isSelected(item) }"
								@click="selectAndConfirm(item)"
								shadow="hover"
							>
								<!-- 选中标记 -->
								<div class="absolute top-2 right-2" v-if="isSelected(item)">
									<el-icon class="text-primary text-xl"><Select /></el-icon>
								</div>

								<!-- 卡片内容 -->
								<div class="flex flex-col h-full">
									<h4 class="font-medium text-[18px] truncate mb-2">{{ item.name }}</h4>
									<div class="text-xs text-gray-500 mb-3">ID: {{ item.id }}</div>
									<div class="flex-1">
										<div v-if="item.market_rule_name" class="mb-2">
											<div class="text-gray-500 text-sm mb-1">市场规则</div>
											<div class="text-sm truncate">{{ item.market_rule_name }}</div>
										</div>
										<div class="mb-2">
											<div class="text-gray-500 text-sm mb-1">创建时间</div>
											<div class="text-sm">{{ item.create_datetime }}</div>
										</div>
									</div>
								</div>
							</el-card>
						</div>
					</el-scrollbar>
				</div>

				<!-- 分页器 - 放在滚动区域外部 -->
				<div class="px-4 py-3 border-t border-gray-200 bg-white" v-if="modelList.length">
					<el-pagination
						v-model:current-page="pagination.page"
						v-model:page-size="pagination.limit"
						:total="pagination.total"
						:page-sizes="[12, 24, 36, 48]"
						layout="total, sizes, prev, pager, next"
						@size-change="handleSizeChange"
						@current-change="handleCurrentChange"
						class="flex justify-end"
					/>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue';
import { ElTree, ElMessage } from 'element-plus';
import { Search, Select } from '@element-plus/icons-vue';
import { getTree,getCaseList, type FileTree } from '/@/views/model/api';
import { GetList, type SiMu } from '/@/views/config/api';
import XEUtils from 'xe-utils';

/**
 * 模型选择组件
 *
 * 使用示例:
 * <SelectInstance
 *   title="选择模型"
 *   :initial-selected="'model-id-1'"
 *   @model-selected="handleModelSelected"
 * />
 *
 * 事件:
 * - model-selected: 用户选择了一个模型，参数为模型ID
 */

// 定义组件属性
const props = defineProps({
	// 组件标题
	title: {
		type: String,
		default: '选择模型',
	},
	// 初始已选模型ID
	initialSelected: {
		type: [String, Number],
		default: '',
	},
});

// 定义组件事件
const emit = defineEmits(['model-selected']);

// 树相关状态
const treeRef = ref<InstanceType<typeof ElTree>>();
const treeData = ref<FileTree[]>([]);
const treeFilterText = ref('');

// 模型列表相关状态
const modelList = ref<SiMu[]>([]);
const loading = ref(false);
const currentFolder = ref<FileTree | null>(null);
const selectedModel = ref<SiMu | null>(null);
const globalSearchKeyword = ref('');

// 分页相关
const pagination = reactive({
	page: 1,
	limit: 12,
	total: 0,
});

// 监听树过滤文本变化
watch(treeFilterText, (val) => {
	debouncedFilterTree(val);
});

// 监听全局搜索关键词变化
watch(globalSearchKeyword, (val) => {
	pagination.page = 1; // 重置到第一页
	loadModelList();
});

// 初始化时加载树数据
onMounted(async () => {
	await loadTreeData();

	// 如果有初始已选模型ID，则加载这个模型数据
	if (props.initialSelected) {
		await loadInitialSelectedModel();
	}

	// 加载根文件夹数据
	if (treeData.value.length > 0) {
		// 选择第一个根节点作为初始文件夹
		const rootFolder = {
			name: '全部数据',
			childrens: treeData.value,
			type: 'collection',
			id: 0,
		} as FileTree;

		currentFolder.value = rootFolder;
		loadModelList();
	}
});

// 加载初始选中的模型
const loadInitialSelectedModel = async () => {
	try {
		// 这里假设有一个根据ID获取单个模型的API
		const { data } = await GetList({ ids: props.initialSelected });

		if (data && data.length > 0) {
			// 将初始选中的模型设置为已选
			selectedModel.value = data[0];
		}
	} catch (error) {
		console.error('Failed to load initial selected model:', error);
		ElMessage.warning('加载初始选中模型失败');
	}
};

// 延迟过滤树节点
const debouncedFilterTree = XEUtils.debounce((val: string) => {
	treeRef.value?.filter(val);
}, 300);

// 树节点过滤方法
const filterNode = (value: string, data: any) => {
	if (!value) return true;
	return data.name.toLowerCase().includes(value.toLowerCase());
};

// 加载树数据
const loadTreeData = async () => {
	try {
		const res = await getTree<FileTree[]>();
		treeData.value = res.data || [];
	} catch (error) {
		console.error('Failed to load tree data:', error);
		ElMessage.error('加载文件夹结构失败');
	}
};

// 处理树节点点击
const handleNodeClick = (data: FileTree) => {
	currentFolder.value = data;
	pagination.page = 1; // 重置到第一页
	loadModelList();
};

// 加载模型列表
const loadModelList = async () => {
	if (!currentFolder.value?.id) {
		// 如果没有选择文件夹，清空列表
		modelList.value = [];
		pagination.total = 0;
		loading.value = false;
		return;
	}

	loading.value = true;
	try {
		const params = {
			collection_id: currentFolder.value.id,
			name: globalSearchKeyword.value || '',
			page: pagination.page,
			limit: pagination.limit,
		};

		const { data, total } = await getCaseList(params);
		modelList.value = data || [];
		pagination.total = total || data?.length || 0;
	} catch (error) {
		console.error('Failed to load model list:', error);
		ElMessage.error('加载模型列表失败');
		modelList.value = [];
		pagination.total = 0;
	} finally {
		loading.value = false;
	}
};

// 处理分页大小变化
const handleSizeChange = (size: number) => {
	pagination.limit = size;
	loadModelList();
};

// 处理页码变化
const handleCurrentChange = (page: number) => {
	pagination.page = page;
	loadModelList();
};

// 处理全局搜索
const handleGlobalSearch = () => {
	loadModelList();
};

// 判断模型是否已选中
const isSelected = (model: SiMu) => {
	return selectedModel.value?.id === model.id;
};

// 选择模型
const selectModel = (model: SiMu) => {
	selectedModel.value = model;
};

// 选择并确认模型
const selectAndConfirm = (model: SiMu) => {
	selectModel(model);
	emit('model-selected', model.id);
};
</script>

<style lang="scss" scoped>
.select-instance {
	background-color: #fff;
}

.model-card {
	transition: all 0.25s ease;
	border-radius: 8px;
	overflow: hidden;
	min-height: 140px;

	&.selected-card {
		border-color: var(--el-color-primary);
		background-color: rgba(var(--el-color-primary-rgb), 0.05);
		transform: translateY(-3px);
		box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
	}

	&:hover {
		transform: translateY(-3px);
		box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
	}

	:deep(.el-card__body) {
		padding: 16px;
		height: 100%;
		display: flex;
		flex-direction: column;
	}
}

/* 媒体查询，确保在不同屏幕尺寸下卡片内容看起来都很舒适 */
@media (max-width: 1280px) {
	.model-card :deep(.el-card__body) {
		padding: 12px;
	}
}

/* 树样式优化 */
:deep(.el-tree-node__content) {
	height: 32px;

	&:hover {
		background-color: var(--el-fill-color-light);
	}
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
	background-color: var(--el-color-primary-light-9);
	color: var(--el-color-primary);
}
</style>
