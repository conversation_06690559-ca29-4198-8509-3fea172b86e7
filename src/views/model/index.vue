<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ArrowLeft, ArrowRight, Search, EditPen, Position, Delete } from '@element-plus/icons-vue';
import { getTree, deleteTreeChild, moveTreeChild, type FileTree } from '/@/views/model/api';
import Folder from './components/Folder.vue';
import ModelList from './ModelList.vue';
import { ElMessageBox, ElTree } from 'element-plus';
import { successNotification } from '/@/utils/message';
import XEUtils from 'xe-utils';

const treeRef = ref<InstanceType<typeof ElTree>>();
const moveTreeRef = ref<InstanceType<typeof ElTree>>();
const filterText = ref('');
watch(
	() => filterText.value,
	(val) => {
		debouncedFilter(treeRef.value, val);
	}
);

const moverFilterText = ref('');
watch(
	() => moverFilterText.value,
	(val) => {
		debouncedFilter(moveTreeRef.value, val);
	}
);

const debouncedFilter = XEUtils.debounce((ref, val) => {
	ref.filter(val);
}, 300);

const filterNode = (value: string, data: any) => {
	if (!value) return true;
	return data.name.includes(value);
};

const handleDeleteFolder = ({ id, name }: { id: number; name: string }) => {
	ElMessageBox.confirm(`您确认删除：${name} 吗?`, '温馨提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		const res = await deleteTreeChild(id);
		if (res?.code === 2000) {
			successNotification(res.msg as string);
			getTreeData();
		}
	});
};

const showTree = ref(true);

const data = ref<FileTree[]>([{ name: '公共文件夹', childrens: [], id: 0, type: 'collection' }]);

// const currenTreeDate = ref<FileTree>({ id: '12a86043-390c-4baa-82b3-ea925c94478c', name: 'test', type: 'scenepackagemodel', childrens: [] });
const currenTreeDate = ref<FileTree>({ name: '全部数据', childrens: data.value, type: 'collection', id: 0 });
const folderLists = computed(() =>
	currenTreeDate.value.type === 'collection' ? currenTreeDate.value.childrens?.filter((item) => item.type === 'collection') : []
);
const fileLists = computed(() =>
	currenTreeDate.value.type === 'collection' ? currenTreeDate.value.childrens?.filter((item) => item.type === 'scenepackagemodel') : []
);

const getTreeData = async () => {
	const res = await getTree<FileTree[]>();
	data.value.length = 0;
	data.value.push(...res.data);
	selectAllData();
};
getTreeData();

const selectAllData = () => {
	currenTreeDate.value = { name: '全部数据', childrens: data.value, type: 'collection', id: 0 };
};
const handleNodeClick = (data: FileTree) => {
	currenTreeDate.value = data;
};
const moveToTargetFolder = ref<Recordable>({});
const handleMoveNodeClick = (data: FileTree) => {
	moveToTargetFolder.value = data;
};
const folderFrom = ref<Recordable>({
	parent_id: 0,
	name: '',
	id: 0,
	description: '',
});
const dialogVisibleForm = ref(false);
const handleFolder = (type: 'add' | 'edit', tree?: FileTree) => {
	if (type === 'add') {
		folderFrom.value = { parent_id: tree?.id || '' };
	} else {
		folderFrom.value = { parent_id: tree!.id, name: tree!.name, id: tree!.id, description: tree!.description };
	}
	dialogVisibleForm.value = true;
};
const dialogVisibleUpload = ref(false);
const handleCreateFile = (data: FileTree) => {
	folderFrom.value = data;
	dialogVisibleUpload.value = true;
};

const filterFolders = (nodes: Recordable[]): Recordable[] => {
	return nodes
		.filter((node) => node.type === 'collection')
		.map((node) => ({
			...node,
			childrens: filterFolders(node.childrens || []),
		}));
};

const folderList = computed(() => {
	return filterFolders(data.value);
});

const dialogVisibleMove = ref(false);
const selectMoveNode = ref();
const handleMoveFolders = (tree: FileTree) => {
	dialogVisibleMove.value = true;
	moverFilterText.value = '';
	moveToTargetFolder.value = {};
	selectMoveNode.value = tree;
};

const moveNode = async () => {
	const res = await moveTreeChild({ id: selectMoveNode.value.id, moved_id: moveToTargetFolder.value.id });
	if (res?.code === 2000) {
		successNotification(res.msg as string);
		getTreeData();
		dialogVisibleMove.value = false;
	}
};

const handleButtonClick = (data: FileTree) => {
	currenTreeDate.value = data;
};
const hoverNode = ref<{ id: number | string; type: 'add' | 'more' }>({
	id: '',
	type: 'add',
});
const handleVisibleChange = (data: FileTree, val: boolean, type: 'add' | 'more') => {
	if (val) {
		hoverNode.value.id = data.id;
		hoverNode.value.type = type;
	} else {
		hoverNode.value.id = '';
		hoverNode.value.type = 'add';
	}
};
</script>

<template>
	<fs-page>
		<div class="flex h-full relative">
			<div
				:style="{ width: showTree ? '280px' : 0 }"
				class="transition-width overflow-hidden flex flex-col h-full duration-300 bg-white border-r border-gray-200"
			>
				<div class="w-full px-4 py-2 pb-0">
					<el-input v-model="filterText" placeholder="搜索" :suffix-icon="Search"> </el-input>
				</div>
				<div
					@click="selectAllData"
					class="cursor-pointer hover:bg-[#f5f7fa] flex items-center justify-between mx-4 my-2 px-2 py-1 group"
					:class="currenTreeDate.id === 0 ? 'bg-[#ebf5ff]' : ''"
				>
					<div class="flex items-center gap-2">
						<img src="/@/assets/svg/resources (3).svg" alt="icon" width="20" height="20" />
						<h4 class="font-bold">全部数据</h4>
					</div>

					<div
						@click.stop="handleFolder('add')"
						class="px-1 opacity-0 group-hover:opacity-100 hover:bg-[#dee2ea] hover:rounded-[2px] w-[20px] h-[20px] flex justify-center items-center"
					>
						<el-icon> <Plus /> </el-icon>
					</div>
				</div>
				<el-scrollbar class="flex-1">
					<el-tree
						class="mx-4 mb-4 mt-0 tree-bg-color"
						:data="data"
						:props="{
							children: 'childrens',
							label: 'name',
						}"
						ref="treeRef"
						default-expand-all
						highlight-current
						:expand-on-click-node="false"
						@node-click="handleNodeClick"
						:filter-node-method="filterNode"
					>
						<template #default="{ node, data }">
							<div
								class="flex group overflow-hidden justify-between items-center pr-2 w-full relative"
								:class="currenTreeDate.id === data.id ? 'select-bg-color' : ''"
							>
								<div class="flex items-center w-full">
									<img
										v-if="data.type === 'collection' && !node.expanded"
										src="/@/assets/svg/resources (1).svg"
										alt="icon"
										width="20"
										height="20"
										class="mr-1"
									/>
									<img
										v-if="data.type === 'collection' && node.expanded"
										src="/@/assets/svg/resources.svg"
										alt="icon"
										width="20"
										height="20"
										class="mr-1"
									/>
									<img v-if="data.type !== 'collection'" src="/@/assets/svg/resources8.svg" alt="icon" width="20" height="20" class="mr-1" />
									{{ data.name }}
								</div>

								<div class="flex items-center gap-1 absolute top-0 right-1">
									<el-dropdown
										trigger="click"
										v-if="data.type === 'collection'"
										@visible-change="(val:boolean) => handleVisibleChange(data, val,'add')"
									>
										<div
											@click.stop=""
											:class="{ 'bg-[#dee2ea] rounded-[2px] opacity-100': hoverNode.id === data.id && hoverNode.type === 'add' }"
											class="opacity-0 group-hover:opacity-100 px-1 hover:bg-[#dee2ea] hover:rounded-[2px] w-[20px] h-[20px] flex justify-center items-center"
										>
											<el-icon> <Plus /> </el-icon>
										</div>
										<template #dropdown>
											<el-dropdown-menu>
												<!-- <el-dropdown-item @click="handleCreateFile(data)">
													<div class="flex items-center gap-2">
														<img src="/@/assets/svg/resources8.svg" alt="icon" width="20" height="20" />
														<p>情景包</p>
													</div>
												</el-dropdown-item> -->
												<el-dropdown-item @click="handleFolder('add', data)">
													<div class="flex items-center gap-2">
														<img src="/@/assets/svg/resources.svg" alt="icon" width="20" height="20" />
														<p>文件夹</p>
													</div>
												</el-dropdown-item>
											</el-dropdown-menu>
										</template>
									</el-dropdown>
									<el-dropdown trigger="click" @visible-change="(val:boolean) => handleVisibleChange(data, val,'more')">
										<div
											@click.stop=""
											:class="{ 'bg-[#dee2ea] rounded-[2px] opacity-100': hoverNode.id === data.id && hoverNode.type === 'more' }"
											class="opacity-0 group-hover:opacity-100 px-1 hover:bg-[#dee2ea] hover:rounded-[2px] w-[20px] h-[20px] flex justify-center items-center"
										>
											<el-icon> <MoreFilled /> </el-icon>
										</div>
										<template #dropdown>
											<el-dropdown-menu>
												<el-dropdown-item v-if="data.type === 'collection'" @click="handleFolder('edit', data)" :icon="EditPen">
													编辑
												</el-dropdown-item>
												<el-dropdown-item @click="handleMoveFolders(data)" :icon="Position"> 移动 </el-dropdown-item>
												<el-dropdown-item v-if="data.type === 'collection'" @click="handleDeleteFolder(data)" :icon="Delete"> 删除 </el-dropdown-item>
											</el-dropdown-menu>
										</template>
									</el-dropdown>
								</div>
							</div>
						</template>
					</el-tree>
				</el-scrollbar>
			</div>
			<el-button
				class="absolute top-[50%] transition-all duration-300"
				style="z-index: 1000"
				:class="{ 'left-[270px]': showTree, 'left-2': !showTree }"
				size="small"
				@click="showTree = !showTree"
				:icon="showTree ? ArrowLeft : ArrowRight"
				circle
			/>
			<div class="flex flex-col flex-1 bg-white overflow-hidden">
				<el-scrollbar class="w-full h-full" v-if="currenTreeDate.type === 'collection'">
					<!-- 根据屏幕尺寸调整显示个数 -->
					<div class="grid gap-4 p-4 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-8" v-show="folderLists?.length">
						<div
							class="group hover:shadow-[0_4px_12px_1px_rgba(0,0,0,0.12)]"
							v-for="(item, index) in folderLists"
							:key="index"
							@click="handleButtonClick(item)"
						>
							<div class="flex items-center w-full opacity-0 group-hover:opacity-100 justify-between px-4" @click.stop="">
								<el-checkbox size="large" />
								<el-icon> <MoreFilled /> </el-icon>
							</div>
							<img v-if="item.type === 'collection'" class="mx-auto" src="/@/assets/svg/resources (4).svg" alt="icon" width="70" height="70" />
							<img v-if="item.type !== 'collection'" class="mx-auto" src="/@/assets/svg/resources8.svg" alt="icon" width="70" height="70" />
							<div class="flex justify-center w-full pt-2 pb-4 px-2">
								<el-text tag="div" truncated> {{ item.name }} </el-text>
							</div>
						</div>
					</div>
					<el-divider class="!m-0" />
					<!-- <div class="grid gap-4 p-4 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-8">
						<div
							class="group hover:shadow-[0_4px_12px_1px_rgba(0,0,0,0.12)]"
							v-for="(item, index) in fileLists"
							:key="index"
							@click="handleButtonClick(item)"
						>
							<div class="flex items-center w-full opacity-0 group-hover:opacity-100 justify-between px-4" @click.stop="">
								<el-checkbox size="large" />
								<el-icon> <MoreFilled /> </el-icon>
							</div>
							<img v-if="item.type === 'scenepackagemodel'" class="mx-auto" src="/@/assets/svg/resources8.svg" alt="icon" width="70" height="70" />
							<div class="flex justify-center w-full pt-2 pb-4 px-2">
								<el-text tag="div" truncated> {{ item.name }} </el-text>
							</div>
						</div>
					</div>
					<el-empty description="该文件夹暂无内容" v-if="!fileLists?.length && !folderLists?.length" /> -->
					<model-list v-if="currenTreeDate.id" :params="currenTreeDate" />
				</el-scrollbar>
			</div>
		</div>

		<el-dialog v-model="dialogVisibleMove" :title="`移动:${selectMoveNode?.name}-选择目标文件夹`" width="400">
			<div class="w-full px-4 pb-4">
				<el-input v-model="moverFilterText" placeholder="搜索" :suffix-icon="Search"> </el-input>
			</div>
			<el-tree
				ref="moveTreeRef"
				@node-click="handleMoveNodeClick"
				class="mx-4 mb-4 mt-0 tree-bg-color"
				:data="folderList"
				node-key="id"
				:props="{
					children: 'childrens',
					label: 'name',
				}"
				highlight-current
				default-expand-all
				:expand-on-click-node="false"
				:filter-node-method="filterNode"
			>
				<template #default="{ node, data }">
					<div class="flex items-center w-full" :class="moveToTargetFolder.id === data.id ? 'select-bg-color' : ''">
						<img v-if="!node.expanded" src="/@/assets/svg/resources (1).svg" alt="icon" width="20" height="20" class="mr-1" />
						<img v-else src="/@/assets/svg/resources.svg" alt="icon" width="20" height="20" class="mr-1" />
						<span>{{ data.name }}</span>
					</div>
				</template>
			</el-tree>
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="dialogVisibleMove = false">取消</el-button>
					<el-button type="primary" :disabled="!moveToTargetFolder" @click="moveNode"> 确定 </el-button>
				</div>
			</template>
		</el-dialog>
		<Folder v-model="dialogVisibleForm" :form="folderFrom" @successful="getTreeData" />
	</fs-page>
</template>

<style lang="scss" scoped>
.transition-all {
	transition: all 0.3s ease;
}
// .tree-bg-color {
// 	:deep(.el-tree-node__expand-icon) {
// 		display: none;
// 	}
// 	:deep(.el-tree-node__content) {
// 		width: 100%;
// 	}
// }
</style>
