<template>
	<div class="h-full bg-white flex flex-col">
		<!-- 顶部搜索和筛选区域 -->
		<div class="p-4 flex items-center justify-between border-b border-gray-200">
			<div class="flex items-center gap-2 w-2/3">
				<!-- 搜索框 -->
				<div class="relative w-full max-w-md">
					<el-input v-model="searchKeyword" placeholder="输入关键词" :prefix-icon="Search" clearable @change="getDataList" class="w-full"></el-input>
				</div>
				<!-- 筛选按钮 -->
				<!-- <el-button @click="showFilterPanel = !showFilterPanel">
					<el-icon><Filter /></el-icon>
				</el-button> -->
			</div>

			<!-- 视图切换和新建按钮 -->
			<div class="flex items-center gap-2">
				<!-- 视图切换 -->
				<div class="bg-gray-100 rounded-md p-1 flex">
					<div
						@click="viewMode = 'card'"
						:class="[viewMode === 'card' ? 'bg-white shadow' : 'hover:bg-gray-200', 'px-3 py-1 rounded cursor-pointer flex items-center']"
					>
						<el-icon>
							<Grid />
						</el-icon>
						<span class="ml-1">卡片</span>
					</div>
					<div
						@click="viewMode = 'list'"
						:class="[viewMode === 'list' ? 'bg-white shadow' : 'hover:bg-gray-200', 'px-3 py-1 rounded cursor-pointer flex items-center']"
					>
						<el-icon>
							<List />
						</el-icon>
						<span class="ml-1">列表</span>
					</div>
				</div>

				<!-- 新建项目按钮 -->
				<el-button type="primary" @click="handleCreateProject"> 新建 </el-button>
			</div>
		</div>

		<!-- 筛选面板 -->
		<div v-if="showFilterPanel" class="bg-white p-4 border-b border-gray-200 animate__animated animate__fadeIn">
			<div class="grid grid-cols-2 md:grid-cols-3 gap-4">
				<!-- 名称筛选 -->
				<div>
					<div class="text-gray-600 mb-1">名称</div>
					<el-input v-model="filters.name" placeholder="请输入关键字" clearable></el-input>
				</div>

				<!-- 创建时间筛选 -->
				<div>
					<div class="text-gray-600 mb-1">创建时间</div>
					<el-date-picker
						v-model="filters.dateRange"
						type="daterange"
						range-separator="-"
						start-placeholder="起始日期"
						end-placeholder="结束日期"
						class="w-full"
					></el-date-picker>
				</div>

				<!-- 创建人筛选 -->
				<div>
					<div class="text-gray-600 mb-1">创建人</div>
					<el-select v-model="filters.creator" placeholder="请选择" clearable class="w-full">
						<el-option v-for="user in creatorOptions" :key="user.value" :label="user.label" :value="user.value"></el-option>
					</el-select>
				</div>

				<!-- 市场规则筛选 -->
				<!-- <div>
					<div class="text-gray-600 mb-1">市场规则</div>
					<el-select v-model="filters.marketRule" placeholder="请选择" clearable class="w-full">
						<el-option v-for="rule in marketRuleOptions" :key="rule.value" :label="rule.label" :value="rule.value"></el-option>
					</el-select>
				</div> -->
			</div>

			<!-- 筛选操作按钮 -->
			<div class="flex justify-end mt-4">
				<el-button @click="resetFilters" class="mr-2">重置</el-button>
				<el-button type="primary" @click="applyFilters">应用筛选</el-button>
			</div>
		</div>

		<!-- 列表内容区域 -->
		<div class="flex-1 overflow-auto flex flex-col">
			<!-- 加载状态 -->
			<div v-if="loading" class="flex justify-center items-center flex-1">
				<el-skeleton :rows="4" animated />
			</div>

			<!-- 空状态 -->
			<div v-else-if="!list.length" class="flex flex-col items-center justify-center flex-1">
				<el-empty :description="`暂无${params?.name || ''}数据`" :image-size="200">
					<el-button type="primary" @click="handleCreateProject">立即创建</el-button>
				</el-empty>
			</div>

			<!-- 卡片视图 -->
			<div
				v-else-if="viewMode === 'card'"
				class="p-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2 2xl:grid-cols-3 3xl:grid-cols-4 gap-4 mb-4"
			>
				<el-card
					v-for="item in list"
					:key="item.id"
					class="market-design-card transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
					shadow="hover"
				>
					<template #header>
						<div class="flex items-center justify-between">
							<div class="font-bold text-[18px] truncate mr-2">{{ item.name }}</div>
						</div>
					</template>
					<div class="grid grid-cols-[80px,1fr] gap-y-2 py-2">
						<!-- <div class="text-gray-500">市场规则</div>
						<el-text tag="div" class="font-medium truncate">{{ item.market_rule_name }}</el-text> -->

						<div class="text-gray-500">创建时间</div>
						<el-text tag="div" class="text-sm">{{ item.created_at }}</el-text>

						<div class="text-gray-500">描述</div>
						<el-text tag="div" class="h-[42px] text-sm text-gray-700" line-clamp="2">{{ item.description || '暂无描述' }}</el-text>
					</div>
					<div class="flex justify-end py-3 mt-2 border-t border-gray-100">
						<el-dropdown trigger="click" @command="handleCommand($event, item)">
							<el-button type="primary" text>
								更多操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
							</el-button>
							<template #dropdown>
								<el-dropdown-menu>
									<el-dropdown-item :disabled="true" command="edit">编辑信息</el-dropdown-item>
									<el-dropdown-item :disabled="true" command="delete" divided>删除模型</el-dropdown-item>
								</el-dropdown-menu>
							</template>
						</el-dropdown>
						<el-button @click="toSimulationScreen(item)" class="ml-2">
							<el-icon> <DataLine /> </el-icon>推演大屏
						</el-button>
						<el-button type="primary" @click="toSimulationPage(item)" class="ml-2">
							<el-icon> <SetUp /> </el-icon>进入实验
						</el-button>
					</div>
				</el-card>
			</div>

			<!-- 列表视图 -->
			<el-table v-else class="flex-1" :data="list" v-loading="loading">
				<el-table-column prop="name" label="模型名称" min-width="180">
					<template #default="{ row }">
						<div class="flex items-center">
							<el-icon class="mr-2 text-primary">
								<DocumentAdd />
							</el-icon>
							<span>{{ row.name }}</span>
						</div>
					</template>
				</el-table-column>
				<!-- <el-table-column prop="market_rule_name" label="市场规则" min-width="180"></el-table-column> -->
				<el-table-column prop="creator_name" label="创建人" width="120"></el-table-column>
				<el-table-column prop="created_at" label="创建时间" width="180">
					<template #default="{ row }">
						{{ row.created_at }}
					</template>
				</el-table-column>
				<el-table-column label="操作" width="300" fixed="right">
					<template #default="{ row }">
						<el-space>
							<el-button type="primary" size="small" @click="toSimulationPage(row)">
								<el-icon> <SetUp /> </el-icon>进入实验
							</el-button>
							<el-button size="small" @click="toSimulationScreen(row)">
								<el-icon> <DataLine /> </el-icon>推演大屏
							</el-button>
							<el-dropdown size="small" trigger="click" @command="handleCommand($event, row)">
								<el-button size="small" link>
									更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
								</el-button>
								<template #dropdown>
									<el-dropdown-menu>
										<el-dropdown-item :disabled="true" command="edit">编辑信息</el-dropdown-item>
										<el-dropdown-item :disabled="true" command="delete" divided>删除模型</el-dropdown-item>
									</el-dropdown-menu>
								</template>
							</el-dropdown>
						</el-space>
					</template>
				</el-table-column>
			</el-table>
		</div>
		<!-- 共享分页组件 - 只有在有数据且不在加载状态时显示 -->
		<div v-if="!loading && list.length > 0" class="pagination-container flex justify-end mt-auto py-4 border-t border-gray-100">
			<el-pagination
				v-model:current-page="pagination.page"
				v-model:page-size="pagination.limit"
				:page-sizes="[10, 20, 50, 100]"
				:total="pagination.total"
				:layout="screenWidth <= 768 ? 'prev, pager, next' : 'total, sizes, prev, pager, next, jumper'"
				@size-change="handlePaginationChange"
				@current-change="handlePaginationChange"
			/>
		</div>
		<el-dialog
			v-model="showCreateDialog"
			header-class="!p-0"
			width="1000"
			:close-on-click-modal="false"
			:close-on-press-escape="!uploading"
			:show-close="!uploading"
			destroy-on-close
			@close="handleDialogClose"
			body-class="h-[750px] pt-1"
		>
			<el-tabs v-model="type" type="card" class="!h-full model-tabs">
				<el-tab-pane label="新建模型" name="add" class="h-full">
					<div class="py-4 px-2">
						<div class="mb-6">
							<p class="mb-4 text-gray-600">请选择模型模板：</p>
							<el-radio-group v-model="template" class="flex flex-wrap gap-4">
								<el-radio-button value="1" class="model-template-card">
									<div class="flex flex-col items-center justify-center p-6">
										<el-icon class="text-3xl mb-3 text-primary">
											<DocumentAdd />
										</el-icon>
										<div class="text-lg font-medium">空白模板</div>
									</div>
								</el-radio-button>
							</el-radio-group>
						</div>
					</div>
				</el-tab-pane>
				<el-tab-pane label="调度实例" name="dispatch" class="h-full">
					<div class="flex items-center justify-center h-full">
						<UploadFile
							ref="uploadFileRef"
							:fileForm="{ collection_id: props.params.id, type: 3 }"
							@successful="handleUploadSuccess"
							@uploading="handleUploading"
						/>
					</div>
				</el-tab-pane>

				<el-tab-pane label="复制实例" name="copy" class="h-full">
					<SelectInstance title="选择模型" @model-selected="handleModelSelected" />
				</el-tab-pane>

				<el-tab-pane label="使用模板" name="template" class="h-full">
					<div class="flex flex-col items-center justify-center p-12 text-gray-500 h-full">
						<el-icon class="text-5xl mb-4">
							<Files />
						</el-icon>
						<p>模板功能开发中，敬请期待</p>
					</div>
				</el-tab-pane>
			</el-tabs>
			<template #footer>
				<div class="flex justify-end py-4">
					<el-button @click="showCreateDialog = false">取消</el-button>
					<el-button :loading="submitLoading || uploading" type="primary" @click="handleSubmit">创建</el-button>
				</div>
			</template>
		</el-dialog>

		<!-- 编辑对话框 -->
		<el-dialog v-model="editDialogVisible" title="编辑模型" width="500px" :close-on-click-modal="false">
			<el-form :model="editForm" label-width="80px">
				<el-form-item label="模型名称" required>
					<el-input v-model="editForm.name" placeholder="请输入模型名称" />
				</el-form-item>
				<el-form-item label="模型描述">
					<el-input v-model="editForm.description" type="textarea" :rows="3" placeholder="请输入模型描述" />
				</el-form-item>
			</el-form>
			<template #footer>
				<div class="flex justify-end">
					<el-button @click="editDialogVisible = false">取消</el-button>
					<el-button type="primary" :loading="editLoading" @click="handleEditSubmit"> 确定 </el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox, ElSpace } from 'element-plus';
import {
	Search,
	Filter,
	Grid,
	List,
	ArrowDown,
	Delete,
	Edit,
	DataLine,
	SetUp,
	DocumentAdd,
	Opportunity,
	Connection,
	Files,
} from '@element-plus/icons-vue';
import SelectInstance from './SelectInstance.vue';
import { AddSiMu, GetList, GetSiMu, UpdateSiMu, DelSiMu } from '/@/views/config/api';
import { createBlankInstance, copyInstance, getCaseList, type BBCase } from './api';
import UploadFile from './components/UploadFile.vue';

const type = ref('add');
const template = ref('1');

const props = defineProps({
	params: {
		type: Object,
		default: () => {
			return {
				name: '',
				id: '',
			};
		},
	},
});

// 加载状态
const loading = ref(false);

// 分页参数
const pagination = reactive({
	page: 1,
	limit: 20,
	total: 0,
});

// 视图模式：卡片/列表
const viewMode = ref('list');

// 搜索关键词
const searchKeyword = ref('');

// 筛选面板显示状态
const showFilterPanel = ref(false);

// 筛选条件
const filters = reactive({
	name: '',
	dateRange: [],
	marketRule: '',
	creator: '',
	status: [],
});

// 模拟数据 - 实际项目中应该从API获取
const creatorOptions = ref([
	{ value: 'user1', label: '用户1' },
	{ value: 'user2', label: '用户2' },
	{ value: 'user3', label: '用户3' },
]);

const marketRuleOptions = ref([
	{ value: 'rule1', label: '中长期规则' },
	{ value: 'rule2', label: '现货规则' },
	{ value: 'rule3', label: '两部制规则' },
]);

// 数据列表
const list = ref<BBCase[]>([]);

// 路由
const router = useRouter();

// 选中的模型ID
const modelId = ref('');

// 屏幕宽度响应式处理
const screenWidth = ref(window.innerWidth);

// 监听窗口大小变化
const handleResize = () => {
	screenWidth.value = window.innerWidth;
};

onMounted(() => {
	window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
	window.removeEventListener('resize', handleResize);
});

watch(
	() => props.params,
	(newVal: any, oldVal: any) => {
		if (newVal.id) {
			nextTick(() => {
				getDataList();
			});
		}
	},
	{
		deep: true,
		immediate: true,
	}
);

// 监听搜索关键词变化
watch(searchKeyword, (val) => {
	// 当搜索关键词变化时，重置分页到第一页
	pagination.page = 1;
	getDataList();
});

// 重置筛选条件
const resetFilters = () => {
	filters.name = '';
	filters.dateRange = [];
	filters.marketRule = '';
	filters.creator = '';
	filters.status = [];
};

// 应用筛选条件
const applyFilters = () => {
	pagination.page = 1;
	getDataList();
	showFilterPanel.value = false;
};

// 获取数据列表
const getDataList = async () => {
	loading.value = true;
	try {
		// 构建请求参数
		const params: Record<string, any> = {
			collection_id: props.params.id,
			name: searchKeyword.value || '', // 使用 keyword 作为搜索参数
			page: pagination.page,
			limit: pagination.limit,
		};

		// 添加筛选条件
		if (filters.name) params.name = filters.name;
		if (filters.marketRule) params.market_rule = filters.marketRule;
		if (filters.creator) params.creator = filters.creator;
		if (filters.dateRange && filters.dateRange.length === 2) {
			params.start_time = filters.dateRange[0];
			params.end_time = filters.dateRange[1];
		}

		const res = await getCaseList(params);
		if (res?.code === 2000) {
			// 确保数据结构正确处理
			list.value = res.data || [];
			pagination.total = Number(res?.total || 0);

			// 处理边界情况：当前页没有数据且不是第一页
			if (list.value.length === 0 && pagination.page > 1) {
				pagination.page--;
				getDataList();
			}
		} else {
			// 处理错误响应
			ElMessage.error(res?.message || '获取数据失败');
			list.value = [];
			pagination.total = 0;
		}
	} catch (error) {
		console.error('获取数据失败:', error);
		ElMessage.error('网络错误，请重试');
		list.value = [];
		pagination.total = 0;
	} finally {
		loading.value = false;
	}
};

// 分页变化处理
const handlePaginationChange = (val: number) => {
	// el-pagination 的 @size-change 和 @current-change 事件都会触发这个方法
	// 由于使用了 v-model 绑定，不需要手动设置 pagination.page 和 pagination.limit
	getDataList();
};

const showCreateDialog = ref(false);
// 创建模型
const handleCreateProject = () => {
	showCreateDialog.value = true;
};

// 编辑相关
const editDialogVisible = ref(false);
const editLoading = ref(false);
const editForm = reactive<{
	id: string | number;
	name: string;
	description: string;
	market_rule_id: string | number;
	scene_model_id: string | number;
	collection_id: string | number;
}>({
	id: '',
	name: '',
	description: '',
	market_rule_id: '',
	scene_model_id: '',
	collection_id: '',
});

// 处理编辑命令
const handleCommand = (command: string, item: BBCase) => {
	switch (command) {
		case 'edit':
			handleEdit(item);
			break;
		case 'delete':
			handleDelete(item);
			break;
	}
};

// 打开编辑对话框
const handleEdit = (item: BBCase) => {
	editForm.id = item.id;
	editForm.name = item.name;
	// editForm.description = item.description || '';
	editForm.market_rule_id = item.market_rule_id;
	// editForm.scene_model_id = item.scene_model_id;
	editForm.collection_id = item.collection_id;
	editDialogVisible.value = true;
};

// 提交编辑
const handleEditSubmit = async () => {
	if (!editForm.name.trim()) {
		ElMessage.warning('请输入模型名称');
		return;
	}

	editLoading.value = true;
	try {
		const res = await UpdateSiMu(editForm);
		if (res?.code === 2000) {
			ElMessage.success('更新成功');
			editDialogVisible.value = false;
			getDataList();
		}
	} catch (error) {
		console.error('更新失败:', error);
		ElMessage.error('更新失败，请重试');
	} finally {
		editLoading.value = false;
	}
};

// 处理删除
const handleDelete = (item: BBCase) => {
	ElMessageBox.confirm(`确定要删除模型 "${item.name}" 吗？此操作不可恢复。`, '删除确认', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			try {
				// const res = await DelSiMu(item.id.toString());
				// if (res?.code === 2000) {
				// 	ElMessage.success('删除成功');
				// 	getDataList();
				// }
			} catch (error) {
				console.error('删除失败:', error);
				ElMessage.error('删除失败，请重试');
			}
		})
		.catch(() => {
			// 用户取消删除
		});
};

const submitLoading = ref(false);
const uploadFileRef = ref();
const uploading = ref(false);

// 监听上传状态
const handleUploading = (status: boolean) => {
	uploading.value = status;
	if (!status) {
		showCreateDialog.value = false;
	}
};

// 修改上传成功处理
const handleUploadSuccess = () => {
	getDataList();
};

// 修改对话框关闭逻辑
const handleDialogClose = () => {
	if (!uploading.value) {
		showCreateDialog.value = false;
	}
};

// 修改提交处理
const handleSubmit = async () => {
	if (type.value === 'dispatch') {
		if (!uploadFileRef.value?.submitUpload()) {
			return;
		}
	} else {
		submitLoading.value = true;
		try {
			switch (type.value) {
				case 'add':
					await createBlankInstance({
						collection_id: props.params.id,
					} as any);
					ElMessage.success('创建成功');
					break;

				case 'dispatch':
					ElMessage.info('调度功能开发中');
					break;
				case 'copy':
					if (!modelId.value) {
						ElMessage.warning('请选择要复制的模型实例');
						return;
					}
					await copyInstance({
						collection_id: props.params.id,
						simu_info_id: modelId.value,
					} as any);
					ElMessage.success('复制成功');
					break;
				case 'template':
					ElMessage.info('模板功能开发中');
					break;
			}
			showCreateDialog.value = false;
		} catch (error) {
			console.error('创建失败:', error);
			ElMessage.error('创建失败，请重试');
		} finally {
			submitLoading.value = false;
			getDataList();
		}
	}
};
const mode = import.meta.env.VITE_ROUTER_MODE;

function toSimulationPage(data: BBCase) {
	if ([2, 3].includes(data.type)) {
		window.open(`${window.location.origin}${mode === 'history' ? '/#' : ''}/simulation-page?id=${data.id}&type=model`, '_blank');
	} else {
		window.open(`${window.location.origin}${mode === 'history' ? '/#' : ''}/power-flow-calculation?id=${data.id}&type=list`, '_blank');
	}
}

function toSimulationScreen(data: BBCase) {
	router.push({ name: 'SimuLargeScreen', query: { id: data.id, type: 'model' } });
}

const handleModelSelected = (id: string) => {
	modelId.value = id;
};
</script>

<style lang="scss">
.model-tabs {
	.el-tabs__header {
		margin: 0 !important;
	}
}
</style>
<style lang="scss" scoped>
.el-table {
	--el-table-header-bg-color: #f5f7fa;
}

.market-design-card {
	transition: all 0.3s ease;
	border-radius: 8px;
	overflow: hidden;

	:deep(.el-card__body) {
		padding-bottom: 0;
	}
}

.market-design-card:hover {
	transform: translateY(-4px);
	box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
}

.model-template-card {
	width: 200px;
	height: 110px;
	border-radius: 8px;
	transition: all 0.3s ease;
	overflow: hidden;

	&:deep(.el-radio-button__inner) {
		width: 100%;
		height: 100%;
		padding: 0;
		border-radius: 8px;
		border: 1px solid var(--el-border-color);

		&:hover {
			border-color: var(--el-color-primary);
		}
	}

	&:deep(.el-radio-button__original) {
		opacity: 0;
		position: absolute;
	}
}

/* 分页组件样式 */
.pagination-container {
	padding: 12px 16px;

	:deep(.el-pagination) {
		justify-content: flex-end;
		flex-wrap: wrap;
	}

	@media (max-width: 768px) {
		padding: 8px;

		:deep(.el-pagination) {
			.el-pagination__sizes,
			.el-pagination__total,
			.el-pagination__jump {
				display: none;
			}
		}
	}
}
</style>
