<script setup lang="ts">
import { ref, reactive } from 'vue';
import { Form, type FormSchema } from '/@//components/Form';
import { useForm } from '/@/components/Form/useForm';
import { successNotification } from '/@/utils/message';
import { createTreeChild, updateTreeChild } from '../api';

const emit = defineEmits(['successful']);
const props = defineProps({
	form: {
		type: Object,
		default: { id: '', name: '', description: '', parent_id: '' },
		required: true,
	},
});
const formType = ref<'add' | 'edit'>('add');
const dialogVisible = defineModel({ default: false, type: Boolean });
const schema = reactive<FormSchema[]>([
	{
		field: 'name',
		label: '文件夹名称',
		component: 'Input',
		colProps: { span: 24 },
		formItemProps: {
			rules: [{ required: true, message: '请输入名称' }],
		},
	},
	{
		field: 'description',
		label: '文件夹描述',
		component: 'Input',
		colProps: { span: 24 },
		formItemProps: {
			rules: [{ required: true, message: '请输入描述' }],
		},
		componentProps: { type: 'textarea' },
	},
]);

const { formRegister, formMethods } = useForm();
const { getElFormExpose, getFormData, setValues } = formMethods;
const openDialog = async () => {
	formType.value = props.form.id ? 'edit' : 'add';
	const elFormExpose = await getElFormExpose();
	elFormExpose?.resetFields();
	setValues({ ...props.form });
};
const submitLoading = ref(false);
const formSubmit = async () => {
	const elFormExpose = await getElFormExpose();
	elFormExpose?.validate(async (valid) => {
		if (valid) {
			const params = await getFormData();
			try {
				submitLoading.value = true;
				let res;
				if (formType.value === 'add') {
					res = await createTreeChild({ parent_id: props.form.parent_id, name: params.name, description: params.description });
				} else {
					res = await updateTreeChild({ id: props.form.id, name: params.name, description: params.description });
				}
				if (res?.code === 2000) {
					successNotification(res.msg as string);
					emit('successful');
					dialogVisible.value = false;
				}
			} finally {
				submitLoading.value = false;
			}
		}
	});
};
</script>

<template>
	<el-dialog @open="openDialog" v-model="dialogVisible" :title="formType === 'edit' ? '编辑文件夹' : '创建文件夹'" width="500">
		<Form :schema="schema" @register="formRegister" />
		<template #footer>
			<el-button type="primary" @click="formSubmit" :loading="submitLoading">确定</el-button>
			<el-button @click="dialogVisible = false">取消</el-button>
		</template>
	</el-dialog>
</template>

<style lang="scss" scoped></style>
