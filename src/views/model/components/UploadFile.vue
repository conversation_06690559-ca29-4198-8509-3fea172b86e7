<template>
	<div class="upload-container">
		<div class="upload-header">
			<h3 class="text-lg font-medium mb-2">上传调度实例文件</h3>
			<p class="text-gray-500 text-sm mb-4">支持 .zip 和 .tar.gz 格式的文件上传。上传后系统将自动解析文件内容。</p>
		</div>
		<el-upload
			ref="uploadRef"
			class="upload-dragger"
			drag
			:action="upload.url"
			:headers="upload.headers"
			:data="fileForm"
			:on-progress="handleProgress"
			:on-success="handleSuccess"
			:on-error="handleError"
			:before-upload="beforeUpload"
			:limit="1"
			:file-list="fileList"
			:auto-upload="false"
			:disabled="uploading"
			accept=".zip,application/zip,application/gzip,application/x-gzip,.tar.gz"
		>
			<el-icon class="el-icon--upload"><upload-filled /></el-icon>
			<div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
			<template #tip>
				<div class="el-upload__tip">只能上传 zip/tar.gz 文件</div>
			</template>
		</el-upload>
	</div>
</template>

<script lang="ts" setup name="importExcel">
import { ref } from 'vue';
import { getBaseURL } from '/@/utils/baseUrl';
import { Session } from '/@/utils/storage';
import { successNotification } from '/@/utils/message';
import { ElMessage } from 'element-plus';
import { UploadFilled } from '@element-plus/icons-vue';

const props = defineProps({
	upload: {
		type: Object,
		default() {
			return {
				// 是否显示弹出层
				open: true,
				// 是否禁用上传
				isUploading: false,
				// 设置上传的请求头部
				headers: { Authorization: 'JWT ' + Session.get('token') },
				// 上传的地址
				url: getBaseURL() + 'api/pm/scene/package/upload_model/',
			};
		},
	},
	fileForm: {
		type: Object,
		default() {
			return {
				collection_id: '',
				type: 3,
			};
		},
	},
	autoUpload: {
		type: Boolean,
		default: false,
	},
});

const emit = defineEmits(['successful', 'uploading']);

const fileList = ref([]);
const progress = ref(0);
const progressStatus = ref('');
const uploading = ref(false);
const uploadRef = ref();

// 提交上传方法 - 暴露给父组件调用
const submitUpload = () => {
	uploading.value = true;
	emit('uploading', true);
	uploadRef.value.submit();
	return true;
};

const beforeUpload = (file: File) => {
	const isZip = file.type === 'application/zip' || file.name.endsWith('.zip');
	const isTarGz = file.name.endsWith('.tar.gz');
	if (!isZip && !isTarGz) {
		ElMessage.error('只能上传 zip/tar.gz 文件!');
		return false;
	}
	return true;
};

const handleProgress = (event: any) => {
	progress.value = Math.round((event.loaded / event.total) * 100);
	progressStatus.value = 'success';
};

const handleSuccess = (response: any) => {
	console.log(response);
	if (response.code === 2000) {
		ElMessage.success('上传成功');
		progress.value = 0;
		fileList.value = [];
		uploading.value = false;
		emit('uploading', false);
		emit('successful');
	} else {
		ElMessage.error(response.msg || '上传失败');
		progressStatus.value = 'exception';
		uploading.value = false;
		emit('uploading', false);
	}
};

const handleError = () => {
	ElMessage.error('上传失败');
	progressStatus.value = 'exception';
	uploading.value = false;
	emit('uploading', false);
};

// 暴露方法给父组件
defineExpose({
	submitUpload,
});
</script>

<style scoped>
.upload-container {
	max-width: 600px;
	margin: 0 auto;
}

.upload-header {
	margin-bottom: 20px;
}

.upload-dragger {
	width: 100%;
}

.upload-progress {
	margin-top: 20px;
}

:deep(.el-upload-dragger) {
	width: 100%;
	height: 200px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

:deep(.el-upload__text) {
	margin-top: 16px;
}

:deep(.el-upload__tip) {
	margin-top: 8px;
	color: #909399;
	font-size: 12px;
}
</style>
