<template>
	<el-dialog :close-on-click-modal="false" title="上传情景包" v-model="uploadShow" width="400px" append-to-body>
		<el-upload
			ref="uploadRef"
			:limit="1"
			accept=".zip,application/zip,application/gzip,application/x-gzip,.tar.gz"
			:headers="props.upload.headers"
			:action="props.upload.url"
			:disabled="isUploading"
			:on-progress="handleFileUploadProgress"
			:on-success="handleFileSuccess"
			:auto-upload="false"
			drag
			:data="{
				collection_id: fileForm.id,
			}"
		>
			<i class="el-icon-upload" />
			<div class="el-upload__text">
				将文件拖到此处，或
				<em>点击上传</em>
			</div>
			<template #tip>
				<div class="el-upload__tip" style="color: red">提示：模型包文件,目前仅支持.zip和.tar.gz两种格式！</div>
			</template>
		</el-upload>
		<template #footer>
			<div class="dialog-footer">
				<el-button type="primary" :loading="isUploading" @click="submitFileForm">
					{{ isUploading ? '上传中' : '上 传' }}
				</el-button>
				<el-button :disabled="isUploading" @click="uploadShow = false">取 消</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup name="importExcel">
import { ref } from 'vue';
import { getBaseURL } from '/@/utils/baseUrl';
import { Session } from '/@/utils/storage';
import { successNotification } from '/@/utils/message';
let props = defineProps({
	upload: {
		type: Object,
		default() {
			return {
				// 是否显示弹出层
				open: true,
				// 是否禁用上传
				isUploading: false,
				// 设置上传的请求头部
				headers: { Authorization: 'JWT ' + Session.get('token') },
				// 上传的地址
				url: getBaseURL() + 'api/pm/scene/package/upload_model/',
			};
		},
	},
	fileForm: {
		type: Object,
		default() {
			return {
				id: '',
			};
		},
	},
});

const uploadRef = ref();
const uploadShow = defineModel({ default: false });
const isUploading = ref(false);
const emit = defineEmits(['successful']);
// 文件上传中处理
const handleFileUploadProgress = function (event: any, file: any, fileList: any) {
	isUploading.value = true;
};
// 文件上传成功处理
const handleFileSuccess = function (response: any, file: any, fileList: any) {
	isUploading.value = false;
	uploadShow.value = false;
	successNotification('上传成功！');
	emit('successful');
	uploadRef.value.clearFiles();
};
// 提交上传文件
const submitFileForm = function () {
	uploadRef.value.submit();
};
</script>

<style scoped></style>
