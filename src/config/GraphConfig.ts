/**
 * 电压等级配置文件
 * 集中管理所有电压等级的配置和颜色常量
 */

// 颜色常量定义
export const COLORS = {
	// 基础颜色
	DEFAULT: '#333',
	DEFAULT_LINE: '#1890ff',
	WHITE: 'white',
	BLACK: '#000',

	// 电压等级颜色
	KV_10_BELOW: '#99CCFF', // 浅蓝色: 10kV以下
	KV_10: '#0066CC', // 蓝色: 10kV
	KV_35: '#003366', // 深蓝色: 35kV
	KV_110: '#006600', // 深绿色: 110kV
	KV_220: '#33CC33', // 浅绿色: 220kV
	KV_330: '#FF9900', // 橙色: 330kV
	KV_500: '#FF0000', // 红色: 500kV
	KV_750: '#990000', // 暗红色: 750kV
	KV_800: '#800080', // 紫色: 1000kV
	KV_1000: '#800080', // 紫色: 1000kV

	// 状态颜色
	STATUS_WARNING: 'orange',
	STATUS_OFFLINE: 'gray',
	STATUS_MAINTENANCE: 'purple',

	// 通道类型颜色
	CHANNEL_BACKUP: 'green',
	CHANNEL_EMERGENCY: 'blue',
};

export const ZOOM = 1.2;

// 圆环层间距配置
export const CIRCLE_LAYER_SPACING = 4 * ZOOM;
export const BASE_OUTER_DIAMETER = 26 * ZOOM;

// 电压等级配置表
export const VOLTAGE_CONFIGS: { [key: string]: VoltageConfig } = {
	// 10kV以下 - 1层圆环
	KV_10_BELOW: {
		name: '10kV以下',
		key: '10kV以下',
		color: COLORS.KV_10_BELOW,
		circles: generateCircleConfig(COLORS.KV_10_BELOW, 1),
		lineWidth: 1,
	},

	// 10kV - 1层圆环
	KV_10: {
		name: '10kV',
		key: '10kV',
		color: COLORS.KV_10,
		circles: generateCircleConfig(COLORS.KV_10, 1),
		lineWidth: 1,
	},

	// 35kV - 1层圆环
	KV_35: {
		name: '35kV',
		key: '35',
		color: COLORS.KV_35,
		circles: generateCircleConfig(COLORS.KV_35, 1),
		lineWidth: 1,
	},

	// 110kV - 1层圆环
	KV_110: {
		name: '110kV',
		key: '110',
		color: COLORS.KV_110,
		circles: generateCircleConfig(COLORS.KV_110, 1),
		lineWidth: 2,
	},

	// 220kV - 2层圆环
	KV_220: {
		name: '220kV',
		key: '220',
		color: COLORS.KV_220,
		circles: generateCircleConfig(COLORS.KV_220, 2),
		lineWidth: 2,
	},

	// 330kV - 3层圆环
	KV_330: {
		name: '330kV',
		key: '330',
		color: COLORS.KV_330,
		circles: generateCircleConfig(COLORS.KV_330, 3),
		lineWidth: 3,
	},

	// 500kV - 3层圆环
	KV_500: {
		name: '500kV',
		key: '500',
		color: COLORS.KV_500,
		circles: generateCircleConfig(COLORS.KV_500, 3),
		lineWidth: 3,
	},

	// 750kV - 4层圆环
	KV_750: {
		name: '750kV',
		key: '750',
		color: COLORS.KV_750,
		circles: generateCircleConfig(COLORS.KV_750, 4),
		lineWidth: 4,
	},

	// 800kV - 5层圆环
	KV_800: {
		name: '800kV',
		key: '800',
		color: COLORS.KV_800,
		circles: generateCircleConfig(COLORS.KV_800, 5),
		lineWidth: 4,
	},

	// 1000kV - 5层圆环
	KV_1000: {
		name: '1000kV',
		key: '1000',
		color: COLORS.KV_1000,
		circles: generateCircleConfig(COLORS.KV_1000, 5),
		lineWidth: 4,
	},

	// 默认/未知电压等级 - 1层圆环
	DEFAULT: {
		name: '未知',
		key: '',
		color: COLORS.KV_35,
		circles: generateCircleConfig(COLORS.KV_35, 1),
		lineWidth: 1,
	},
};

/**
 * 确定节点类型
 */
export const determineNodeType = (type: string): 'station' | 'plant' => {
	if (!type) return 'station';
	if (['变电站', '换流站', '开关站', '牵引站'].includes(type)) {
		return 'station';
	} else {
		return 'plant';
	}
};

/**
 * 获取电压等级对应的内层圆环配置
 * @param voltage 电压等级字符串
 * @returns 内层圆环配置数组
 */
export const getInnerCircles = (voltage: string) => {
	const config = getVoltageConfig(voltage);
	return config.circles;
};

/**
 * 生成圆环配置
 * @param color 圆环颜色
 * @param layerCount 圆环层数
 * @returns 圆环配置数组
 */
function generateCircleConfig(color: string, layerCount: number) {
	const circles: any[] = [];

	// 外层圆环
	const outerCircle = {
		diameter: BASE_OUTER_DIAMETER,
		stroke: color,
		strokeWidth: 1,
		isOuter: true,
	};
	circles.push(outerCircle);

	// 内层圆环
	for (let i = 1; i <= layerCount; i++) {
		const diameter = BASE_OUTER_DIAMETER - CIRCLE_LAYER_SPACING * i;
		if (diameter > 0) {
			circles.push({
				diameter: diameter,
				stroke: color,
				strokeWidth: 1,
			});
		}
	}

	return circles;
}

/**
 * 获取电压等级配置
 * @param voltageLevel 电压等级字符串
 * @returns 对应的电压等级配置
 */
export function getVoltageConfig(voltageLevel: string): VoltageConfig {
	if (!voltageLevel) return VOLTAGE_CONFIGS.DEFAULT;
	// 优先进行精确匹配
	for (const key in VOLTAGE_CONFIGS) {
		if (voltageLevel === VOLTAGE_CONFIGS[key].name) {
			return VOLTAGE_CONFIGS[key];
		}
	}

	for (const key in VOLTAGE_CONFIGS) {
		if (voltageLevel.includes(VOLTAGE_CONFIGS[key].key)) {
			return VOLTAGE_CONFIGS[key];
		}
	}

	// 未找到匹配配置，返回默认配置
	return VOLTAGE_CONFIGS.DEFAULT;
}

/**
 * 获取电压等级对应的颜色
 * @param voltageLevel 电压等级
 * @returns 对应的颜色值
 */
export function getVoltageColor(voltageLevel: string): string {
	return getVoltageConfig(voltageLevel)?.color || COLORS.DEFAULT_LINE;
}

// 最大支持的平行线路数
export const MAX_PARALLEL_LINES = 4;

// 线路配置定义
export const PARALLEL_LINE_CONFIG: ParallelLinesConfig = {
	// 单线配置
	1: {
		0: { segmentFraction: 0.5 },
	},
	// 双线配置
	2: {
		0: { segmentFraction: 1 },
		1: { segmentFraction: 1 },
	},
	// 三线配置
	3: {
		0: { segmentFraction: 1 },
		1: { segmentFraction: 0.03 },
		2: { segmentFraction: 1 },
	},
	// 四线配置
	4: {
		0: { segmentFraction: 1 },
		1: { segmentFraction: 1 },
		2: { segmentFraction: 1 },
		3: { segmentFraction: 1 },
	},
};

/**
 * 获取线路上元素位置配置
 * @param lineCount 线路总数
 * @param lineIndex 当前线路索引
 * @returns 位置比例
 */
export function getLineSegmentFraction(lineCount: number, lineIndex: number): number {
	if (PARALLEL_LINE_CONFIG[lineCount] && PARALLEL_LINE_CONFIG[lineCount][lineIndex]) {
		return PARALLEL_LINE_CONFIG[lineCount][lineIndex].segmentFraction;
	}
	return 0.5;
}

import { h } from 'vue';
import { ElDivider } from 'element-plus';
export const spacer = h(ElDivider, { direction: 'vertical' });
