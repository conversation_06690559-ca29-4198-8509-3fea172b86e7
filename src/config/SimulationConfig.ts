import GeneralPopup from '/@/views/components/ag-grid/GeneralPopup.vue';

export const scenarioPackAllDate = [
	{
		name: '基础信息',
		children: [
			{ name: '区域', table_key: 'bb_control_area' },
			{ name: '电压等级', table_key: 'bb_basevoltage' },
		],
	},
	{
		name: '厂站',
		children: [
			{ name: '厂站', table_key: 'bb_substation' },
			{ name: '母线', table_key: 'bb_bus' },
			{ name: '两绕变', table_key: 'bb_trafo' },
			{ name: '三绕变', table_key: 'bb_trafo3w' },
			{ name: '负荷', table_key: 'bb_load' },
			{ name: '开关', table_key: 'bb_switch' },
		],
	},
	{
		name: '断面',
		children: [
			{ name: '断面', table_key: 'bb_section_basic' },
			{ name: '断面设备', table_key: 'bb_section_device' },
			{ name: '断面参数', table_key: 'bb_section_param' },
			{ name: '断面灵敏度', table_key: 'bb_section_sens' },
		],
	},
	{
		name: '线路',
		children: [
			{ name: '线路', table_key: 'bb_line' },
			{ name: '线路正负向限额', table_key: 'bb_line_limit' },
			{ name: '转移因子', table_key: 'bb_node_sensitivity' },
		],
	},
	{
		name: '机组',
		children: [
			{ name: '机组', table_key: 'bb_sgen' },
			{ name: '机组参数', table_key: 'bb_unit_param' },
			{ name: '机组初始状态', table_key: 'bb_unit_initial_state' },
			{ name: '机组启动报价', table_key: 'bb_unit_offer_start' },
			{ name: '机组功率范围', table_key: 'bb_unit_power_range' },
			{ name: '机组爬坡能力', table_key: 'bb_unit_ramp_capability' },
		],
	},
	{
		name: '用户',
		children: [
			{ name: '用户信息', table_key: 'bb_user_basic' },
			{ name: '用户分时段分配因子', table_key: 'bb_user_node_factor' },
		],
	},
	{
		name: '负荷',
		children: [
			{ name: '母线负荷', table_key: 'bb_elc_bus_basic' },
			{ name: '母线负荷预测', table_key: 'bb_bus_load_forecast' },
			{ name: '系统负荷', table_key: 'bb_system_load' },
		],
	},
];

export const powerFlowCalculation = [
	{ name: '母线', table_key: 'bb_bus' },
	{ name: '线路', table_key: 'bb_line' },
	{ name: '负荷', table_key: 'bb_load' },
	{ name: '发电机', table_key: 'bb_sgen' },
	{ name: '并联容抗', table_key: 'bb_shunt' },
	{ name: '两绕变', table_key: 'bb_trafo' },
	{ name: '三绕变', table_key: 'bb_trafo3w' },
	{ name: '开关', table_key: 'bb_switch' },
];

import { getDictConfig } from '/@/config/ag-grid';

export const dataApiConfig = {
	bb_control_area: {
		list: '/api/pm/bb_control_area/query_list/',
		update: '/api/pm/bb_control_area/batch_create_update/',
		add: '/api/pm/bb_control_area/batch_create_update/',
		delete: '/api/pm/bb_control_area/multiple_delete/',
	},
	bb_basevoltage: {
		list: '/api/pm/bb_basevoltage/query_list/',
		update: '/api/pm/bb_basevoltage/batch_create_update/',
		add: '/api/pm/bb_basevoltage/batch_create_update/',
		delete: '/api/pm/bb_basevoltage/multiple_delete/',
	},
	bb_substation: {
		list: '/api/pm/bb_substation/query_list/',
		update: '/api/pm/bb_substation/batch_create_update/',
		add: '/api/pm/bb_substation/batch_create_update/',
		delete: '/api/pm/bb_substation/multiple_delete/',
	},
	bb_bus: {
		list: '/api/pm/bb_bus/query_list/',
		update: '/api/pm/bb_bus/batch_create_update/',
		add: '/api/pm/bb_bus/batch_create_update/',
		delete: '/api/pm/bb_bus/multiple_delete/',
	},
	bb_line: {
		list: '/api/pm/bb_line/query_list/',
		update: '/api/pm/bb_line/batch_create_update/',
		add: '/api/pm/bb_line/batch_create_update/',
		delete: '/api/pm/bb_line/multiple_delete/',
	},
	bb_trafo: {
		list: '/api/pm/bb_trafo/query_list/',
		update: '/api/pm/bb_trafo/batch_create_update/',
		add: '/api/pm/bb_trafo/batch_create_update/',
		delete: '/api/pm/bb_trafo/multiple_delete/',
	},
	bb_trafo3w: {
		list: '/api/pm/bb_trafo3w/query_list/',
		update: '/api/pm/bb_trafo3w/batch_create_update/',
		add: '/api/pm/bb_trafo3w/batch_create_update/',
		delete: '/api/pm/bb_trafo3w/multiple_delete/',
	},
	bb_load: {
		list: '/api/pm/bb_load/query_list/',
		update: '/api/pm/bb_load/batch_create_update/',
		add: '/api/pm/bb_load/batch_create_update/',
		delete: '/api/pm/bb_load/multiple_delete/',
	},
	bb_sgen: {
		list: '/api/pm/bb_sgen/query_list/',
		update: '/api/pm/bb_sgen/batch_create_update/',
		add: '/api/pm/bb_sgen/batch_create_update/',
		delete: '/api/pm/bb_sgen/multiple_delete/',
	},
	bb_shunt: {
		list: '/api/pm/bb_shunt/query_list/',
		update: '/api/pm/bb_shunt/batch_create_update/',
		add: '/api/pm/bb_shunt/batch_create_update/',
		delete: '/api/pm/bb_shunt/multiple_delete/',
	},
	bb_switch: {
		list: '/api/pm/bb_switch/query_list/',
		update: '/api/pm/bb_switch/batch_create_update/',
		add: '/api/pm/bb_switch/batch_create_update/',
		delete: '/api/pm/bb_switch/multiple_delete/',
	},
	bb_section_basic: {
		list: '/api/pm/bb_section_basic/query_list/',
		update: '/api/pm/bb_section_basic/batch_create_update/',
		add: '/api/pm/bb_section_basic/batch_create_update/',
		delete: '/api/pm/bb_section_basic/multiple_delete/',
	},
	bb_section_device: {
		list: '/api/pm/bb_section_device/query_list/',
		update: '/api/pm/bb_section_device/batch_create_update/',
		add: '/api/pm/bb_section_device/batch_create_update/',
		delete: '/api/pm/bb_section_device/multiple_delete/',
	},
	bb_section_param: {
		list: '/api/pm/bb_section_param/query_list/',
		update: '/api/pm/bb_section_param/batch_create_update/',
		add: '/api/pm/bb_section_param/batch_create_update/',
		delete: '/api/pm/bb_section_param/multiple_delete/',
	},
	bb_section_sens: {
		list: '/api/pm/bb_section_sens/query_list/',
		update: '/api/pm/bb_section_sens/batch_create_update/',
		add: '/api/pm/bb_section_sens/batch_create_update/',
		delete: '/api/pm/bb_section_sens/multiple_delete/',
	},
	bb_unit_param: {
		list: '/api/pm/bb_unit_param/query_list/',
		update: '/api/pm/bb_unit_param/batch_create_update/',
		add: '/api/pm/bb_unit_param/batch_create_update/',
		delete: '/api/pm/bb_unit_param/multiple_delete/',
	},
	bb_unit_connect_bus: {
		list: '/api/pm/bb_unit_connect_bus/query_list/',
		update: '/api/pm/bb_unit_connect_bus/batch_create_update/',
		add: '/api/pm/bb_unit_connect_bus/batch_create_update/',
		delete: '/api/pm/bb_unit_connect_bus/multiple_delete/',
	},
	bb_unit_initial_state: {
		list: '/api/pm/bb_unit_initial_state/query_list/',
		update: '/api/pm/bb_unit_initial_state/batch_create_update/',
		add: '/api/pm/bb_unit_initial_state/batch_create_update/',
		delete: '/api/pm/bb_unit_initial_state/multiple_delete/',
	},
	bb_unit_offer_start: {
		list: '/api/pm/bb_unit_offer_start/query_list/',
		update: '/api/pm/bb_unit_offer_start/batch_create_update/',
		add: '/api/pm/bb_unit_offer_start/batch_create_update/',
		delete: '/api/pm/bb_unit_offer_start/multiple_delete/',
	},
	bb_unit_power_range: {
		list: '/api/pm/bb_unit_power_range/query_list/',
		update: '/api/pm/bb_unit_power_range/batch_create_update/',
		add: '/api/pm/bb_unit_power_range/batch_create_update/',
		delete: '/api/pm/bb_unit_power_range/multiple_delete/',
	},
	bb_unit_ramp_capability: {
		list: '/api/pm/bb_unit_ramp_capability/query_list/',
		update: '/api/pm/bb_unit_ramp_capability/batch_create_update/',
		add: '/api/pm/bb_unit_ramp_capability/batch_create_update/',
		delete: '/api/pm/bb_unit_ramp_capability/multiple_delete/',
	},
	bb_unit_shutdown_curve: {
		list: '/api/pm/bb_unit_shutdown_curve/query_list/',
		update: '/api/pm/bb_unit_shutdown_curve/batch_create_update/',
		add: '/api/pm/bb_unit_shutdown_curve/batch_create_update/',
		delete: '/api/pm/bb_unit_shutdown_curve/multiple_delete/',
	},
	bb_user_basic: {
		list: '/api/pm/bb_user_basic/query_list/',
		update: '/api/pm/bb_user_basic/batch_create_update/',
		add: '/api/pm/bb_user_basic/batch_create_update/',
		delete: '/api/pm/bb_user_basic/multiple_delete/',
	},
	bb_user_node_factor: {
		list: '/api/pm/bb_user_node_factor/query_list/',
		update: '/api/pm/bb_user_node_factor/batch_create_update/',
		add: '/api/pm/bb_user_node_factor/batch_create_update/',
		delete: '/api/pm/bb_user_node_factor/multiple_delete/',
	},
	bb_elc_bus_basic: {
		list: '/api/pm/bb_elc_bus_basic/query_list/',
		update: '/api/pm/bb_elc_bus_basic/batch_create_update/',
		add: '/api/pm/bb_elc_bus_basic/batch_create_update/',
		delete: '/api/pm/bb_elc_bus_basic/multiple_delete/',
	},
	bb_bus_load_forecast: {
		list: '/api/pm/bb_bus_load_forecast/query_list/',
		update: '/api/pm/bb_bus_load_forecast/batch_create_update/',
		add: '/api/pm/bb_bus_load_forecast/batch_create_update/',
		delete: '/api/pm/bb_bus_load_forecast/multiple_delete/',
	},
	bb_line_limit: {
		list: '/api/pm/bb_line_limit/query_list/',
		update: '/api/pm/bb_line_limit/batch_create_update/',
		add: '/api/pm/bb_line_limit/batch_create_update/',
		delete: '/api/pm/bb_line_limit/multiple_delete/',
	},
	bb_node_sensitivity: {
		list: '/api/pm/bb_node_sensitivity/query_list/',
		update: '/api/pm/bb_node_sensitivity/batch_create_update/',
		add: '/api/pm/bb_node_sensitivity/batch_create_update/',
		delete: '/api/pm/bb_node_sensitivity/multiple_delete/',
	},
	bb_system_load: {
		list: '/api/pm/bb_system_load/query_list/',
		update: '/api/pm/bb_system_load/batch_create_update/',
		add: '/api/pm/bb_system_load/batch_create_update/',
		delete: '/api/pm/bb_system_load/multiple_delete/',
	},
};

export const dictConfig: Recordable = {
	SetState: {
		'22': '发电',
		'21': '待机',
	},
	Type: {
		'1': '母线',
		'2': '节点',
	},
	InitialState: {
		'11': '启用',
		'10': '停用',
	},
	isBool: {
		'1': '是',
		'0': '否',
	},
	BusType: {
		b: 'bus',
		n: 'node',
	},
	SubstationType: {
		火电站: '火电站',
		水电站: '水电站',
		变电站: '变电站',
		换流站: '换流站',
		抽水蓄能站: '抽水蓄能站',
	},
	SwitchElementType: {
		b: '母线',
		l: '线路',
		t: '变压器',
	},
	SwitchStatus: {
		0: '断开',
		1: '闭合',
	},
	SectionType: {
		0: '全网',
		1: '500kV',
		2: '分区',
	},
	TapSide2: {
		hv: '高压侧',
		lv: '低压侧',
	},
	TapSide3: {
		hv: '高压侧',
		lv: '低压侧',
		mv: '中压侧',
	},
	GenNodeType: {
		0: 'slack',
		1: 'pq',
		2: 'pv',
	},
};

export const configColumns = {
	bb_control_area: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '区域ID',
			field: 'control_area_id',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: '区域名称',
			field: 'control_area_name',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: '上级区域',
			field: 'control_area_parent',
			filter: 'agTextColumnFilter',
			editable: true,
			cellRenderer: GeneralPopup,
			cellRendererParams: {
				table_key: 'bb_control_area',
				valueFields: {
					control_area_parent: 'control_area_id',
				},
			},
		},
		{
			headerName: '是否有效',
			field: 'in_service',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'),
		},
		{
			headerName: '模型ID',
			field: 'bb_case_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
	],
	bb_basevoltage: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '电压ID',
			field: 'mRID',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: '电压名称',
			field: 'name',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: '基准电压',
			field: 'nomkV',
			filter: 'agNumberColumnFilter',
			editable: true,
			cellEditor: 'agNumberCellEditor',
		},
		{
			headerName: '模型ID',
			field: 'bb_case_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
	],
	bb_substation: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '厂站ID',
			field: 'mRID',
			editable: true,
			hide: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '厂站名称',
			field: 'name',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '全路径名称',
			field: 'path_name',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '厂站类型',
			field: 'type',
			editable: true,
			...getDictConfig(dictConfig, 'SubstationType'),
		},
		{
			headerName: '所属区域ID',
			field: 'zone',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '区域名称',
			field: 'zone_name',
			editable: true,
			filter: 'agTextColumnFilter',
			cellRenderer: GeneralPopup,
			cellRendererParams: {
				table_key: 'bb_control_area',
				valueFields: {
					zone: 'control_area_id',
					zone_name: 'control_area_name',
				},
			},
		},
		{
			headerName: '电压等级',
			field: 'basevoltage',
			filter: 'agNumberColumnFilter',
			hide: true,
		},
		{
			headerName: '电压等级名称',
			field: 'basevoltage_name',
			editable: true,
			filter: 'agTextColumnFilter',
			cellRenderer: GeneralPopup,
			cellRendererParams: {
				table_key: 'bb_basevoltage',
				valueFields: {
					basevoltage: 'mRID',
					basevoltage_name: 'name',
				},
			},
		},
		{
			headerName: '总有功',
			field: 'P',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '总无功',
			field: 'Q',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '经度',
			field: 'x',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '纬度',
			field: 'y',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: 'CIME模型ID',
			field: 'cime_model_id',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
	],
	bb_bus: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '计算节点ID',
			field: 'id',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '母线',
			field: 'bus_id',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '母线名称',
			field: 'bus_name',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '母线所属厂站',
			field: 'substation',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '母线所属厂站名称',
			field: 'substation_name',
			editable: true,
			filter: 'agTextColumnFilter',
			cellRenderer: GeneralPopup,
			cellRendererParams: {
				table_key: 'bb_substation',
				valueFields: {
					substation: 'mRID',
					substation_name: 'name',
					substation_basevoltage_id: 'basevoltage',
					substation_basevoltage_name: 'basevoltage_name',
					zone: 'zone',
					zone_name: 'zone_name',
				},
			},
		},
		{
			headerName: '厂站电压等级',
			field: 'substation_basevoltage_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '厂站电压的等级名称',
			field: 'substation_basevoltage_name',
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '所属厂站类型',
			field: 'sub_type',
			editable: true,
			...getDictConfig(dictConfig, 'SubstationType'),
		},
		{
			headerName: '电压等级ID',
			field: 'basevoltage',
			filter: 'agNumberColumnFilter',
			hide: true,
		},
		{
			headerName: '电压等级名称',
			field: 'basevoltage_name',
			filter: 'agTextColumnFilter',
			cellRenderer: GeneralPopup,
			cellRendererParams: {
				table_key: 'bb_basevoltage',
				valueFields: {
					basevoltage: 'mRID',
					basevoltage_name: 'name',
				},
			},
		},
		{
			headerName: '母线基准电压',
			field: 'vn_kv',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '母线电压(量测)',
			field: 'dt_vn_kv',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '母线类型',
			field: 'type',
			editable: true,
			...getDictConfig(dictConfig, 'BusType'), // B/N类型字典
		},
		{
			headerName: '所属区域',
			field: 'zone',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '所属区域名称',
			field: 'zone_name',
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '是否有效',
			field: 'in_service',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'),
		},
		{
			headerName: '潮流计算作业ID',
			field: 'bb_case_id',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '母线电压(状态估计)',
			field: 'qs_vn_kv',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
	],
	bb_line: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '线路ID',
			field: 'line_id',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '线路名称',
			field: 'line_name',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '线路型号',
			field: 'std_type',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '起始母线节点',
			field: 'from_bus',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '终止母线节点',
			field: 'to_bus',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '长度',
			field: 'length_km',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '电阻/km',
			field: 'r_ohm_per_km',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '电抗/km',
			field: 'x_ohm_per_km',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '电容/km',
			field: 'c_nf_per_km',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '电导/km',
			field: 'g_us_per_km',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '最大电流',
			field: 'max_i_ka',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: 'df',
			field: 'df',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: 'parallel',
			field: 'parallel',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '类型',
			field: 'type',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '是否可用',
			field: 'in_service',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'),
		},
		{
			headerName: '零序电阻/km',
			field: 'r0_ohm_per_km',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '零序电抗/km',
			field: 'x0_ohm_per_km',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '零序电容/km',
			field: 'c0_nf_per_km',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '零序电导/km',
			field: 'g0_us_per_km',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: 'I侧量测有功',
			field: 'dt_p_from_mw',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: 'I侧量测无功',
			field: 'dt_q_from_mvar',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: 'J侧量测有功',
			field: 'dt_p_to_mw',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: 'J侧量测无功',
			field: 'dt_q_to_mvar',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '潮流计算作业ID',
			field: 'bb_case_id',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '首端厂站ID',
			field: 'from_substation_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '首端厂站名称',
			field: 'from_substation_name',
			editable: true,
			filter: 'agTextColumnFilter',
			cellRenderer: GeneralPopup,
			cellRendererParams: {
				table_key: 'bb_substation',
				valueFields: {
					from_substation_id: 'mRID',
					from_substation_name: 'name',
					from_zone_id: 'zone',
					from_zone_name: 'zone_name',
				},
			},
		},
		{
			headerName: '末端厂站ID',
			field: 'to_substation_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '末端厂站名称',
			field: 'to_substation_name',
			editable: true,
			filter: 'agTextColumnFilter',
			cellRenderer: GeneralPopup,
			cellRendererParams: {
				table_key: 'bb_substation',
				valueFields: {
					to_substation_id: 'mRID',
					to_substation_name: 'name',
					to_zone_id: 'zone',
					to_zone_name: 'zone_name',
				},
			},
		},
		{
			headerName: '电压等级ID',
			field: 'basevoltage_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '电压等级名称',
			field: 'basevoltage_name',
			editable: true,
			filter: 'agTextColumnFilter',
			cellRenderer: GeneralPopup,
			cellRendererParams: {
				table_key: 'bb_basevoltage',
				valueFields: {
					basevoltage_id: 'mRID',
					basevoltage_name: 'name',
				},
			},
		},
		{
			headerName: '首端区域ID',
			field: 'from_zone_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '首端区域名称',
			field: 'from_zone_name',
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '末端区域ID',
			field: 'to_zone_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '末端区域名称',
			field: 'to_zone_name',
			filter: 'agTextColumnFilter',
		},
	],
	bb_trafo: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '主变ID',
			field: 'trafo_id',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '主变名称',
			field: 'trafo_name',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '主变型号',
			field: 'std_type',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '高压侧节点',
			field: 'hv_bus',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '低压侧节点',
			field: 'lv_bus',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '容量(MVA)',
			field: 'sn_mva',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '高压侧额定电压(kV)',
			field: 'vn_hv_kv',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '低压侧额定电压(kV)',
			field: 'vn_lv_kv',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '短路电压(%)',
			field: 'vk_percent',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '短路电压实部(%)',
			field: 'vkr_percent',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '空载损耗(kW)',
			field: 'pfe_kw',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '空载电流(%)',
			field: 'i0_percent',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '相移角度(度)',
			field: 'shift_degree',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '抽头侧',
			field: 'tap_side',
			editable: true,
			...getDictConfig(dictConfig, 'TapSide2'),
		},
		{
			headerName: '抽头额定位置',
			field: 'tap_neutral',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '抽头最小值',
			field: 'tap_min',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '抽头最大值',
			field: 'tap_max',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '抽头档位比例(%)',
			field: 'tap_step_percent',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '抽头档位角度(度)',
			field: 'tap_step_degree',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '抽头位置',
			field: 'tap_pos',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '相位调节器',
			field: 'tap_phase_shifter',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '并联数',
			field: 'parallel',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '分散系数',
			field: 'df',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '是否可用',
			field: 'in_service',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'),
		},
		{
			headerName: '抽头位置（双精度）',
			field: 'tp_pos',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '潮流计算作业ID',
			field: 'bb_case_id',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '高压侧有功(MW)',
			field: 'dt_p_hv_mw',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '高压侧无功(MVar)',
			field: 'dt_q_hv_mvar',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '低压侧有功(MW)',
			field: 'dt_p_lv_mw',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '低压侧无功(MVar)',
			field: 'dt_q_lv_mvar',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '所属厂站ID',
			field: 'substation_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '所属厂站名称',
			field: 'substation_name',
			editable: true,
			filter: 'agTextColumnFilter',
			cellRenderer: GeneralPopup,
			cellRendererParams: {
				table_key: 'bb_substation',
				valueFields: {
					substation_id: 'mRID',
					substation_name: 'name',
					substation_basevoltage_id: 'basevoltage',
					substation_basevoltage_name: 'basevoltage_name',
					zone_id: 'zone',
					zone_name: 'zone_name',
				},
			},
		},
		{
			headerName: '电压等级ID',
			field: 'basevoltage_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '电压等级名称',
			field: 'basevoltage_name',
			editable: true,
			filter: 'agTextColumnFilter',
			cellRenderer: GeneralPopup,
			cellRendererParams: {
				table_key: 'bb_basevoltage',
				valueFields: {
					basevoltage_id: 'mRID',
					basevoltage_name: 'name',
				},
			},
		},
		{
			headerName: '所属区域ID',
			field: 'zone_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '所属区域名称',
			field: 'zone_name',
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '电磁环网ID',
			field: 'elec_loop_id',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '电磁环网名称',
			field: 'elec_loop_name',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '厂站电压等级ID',
			field: 'substation_basevoltage_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '厂站电压等级名称',
			field: 'substation_basevoltage_name',
			filter: 'agTextColumnFilter',
		},
	],
	bb_trafo3w: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '三绕组变压器ID',
			field: 'trafo3w_id',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '主变名称',
			field: 'trafo3w_name',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '主变型号',
			field: 'std_type',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '高压侧节点',
			field: 'hv_bus',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '中压侧节点',
			field: 'mv_bus',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '低压侧节点',
			field: 'lv_bus',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '高压侧容量(MVA)',
			field: 'sn_hv_mva',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '中压侧容量(MVA)',
			field: 'sn_mv_mva',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '低压侧容量(MVA)',
			field: 'sn_lv_mva',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '高压侧额定电压(kV)',
			field: 'vn_hv_kv',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '中压侧额定电压(kV)',
			field: 'vn_mv_kv',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '低压侧额定电压(kV)',
			field: 'vn_lv_kv',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '高-中短路电压(%)',
			field: 'vk_hv_percent',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '中-低短路电压(%)',
			field: 'vk_mv_percent',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '高-低短路电压(%)',
			field: 'vk_lv_percent',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '高-中短路实部(%)',
			field: 'vkr_hv_percent',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '中-低短路实部(%)',
			field: 'vkr_mv_percent',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '高-低短路实部(%)',
			field: 'vkr_lv_percent',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '铁损(kW)',
			field: 'pfe_kw',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '空载电流(%)',
			field: 'i0_percent',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '中压侧相移角度(度)',
			field: 'shift_mv_degree',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '低压侧相移角度(度)',
			field: 'shift_lv_degree',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '抽头侧',
			field: 'tap_side',
			editable: true,
			...getDictConfig(dictConfig, 'TapSide3'),
		},
		{
			headerName: '抽头额定位置',
			field: 'tap_neutral',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '抽头最小档',
			field: 'tap_min',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '抽头最大档',
			field: 'tap_max',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '抽头档位比例(%)',
			field: 'tap_step_percent',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '抽头档位角度(度)',
			field: 'tap_step_degree',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '抽头位置',
			field: 'tap_pos',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '星点抽头',
			field: 'tap_at_star_point',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '是否有效',
			field: 'in_service',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'), // 布尔字典（是/否）
		},
		{
			headerName: '高压零序短路电压(%)',
			field: 'vk0_hv_percent',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '中压零序短路电压(%)',
			field: 'vk0_mv_percent',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '低压零序短路电压(%)',
			field: 'vk0_lv_percent',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '高压零序短路实部(%)',
			field: 'vkr0_hv_percent',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '中压零序短路实部(%)',
			field: 'vkr0_mv_percent',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '低压零序短路实部(%)',
			field: 'vkr0_lv_percent',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '向量组',
			field: 'vector_group',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '潮流计算作业ID',
			field: 'bb_case_id',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '高压侧有功(MW)',
			field: 'dt_p_hv_mw',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '高压侧无功(MVar)',
			field: 'dt_q_hv_mvar',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '中压侧有功(MW)',
			field: 'dt_p_mv_mw',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '中压侧无功(MVar)',
			field: 'dt_q_mv_mvar',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '低压侧有功(MW)',
			field: 'dt_p_lv_mw',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '低压侧无功(MVar)',
			field: 'dt_q_lv_mvar',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '所属厂站ID',
			field: 'substation_id',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '所属厂站名称',
			field: 'substation_name',
			editable: true,
			filter: 'agTextColumnFilter',
			cellRenderer: GeneralPopup,
			cellRendererParams: {
				table_key: 'bb_substation',
				valueFields: {
					substation_id: 'mRID',
					substation_name: 'name',
					zone_id: 'zone',
					zone_name: 'zone_name',
					substation_basevoltage_id: 'basevoltage',
					substation_basevoltage_name: 'basevoltage_name',
				},
			},
		},
		{
			headerName: '电压等级ID',
			field: 'basevoltage_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '电压等级名称',
			field: 'basevoltage_name',
			editable: true,
			filter: 'agTextColumnFilter',
			cellRenderer: GeneralPopup,
			cellRendererParams: {
				table_key: 'bb_basevoltage',
				valueFields: {
					basevoltage_id: 'mRID',
					basevoltage_name: 'name',
				},
			},
		},
		{
			headerName: '所属区域ID',
			field: 'zone_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '所属区域名称',
			field: 'zone_name',
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '电磁环网ID',
			field: 'elec_loop_id',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '电磁环网名称',
			field: 'elec_loop_name',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '厂站电压等级ID',
			field: 'substation_basevoltage_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '厂站电压等级名称',
			field: 'substation_basevoltage_name',
			filter: 'agTextColumnFilter',
		},
	],
	bb_load: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '负荷ID',
			field: 'load_id',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '负荷名称',
			field: 'load_name',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '所在节点',
			field: 'bus',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '有功功率(MW)',
			field: 'p_mw',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '无功功率(MVar)',
			field: 'q_mvar',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '恒定阻抗百分比(%)',
			field: 'const_z_percent',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '恒定电流百分比(%)',
			field: 'const_i_percent',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '容量(MVA)',
			field: 'sn_mva',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '功率因数',
			field: 'scaling',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '是否可用',
			field: 'in_service',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'),
		},
		{
			headerName: '负荷类型',
			field: 'type',
			editable: true,
			// ...getDictConfig(dictConfig, 'LoadType'),
		},
		{
			headerName: '潮流计算作业ID',
			field: 'bb_case_id',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '所属厂站ID',
			field: 'substation_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '所属厂站名称',
			field: 'substation_name',
			editable: true,
			filter: 'agTextColumnFilter',
			cellRenderer: GeneralPopup,
			cellRendererParams: {
				table_key: 'bb_substation',
				valueFields: {
					substation_id: 'mRID',
					substation_name: 'name',
					substation_basevoltage_id: 'basevoltage',
					substation_basevoltage_name: 'basevoltage_name',
					zone_id: 'zone',
					zone_name: 'zone_name',
				},
			},
		},
		{
			headerName: '电压等级ID',
			field: 'basevoltage_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '电压等级名称',
			field: 'basevoltage_name',
			editable: true,
			filter: 'agTextColumnFilter',
			cellRenderer: GeneralPopup,
			cellRendererParams: {
				table_key: 'bb_basevoltage',
				valueFields: {
					basevoltage_id: 'mRID',
					basevoltage_name: 'name',
				},
			},
		},
		{
			headerName: '所属区域ID',
			field: 'zone_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '所属区域名称',
			field: 'zone_name',
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '厂站电压等级ID',
			field: 'substation_basevoltage_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '厂站电压等级名称',
			field: 'substation_basevoltage_name',
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '固定不拉升',
			field: 'fixed',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'), // 将 int2 转为布尔值
		},
	],
	bb_shunt: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '并联容抗ID',
			field: 'shunt_id',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '并联容抗名称',
			field: 'shunt_name',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '所在母线节点',
			field: 'bus',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '有功(MW)',
			field: 'p_mw',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '无功(MVar)',
			field: 'q_mvar',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '额定电压(kV)',
			field: 'vn_kv',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '调节挡位置',
			field: 'step',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '调节点最大位置',
			field: 'max_step',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '是否可用',
			field: 'in_service',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'), // 布尔字典（0/1 → 否/是）
		},
		{
			headerName: '潮流计算作业ID',
			field: 'bb_case_id',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '所属厂站ID',
			field: 'substation_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '所属厂站名称',
			field: 'substation_name',
			editable: true,
			filter: 'agTextColumnFilter',
			cellRenderer: GeneralPopup,
			cellRendererParams: {
				table_key: 'bb_substation',
				valueFields: {
					substation_id: 'mRID',
					substation_name: 'name',
					substation_basevoltage_id: 'basevoltage',
					substation_basevoltage_name: 'basevoltage_name',
					zone_id: 'zone',
					zone_name: 'zone_name',
				},
			},
		},
		{
			headerName: '电压等级ID',
			field: 'basevoltage_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '电压等级名称',
			field: 'basevoltage_name',
			editable: true,
			filter: 'agTextColumnFilter',
			cellRenderer: GeneralPopup,
			cellRendererParams: {
				table_key: 'bb_basevoltage',
				valueFields: {
					basevoltage_id: 'mRID',
					basevoltage_name: 'name',
				},
			},
		},
		{
			headerName: '所属区域ID',
			field: 'zone_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '所属区域名称',
			field: 'zone_name',
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '厂站电压等级ID',
			field: 'substation_basevoltage_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '厂站电压等级名称',
			field: 'substation_basevoltage_name',
			filter: 'agTextColumnFilter',
		},
	],
	bb_switch: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '开关ID',
			field: 'switch_id',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '开关名称',
			field: 'switch_name',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '所连母线节点',
			field: 'bus',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '连接设备ID',
			field: 'element_id',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '设备类型',
			field: 'et',
			editable: true,
			// ...getDictConfig(dictConfig, 'SwitchElementType'), // 字典：b-母线，l-线路，t-变压器
		},
		{
			headerName: '开关状态',
			field: 'closed',
			editable: true,
			// ...getDictConfig(dictConfig, 'SwitchStatus'), // 字典：0-断开，1-闭合
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '最大电流(kA)',
			field: 'in_ka',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '潮流计算作业ID',
			field: 'bb_case_id',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '所属厂站ID',
			field: 'substation_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '所属厂站名称',
			field: 'substation_name',
			editable: true,
			filter: 'agTextColumnFilter',
			cellRenderer: GeneralPopup,
			cellRendererParams: {
				table_key: 'bb_substation',
				valueFields: {
					substation_id: 'mRID',
					substation_name: 'name',
					substation_basevoltage_id: 'basevoltage',
					substation_basevoltage_name: 'basevoltage_name',
					zone_id: 'zone',
					zone_name: 'zone_name',
				},
			},
		},
		{
			headerName: '电压等级ID',
			field: 'basevoltage_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '电压等级名称',
			field: 'basevoltage_name',
			editable: true,
			filter: 'agTextColumnFilter',
			cellRenderer: GeneralPopup,
			cellRendererParams: {
				table_key: 'bb_basevoltage',
				valueFields: {
					basevoltage_id: 'mRID',
					basevoltage_name: 'name',
				},
			},
		},
		{
			headerName: '所属区域ID',
			field: 'zone_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '所属区域名称',
			field: 'zone_name',
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '厂站电压等级ID',
			field: 'substation_basevoltage_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '厂站电压等级名称',
			field: 'substation_basevoltage_name',
			filter: 'agTextColumnFilter',
		},
	],
	bb_section_basic: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '断面ID',
			field: 'section_id',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '断面名称',
			field: 'section_name',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '断面类型',
			field: 'section_type',
			editable: true,
			...getDictConfig(dictConfig, 'SectionType'), // 字典：0-全网，1-500kV，2-分区断面
		},
	],
	bb_section_device: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '断面ID',
			field: 'section_id',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '设备ID',
			field: 'device_id',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '设备类型',
			field: 'device_type',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '功率比',
			field: 'dev_power_ratio',
			editable: true,
			filter: 'agNumberColumnFilter',
			cellEditor: 'agNumberCellEditor',
		},
	],
	bb_section_param: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '断面ID',
			field: 'section_id',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '是否考虑断面极限约束',
			field: 'if_con_trans_limit',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'),
		},
		{
			headerName: '允许断面极限偏差比例',
			field: 'trans_limit_error',
			editable: true,
			filter: 'agNumberColumnFilter',
			cellEditor: 'agNumberCellEditor',
		},
		{
			headerName: '潮流控制百分比',
			field: 'limit_pct',
			editable: true,
			filter: 'agNumberColumnFilter',
			cellEditor: 'agNumberCellEditor',
		},
		{
			headerName: '风险等级',
			field: 'risk_level',
			editable: true,
			filter: 'agNumberColumnFilter',
			cellEditor: 'agNumberCellEditor',
		},
		{
			headerName: '是否考虑备用减扣',
			field: 'if_con_rsv_limit',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'),
		},
		{
			headerName: '备用减扣相应机组灵敏度阀值',
			field: 'rsv_lmt_min_sens',
			editable: true,
			filter: 'agNumberColumnFilter',
			cellEditor: 'agNumberCellEditor',
		},
	],
	bb_section_sens: [
		{
			headerName: '断面ID',
			field: 'section_id',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '拓扑节点ID',
			field: 'node_id',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '时段',
			field: 'period_id',
			editable: true,
			filter: 'agNumberColumnFilter',
			cellEditor: 'agNumberCellEditor',
		},
		{
			headerName: '灵敏度',
			field: 'sens',
			editable: true,
			filter: 'agNumberColumnFilter',
			cellEditor: 'agNumberCellEditor',
		},
	],
	bb_sgen: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '电源ID',
			field: 'sgen_id',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '电源名称',
			field: 'sgen_name',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '所在母线节点',
			field: 'bus',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '有功功率(MW)',
			field: 'p_mw',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '无功功率(MVar)',
			field: 'q_mvar',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '容量(MVA)',
			field: 'sn_mva',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '额定电压(kV)',
			field: 'vn_kv',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '最小有功(MW)',
			field: 'min_p_mw',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '最大有功(MW)',
			field: 'max_p_mw',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '最小无功(MVar)',
			field: 'min_q_mw',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '最大无功(MVar)',
			field: 'max_q_mw',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '机端电压(pu)',
			field: 'vm_pu',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '功率因素',
			field: 'scaling',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '是否可用',
			field: 'in_service',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'),
		},
		{
			headerName: '电源类型',
			field: 'type',
			editable: true,
			// ...getDictConfig(dictConfig, 'GenType'), // 发电机类型字典
		},
		{
			headerName: '潮流计算作业ID',
			field: 'bb_case_id',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '发电机节点类型',
			field: 'mode_ctrl',
			editable: true,
			...getDictConfig(dictConfig, 'GenNodeType'),
		},
		{
			headerName: '所属厂站ID',
			field: 'substation_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '所属厂站名称',
			field: 'substation_name',
			editable: true,
			filter: 'agTextColumnFilter',
			cellRenderer: GeneralPopup,
			cellRendererParams: {
				table_key: 'bb_substation',
				valueFields: {
					substation_id: 'mRID',
					substation_name: 'name',
					substation_basevoltage_id: 'basevoltage',
					substation_basevoltage_name: 'basevoltage_name',
					zone_id: 'zone',
					zone_name: 'zone_name',
				},
			},
		},
		{
			headerName: '电压等级ID',
			field: 'basevoltage_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '电压等级名称',
			field: 'basevoltage_name',
			editable: true,
			filter: 'agTextColumnFilter',
			cellRenderer: GeneralPopup,
			cellRendererParams: {
				table_key: 'bb_basevoltage',
				valueFields: {
					basevoltage_id: 'mRID',
					basevoltage_name: 'name',
				},
			},
		},
		{
			headerName: '所属区域ID',
			field: 'zone_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '所属区域名称',
			field: 'zone_name',
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '厂站电压等级ID',
			field: 'substation_basevoltage_id',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '厂站电压等级名称',
			field: 'substation_basevoltage_name',
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '发电集团ID',
			field: 'group',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '机组设备ID',
			field: 'unit_id',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '甘肃分区',
			field: 'region',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '是否建模',
			field: 'if_model',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'),
		},
		{
			headerName: '厂用电率(%)',
			field: 'SelfUseRatio',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '厂用电负荷(MW)',
			field: 'SelfUseLoad',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
	],
	bb_unit_param: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '机组ID',
			field: 'unit_id',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: '是否考虑爬坡约束',
			field: 'if_con_ramp',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'),
		},
		{
			headerName: '允许爬坡偏差比例(%)',
			field: 'ramp_error',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '是否考虑电量约束',
			field: 'if_con_energy',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'),
		},
		{
			headerName: '允许电量偏差比例(%)',
			field: 'energy_error',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '是否与预状态出力衔接',
			field: 'if_con_init_power',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'),
		},
		{
			headerName: '是否避开振动区',
			field: 'if_con_vibration',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'),
		},
		{
			headerName: '是否考虑磨煤机约束',
			field: 'if_con_triturator',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'),
		},
		{
			headerName: '是否限制启停次数',
			field: 'if_con_up_dn_times',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'),
		},
		{
			headerName: '允许启停次数',
			field: 'allow_up_dn_times',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '是否考虑启动费用',
			field: 'if_con_start_cost',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'),
		},
		{
			headerName: '是否考虑空载费用',
			field: 'if_con_noload_cost',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'),
		},
		{
			headerName: '是否考虑最小启停时间',
			field: 'if_con_min_on_off_tm',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'),
		},
		{
			headerName: '最小运行时间(小时)',
			field: 'min_on_time',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '最小停机时间(小时)',
			field: 'min_off_time',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '是否考虑启机曲线',
			field: 'if_con_start_curve',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'),
		},
		{
			headerName: '是否考虑停机曲线',
			field: 'if_con_shut_curve',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'),
		},
		{
			headerName: '是否正旋转备用',
			field: 'if_con_pos_rsv_spin',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'),
		},
		{
			headerName: '是否负旋转备用',
			field: 'if_con_neg_rsv_spin',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'),
		},
		{
			headerName: '是否正AGC备用',
			field: 'if_con_pos_rsv_agc',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'),
		},
		{
			headerName: '是否负AGC备用',
			field: 'if_con_neg_rsv_agc',
			editable: true,
			...getDictConfig(dictConfig, 'isBool'),
		},
		{
			headerName: '最小发电能力',
			field: 'min_p_capacity',
			editable: true,
			filter: 'agTextColumnFilter',
		},
	],
	bb_unit_connect_bus: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '机组ID',
			field: 'unit_id',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: '时段',
			field: 'period_id',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '母线ID',
			field: 'bus_id',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '发电占比(%)',
			field: 'factor',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '连接类型',
			field: 'type',
			editable: true,
			filter: 'agTextColumnFilter',
		},
	],
	bb_unit_initial_state: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '机组ID',
			field: 'unit_id',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '初始状态',
			field: 'initial_state',
			editable: true,
			...getDictConfig(dictConfig, 'InitialState'),
		},
		{
			headerName: '状态保持时间(小时)',
			field: 'keep_time',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
		{
			headerName: '有功出力值(MW)',
			field: 'power',
			editable: true,
			filter: 'agNumberColumnFilter',
		},
	],
	bb_unit_offer_start: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '机组ID',
			field: 'unit_id',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: '热态启动报价',
			field: 'start_cost_hot',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '温态启动报价',
			field: 'start_cost_warm',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '冷态启动报价',
			field: 'start_cost_cold',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '停机至温态时间',
			field: 'time_to_warm',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '停机至冷态时间',
			field: 'time_to_cold',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '空载费用报价',
			field: 'no_load_cost',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '最小发电成本',
			field: 'pmin_cost',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
	],
	bb_unit_power_range: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '机组ID',
			field: 'unit_id',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: '时段',
			field: 'period_id',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '最大有功出力',
			field: 'max_power',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '最小有功出力',
			field: 'min_power',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
	],
	bb_unit_ramp_capability: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '机组ID',
			field: 'unit_id',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: '正常上升速率',
			field: 'inc_rate',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: '正常下降速率',
			field: 'dec_rate',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: '紧急上升速率',
			field: 'inc_rate_emg',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: '紧急下降速率',
			field: 'dec_rate_emg',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: 'AGC上升速率',
			field: 'inc_rate_agc',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: 'AGC下降速率',
			field: 'dec_rate_agc',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: '机组调峰上升速率',
			field: 'unit_peak_ramp_up',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: '机组调峰下降速率',
			field: 'unit_peak_ramp_down',
			filter: 'agTextColumnFilter',
			editable: true,
		},
	],
	bb_user_basic: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '用户ID',
			field: 'user_id',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: '用户名称',
			field: 'user_name',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: '用户类型',
			field: 'type',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: '用户所属甘肃分区',
			field: 'region',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: '报装容量',
			field: 'max_capacity',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '正常上升速率',
			field: 'inc_rate',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '正常下降速率',
			field: 'dec_rate',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '是否关联自备机组',
			field: 'if_link_unit',
			...getDictConfig(dictConfig, 'isBool'),
			editable: true,
		},
		{
			headerName: '关联自备机组id',
			field: 'link_unit_id',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
	],
	bb_user_node_factor: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '用户ID',
			field: 'user_id',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: '时段',
			field: 'period_id',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '母线ID',
			field: 'bus_id',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: '分配因子',
			field: 'factor',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
	],
	bb_elc_bus_basic: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '母线ID',
			field: 'elc_bus_id',
			editable: true,
			filter: 'agNumberColumnFilter',
			cellEditor: 'agNumberCellEditor',
		},
		{
			headerName: '负荷名称',
			field: 'elc_bus_name',
			editable: true,
			filter: 'agTextColumnFilter',
		},
		{
			headerName: '负荷设备对应CIM模型ID',
			field: 'cim_id',
			editable: true,
			filter: 'agTextColumnFilter',
		},
	],
	bb_bus_load_forecast: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '负荷ID',
			field: 'load_id',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: '时段',
			field: 'period_id',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '母线有功负荷预测',
			field: 'load_forecast',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '电气岛',
			field: 'island',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
	],
	bb_line_limit: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '线路ID',
			field: 'line_id',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: '正向限额',
			field: 'pos_trans_limit',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '负向限额',
			field: 'neg_trans_limit',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '时段ID',
			field: 'period_id',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
	],
	bb_node_sensitivity: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '节点ID',
			field: 'node',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: '线路ID',
			field: 'line_id',
			filter: 'agTextColumnFilter',
			editable: true,
		},
		{
			headerName: '灵敏度',
			field: 'sens',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
	],
	bb_system_load: [
		{
			headerName: '主键ID',
			field: 'ubid',
			filter: 'agTextColumnFilter',
			hide: true,
		},
		{
			headerName: '时段',
			field: 'period_id',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '系统有功负荷预测',
			field: 'system_load',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '非市场用户负荷',
			field: 'non_market_load',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '系统正备用容量下限',
			field: 'min_pos_rsv',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '系统正备用容量上限',
			field: 'max_pos_rsv',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '系统负备用容量下限',
			field: 'min_neg_rsv',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '系统正旋转备用容量下限',
			field: 'min_pos_rsv_spin',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '系统负旋转备用容量下限',
			field: 'min_neg_rsv_spin',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: 'AGC正备用容量下限',
			field: 'min_pos_rsv_agc',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: 'AGC负备用容量下限',
			field: 'min_neg_rsv_agc',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '风电预测用置信因子',
			field: 'winter_forecast_factor',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
		{
			headerName: '光伏预测用置信因子',
			field: 'solar_forecast_factor',
			filter: 'agNumberColumnFilter',
			editable: true,
		},
	],
};
