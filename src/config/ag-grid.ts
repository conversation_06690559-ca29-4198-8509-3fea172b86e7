import { LicenseManager } from 'ag-grid-enterprise';
LicenseManager.setLicenseKey(import.meta.env.VITE_GRID_LICENSE_KEY);

import { themeQuartz, iconSetQuartzLight } from 'ag-grid-community';

import { AG_GRID_LOCALE_CN } from '@ag-grid-community/locale';
// 自定义主题配置
export const myTheme = themeQuartz.withPart(iconSetQuartzLight).withParams({
	accentColor: '#087AD1',
	backgroundColor: '#FFFFFF',
	borderColor: '#D4D4D4',
	borderRadius: 2,
	browserColorScheme: 'light',
	cellHorizontalPaddingScale: 0.7,
	cellTextColor: '#0E1B37',
	chromeBackgroundColor: '#EBEFF4',
	columnBorder: true,
	fontFamily: ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Oxygen-Sans', 'Ubuntu', 'Cantarell', 'Helvetica Neue', 'sans-serif'],
	fontSize: 14,
	foregroundColor: '#555B62',
	headerBackgroundColor: '#EBEFF4',
	headerFontSize: 14,
	headerFontWeight: 600,
	headerRowBorder: true,
	headerTextColor: '#0E1B37',
	oddRowBackgroundColor: '#F7F8FB',
	rowBorder: true,
	rowHeight: 32,
	rowVerticalPaddingScale: 0.8,
	sidePanelBorder: false,
	spacing: 6,
	wrapperBorder: false,
	wrapperBorderRadius: 2,
});

export const localeText = AG_GRID_LOCALE_CN; // 本地化文本

// 前缀键，可能用于生成唯一标识符
export const prefixKey = 'sssssssid';

//https://www.ag-grid.com/vue-data-grid/row-selection-multi-row/
export const selectionColumnDef = {
	headerName: '选择',
	field: 'selected',
	width: 80,
	maxWidth: 80,
	pinned: 'left',
	cellStyle: { textAlign: 'center' },
	headerStyle: { textAlign: 'center' },
};

// 序号列
export const SERIAL_NUMBER = {
	headerName: '序号',
	field: 'SERIAL_NUMBER',
	valueGetter: (params: Record<string, any>) => params.node.rowIndex + 1, // 使用 rowIndex + 1 作为序号
	width: 75,
	minWidth: 75,
	sortable: false,
	filter: false,
	resizable: false,
	align: 'center',
	// pinned: 'left',
	cellStyle: { textAlign: 'center' },
	headerStyle: { textAlign: 'center' },
};

// 侧边栏配置  https://www.ag-grid.com/vue-data-grid/side-bar/
export const sideBar = {
	toolPanels: [
		{
			id: 'columns', // 列面板ID
			labelDefault: 'Columns', // 默认标签
			labelKey: 'columns', // 标签键
			iconKey: 'columns', // 图标键
			toolPanel: 'agColumnsToolPanel', // 使用的工具面板
			// https://www.ag-grid.com/vue-data-grid/tool-panel-columns/
			toolPanelParams: {
				suppressRowGroups: true,
				suppressValues: true,
				suppressPivots: true,
			},
		},
		{
			id: 'filters', // 过滤器面板ID
			labelDefault: 'Filters', // 默认标签
			labelKey: 'filters', // 标签键
			iconKey: 'filter', // 图标键
			toolPanel: 'agFiltersToolPanel', // 使用的工具面板
		},
	],
	defaultToolPanel: '', // 默认工具面板
};

// 默认列配置 https://www.ag-grid.com/vue-data-grid/column-properties/
export const defaultColDef = {
	sortable: true, // 启用排序
	filter: true, // 启用过滤
	flex: 1, // 自适应列宽
	minWidth: 200, // 最小列宽
	// floatingFilter: true, // 启用浮动筛选器
	enableValue: true, // 启用值
	enablePivot: true, // 启用透视
	filterParams: {
		buttons: ['apply', 'reset'],
		closeOnApply: true,
	},
};

export function getDictConfig(dictConfig: Recordable, key: string) {
	return {
		filter: 'agSetColumnFilter',
		cellEditor: 'agRichSelectCellEditor',
		filterParams: {
			values: Object.keys(dictConfig[key] || {}),
			valueFormatter: (params: Recordable) => {
				return dictConfig[key]?.[params.value];
			},
		},
		cellEditorParams: {
			values: Object.keys(dictConfig[key] || {}),
		},
		valueFormatter: (params: Recordable) => {
			return dictConfig[key]?.[params.value];
		},
	};
}
