<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4698069" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xec6b;</span>
                <div class="name">电子表格</div>
                <div class="code-name">&amp;#xec6b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61e;</span>
                <div class="name">地理接线图</div>
                <div class="code-name">&amp;#xe61e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe669;</span>
                <div class="name">潮流计算</div>
                <div class="code-name">&amp;#xe669;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe668;</span>
                <div class="name">灵敏度计算</div>
                <div class="code-name">&amp;#xe668;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe616;</span>
                <div class="name">系统</div>
                <div class="code-name">&amp;#xe616;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62c;</span>
                <div class="name">结果</div>
                <div class="code-name">&amp;#xe62c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62e;</span>
                <div class="name">爬坡</div>
                <div class="code-name">&amp;#xe62e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62b;</span>
                <div class="name">配置</div>
                <div class="code-name">&amp;#xe62b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe626;</span>
                <div class="name">故障集</div>
                <div class="code-name">&amp;#xe626;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe627;</span>
                <div class="name">静态安全分析</div>
                <div class="code-name">&amp;#xe627;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe629;</span>
                <div class="name">调度员潮流</div>
                <div class="code-name">&amp;#xe629;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62a;</span>
                <div class="name">结果</div>
                <div class="code-name">&amp;#xe62a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61c;</span>
                <div class="name">备用</div>
                <div class="code-name">&amp;#xe61c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61d;</span>
                <div class="name">加载文件</div>
                <div class="code-name">&amp;#xe61d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61b;</span>
                <div class="name">验证</div>
                <div class="code-name">&amp;#xe61b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe617;</span>
                <div class="name">调屏</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe618;</span>
                <div class="name">模型</div>
                <div class="code-name">&amp;#xe618;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe619;</span>
                <div class="name">爬坡</div>
                <div class="code-name">&amp;#xe619;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe631;</span>
                <div class="name">4-4 独立</div>
                <div class="code-name">&amp;#xe631;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe615;</span>
                <div class="name">联合</div>
                <div class="code-name">&amp;#xe615;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe623;</span>
                <div class="name">系统管理</div>
                <div class="code-name">&amp;#xe623;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe614;</span>
                <div class="name">验证码</div>
                <div class="code-name">&amp;#xe614;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe612;</span>
                <div class="name">日前市场用户</div>
                <div class="code-name">&amp;#xe612;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe613;</span>
                <div class="name">机组</div>
                <div class="code-name">&amp;#xe613;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe611;</span>
                <div class="name">计算</div>
                <div class="code-name">&amp;#xe611;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60f;</span>
                <div class="name">机组</div>
                <div class="code-name">&amp;#xe60f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe610;</span>
                <div class="name">用户</div>
                <div class="code-name">&amp;#xe610;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61a;</span>
                <div class="name">返回</div>
                <div class="code-name">&amp;#xe61a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe936;</span>
                <div class="name">保存</div>
                <div class="code-name">&amp;#xe936;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe675;</span>
                <div class="name">fullscreen-expand</div>
                <div class="code-name">&amp;#xe675;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe923;</span>
                <div class="name">icon_tuichuquanping</div>
                <div class="code-name">&amp;#xe923;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe607;</span>
                <div class="name">配置</div>
                <div class="code-name">&amp;#xe607;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe608;</span>
                <div class="name">市场规则</div>
                <div class="code-name">&amp;#xe608;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe609;</span>
                <div class="name">结算</div>
                <div class="code-name">&amp;#xe609;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60a;</span>
                <div class="name">智能体配罟</div>
                <div class="code-name">&amp;#xe60a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60b;</span>
                <div class="name">辅助服务市场</div>
                <div class="code-name">&amp;#xe60b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60c;</span>
                <div class="name">计算</div>
                <div class="code-name">&amp;#xe60c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60d;</span>
                <div class="name">日前市场</div>
                <div class="code-name">&amp;#xe60d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60e;</span>
                <div class="name">出清结果</div>
                <div class="code-name">&amp;#xe60e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe602;</span>
                <div class="name">icon_市场设计</div>
                <div class="code-name">&amp;#xe602;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe603;</span>
                <div class="name">icon_市场结算</div>
                <div class="code-name">&amp;#xe603;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe604;</span>
                <div class="name">icon_市场出清</div>
                <div class="code-name">&amp;#xe604;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe605;</span>
                <div class="name">icon_辅助分析</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe606;</span>
                <div class="name">icon_仿真数据</div>
                <div class="code-name">&amp;#xe606;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">icon_智能试验</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe601;</span>
                <div class="name">icon_情景包</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1749048918368') format('woff2'),
       url('iconfont.woff?t=1749048918368') format('woff'),
       url('iconfont.ttf?t=1749048918368') format('truetype'),
       url('iconfont.svg?t=1749048918368#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-liebiao"></span>
            <div class="name">
              电子表格
            </div>
            <div class="code-name">.icon-liebiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-sutpc-ditufuwuqiguanli_huaban1"></span>
            <div class="name">
              地理接线图
            </div>
            <div class="code-name">.icon-sutpc-ditufuwuqiguanli_huaban1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-CH-xiangmuneirong"></span>
            <div class="name">
              潮流计算
            </div>
            <div class="code-name">.icon-CH-xiangmuneirong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-hanshu"></span>
            <div class="name">
              灵敏度计算
            </div>
            <div class="code-name">.icon-hanshu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xitong"></span>
            <div class="name">
              系统
            </div>
            <div class="code-name">.icon-xitong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jieguo1"></span>
            <div class="name">
              结果
            </div>
            <div class="code-name">.icon-jieguo1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-papo3"></span>
            <div class="name">
              爬坡
            </div>
            <div class="code-name">.icon-papo3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-peizhi"></span>
            <div class="name">
              配置
            </div>
            <div class="code-name">.icon-peizhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-guzhangji"></span>
            <div class="name">
              故障集
            </div>
            <div class="code-name">.icon-guzhangji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jingtaianquanfenxi"></span>
            <div class="name">
              静态安全分析
            </div>
            <div class="code-name">.icon-jingtaianquanfenxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tiaoduyuanchaoliu"></span>
            <div class="name">
              调度员潮流
            </div>
            <div class="code-name">.icon-tiaoduyuanchaoliu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jieguo"></span>
            <div class="name">
              结果
            </div>
            <div class="code-name">.icon-jieguo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-beiyong"></span>
            <div class="name">
              备用
            </div>
            <div class="code-name">.icon-beiyong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiazaiwenjian"></span>
            <div class="name">
              加载文件
            </div>
            <div class="code-name">.icon-jiazaiwenjian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yanzheng"></span>
            <div class="name">
              验证
            </div>
            <div class="code-name">.icon-yanzheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tiaoping"></span>
            <div class="name">
              调屏
            </div>
            <div class="code-name">.icon-tiaoping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-moxing"></span>
            <div class="name">
              模型
            </div>
            <div class="code-name">.icon-moxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-papo"></span>
            <div class="name">
              爬坡
            </div>
            <div class="code-name">.icon-papo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-44duli"></span>
            <div class="name">
              4-4 独立
            </div>
            <div class="code-name">.icon-44duli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lianhe"></span>
            <div class="name">
              联合
            </div>
            <div class="code-name">.icon-lianhe
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xitongguanli"></span>
            <div class="name">
              系统管理
            </div>
            <div class="code-name">.icon-xitongguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yanzhengma"></span>
            <div class="name">
              验证码
            </div>
            <div class="code-name">.icon-yanzhengma
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-riqianshichangyonghu"></span>
            <div class="name">
              日前市场用户
            </div>
            <div class="code-name">.icon-riqianshichangyonghu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-riqianshichangjizu"></span>
            <div class="name">
              机组
            </div>
            <div class="code-name">.icon-riqianshichangjizu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jisuan"></span>
            <div class="name">
              计算
            </div>
            <div class="code-name">.icon-jisuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jizu"></span>
            <div class="name">
              机组
            </div>
            <div class="code-name">.icon-jizu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yonghu"></span>
            <div class="name">
              用户
            </div>
            <div class="code-name">.icon-yonghu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fanhui"></span>
            <div class="name">
              返回
            </div>
            <div class="code-name">.icon-fanhui
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-baocun"></span>
            <div class="name">
              保存
            </div>
            <div class="code-name">.icon-baocun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fullscreen-expand"></span>
            <div class="name">
              fullscreen-expand
            </div>
            <div class="code-name">.icon-fullscreen-expand
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_tuichuquanping"></span>
            <div class="name">
              icon_tuichuquanping
            </div>
            <div class="code-name">.icon-icon_tuichuquanping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-weibiaoti-2_huaban1fuben"></span>
            <div class="name">
              配置
            </div>
            <div class="code-name">.icon-a-weibiaoti-2_huaban1fuben
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-weibiaoti-2_huaban1fuben5"></span>
            <div class="name">
              市场规则
            </div>
            <div class="code-name">.icon-a-weibiaoti-2_huaban1fuben5
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-weibiaoti-2_huaban1fuben6"></span>
            <div class="name">
              结算
            </div>
            <div class="code-name">.icon-a-weibiaoti-2_huaban1fuben6
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-weibiaoti-2_huaban1fuben4"></span>
            <div class="name">
              智能体配罟
            </div>
            <div class="code-name">.icon-a-weibiaoti-2_huaban1fuben4
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-weibiaoti-2_huaban1"></span>
            <div class="name">
              辅助服务市场
            </div>
            <div class="code-name">.icon-a-weibiaoti-2_huaban1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-computing"></span>
            <div class="name">
              计算
            </div>
            <div class="code-name">.icon-a-computing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-weibiaoti-2_huaban1fuben2"></span>
            <div class="name">
              日前市场
            </div>
            <div class="code-name">.icon-a-weibiaoti-2_huaban1fuben2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-weibiaoti-2_huaban1fuben3"></span>
            <div class="name">
              出清结果
            </div>
            <div class="code-name">.icon-a-weibiaoti-2_huaban1fuben3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_shichangsheji"></span>
            <div class="name">
              icon_市场设计
            </div>
            <div class="code-name">.icon-icon_shichangsheji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_shichangjiesuan"></span>
            <div class="name">
              icon_市场结算
            </div>
            <div class="code-name">.icon-icon_shichangjiesuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_shichangchuqing"></span>
            <div class="name">
              icon_市场出清
            </div>
            <div class="code-name">.icon-icon_shichangchuqing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_fuzhufenxi"></span>
            <div class="name">
              icon_辅助分析
            </div>
            <div class="code-name">.icon-icon_fuzhufenxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_fangzhenshuju"></span>
            <div class="name">
              icon_仿真数据
            </div>
            <div class="code-name">.icon-icon_fangzhenshuju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_zhinengshiyan"></span>
            <div class="name">
              icon_智能试验
            </div>
            <div class="code-name">.icon-icon_zhinengshiyan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_qingjingbao"></span>
            <div class="name">
              icon_情景包
            </div>
            <div class="code-name">.icon-icon_qingjingbao
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-liebiao"></use>
                </svg>
                <div class="name">电子表格</div>
                <div class="code-name">#icon-liebiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-sutpc-ditufuwuqiguanli_huaban1"></use>
                </svg>
                <div class="name">地理接线图</div>
                <div class="code-name">#icon-sutpc-ditufuwuqiguanli_huaban1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-CH-xiangmuneirong"></use>
                </svg>
                <div class="name">潮流计算</div>
                <div class="code-name">#icon-CH-xiangmuneirong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-hanshu"></use>
                </svg>
                <div class="name">灵敏度计算</div>
                <div class="code-name">#icon-hanshu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xitong"></use>
                </svg>
                <div class="name">系统</div>
                <div class="code-name">#icon-xitong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jieguo1"></use>
                </svg>
                <div class="name">结果</div>
                <div class="code-name">#icon-jieguo1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-papo3"></use>
                </svg>
                <div class="name">爬坡</div>
                <div class="code-name">#icon-papo3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-peizhi"></use>
                </svg>
                <div class="name">配置</div>
                <div class="code-name">#icon-peizhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-guzhangji"></use>
                </svg>
                <div class="name">故障集</div>
                <div class="code-name">#icon-guzhangji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jingtaianquanfenxi"></use>
                </svg>
                <div class="name">静态安全分析</div>
                <div class="code-name">#icon-jingtaianquanfenxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tiaoduyuanchaoliu"></use>
                </svg>
                <div class="name">调度员潮流</div>
                <div class="code-name">#icon-tiaoduyuanchaoliu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jieguo"></use>
                </svg>
                <div class="name">结果</div>
                <div class="code-name">#icon-jieguo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-beiyong"></use>
                </svg>
                <div class="name">备用</div>
                <div class="code-name">#icon-beiyong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiazaiwenjian"></use>
                </svg>
                <div class="name">加载文件</div>
                <div class="code-name">#icon-jiazaiwenjian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yanzheng"></use>
                </svg>
                <div class="name">验证</div>
                <div class="code-name">#icon-yanzheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tiaoping"></use>
                </svg>
                <div class="name">调屏</div>
                <div class="code-name">#icon-tiaoping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-moxing"></use>
                </svg>
                <div class="name">模型</div>
                <div class="code-name">#icon-moxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-papo"></use>
                </svg>
                <div class="name">爬坡</div>
                <div class="code-name">#icon-papo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-44duli"></use>
                </svg>
                <div class="name">4-4 独立</div>
                <div class="code-name">#icon-44duli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lianhe"></use>
                </svg>
                <div class="name">联合</div>
                <div class="code-name">#icon-lianhe</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xitongguanli"></use>
                </svg>
                <div class="name">系统管理</div>
                <div class="code-name">#icon-xitongguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yanzhengma"></use>
                </svg>
                <div class="name">验证码</div>
                <div class="code-name">#icon-yanzhengma</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-riqianshichangyonghu"></use>
                </svg>
                <div class="name">日前市场用户</div>
                <div class="code-name">#icon-riqianshichangyonghu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-riqianshichangjizu"></use>
                </svg>
                <div class="name">机组</div>
                <div class="code-name">#icon-riqianshichangjizu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jisuan"></use>
                </svg>
                <div class="name">计算</div>
                <div class="code-name">#icon-jisuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jizu"></use>
                </svg>
                <div class="name">机组</div>
                <div class="code-name">#icon-jizu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yonghu"></use>
                </svg>
                <div class="name">用户</div>
                <div class="code-name">#icon-yonghu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fanhui"></use>
                </svg>
                <div class="name">返回</div>
                <div class="code-name">#icon-fanhui</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-baocun"></use>
                </svg>
                <div class="name">保存</div>
                <div class="code-name">#icon-baocun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fullscreen-expand"></use>
                </svg>
                <div class="name">fullscreen-expand</div>
                <div class="code-name">#icon-fullscreen-expand</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_tuichuquanping"></use>
                </svg>
                <div class="name">icon_tuichuquanping</div>
                <div class="code-name">#icon-icon_tuichuquanping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-weibiaoti-2_huaban1fuben"></use>
                </svg>
                <div class="name">配置</div>
                <div class="code-name">#icon-a-weibiaoti-2_huaban1fuben</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-weibiaoti-2_huaban1fuben5"></use>
                </svg>
                <div class="name">市场规则</div>
                <div class="code-name">#icon-a-weibiaoti-2_huaban1fuben5</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-weibiaoti-2_huaban1fuben6"></use>
                </svg>
                <div class="name">结算</div>
                <div class="code-name">#icon-a-weibiaoti-2_huaban1fuben6</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-weibiaoti-2_huaban1fuben4"></use>
                </svg>
                <div class="name">智能体配罟</div>
                <div class="code-name">#icon-a-weibiaoti-2_huaban1fuben4</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-weibiaoti-2_huaban1"></use>
                </svg>
                <div class="name">辅助服务市场</div>
                <div class="code-name">#icon-a-weibiaoti-2_huaban1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-computing"></use>
                </svg>
                <div class="name">计算</div>
                <div class="code-name">#icon-a-computing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-weibiaoti-2_huaban1fuben2"></use>
                </svg>
                <div class="name">日前市场</div>
                <div class="code-name">#icon-a-weibiaoti-2_huaban1fuben2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-weibiaoti-2_huaban1fuben3"></use>
                </svg>
                <div class="name">出清结果</div>
                <div class="code-name">#icon-a-weibiaoti-2_huaban1fuben3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_shichangsheji"></use>
                </svg>
                <div class="name">icon_市场设计</div>
                <div class="code-name">#icon-icon_shichangsheji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_shichangjiesuan"></use>
                </svg>
                <div class="name">icon_市场结算</div>
                <div class="code-name">#icon-icon_shichangjiesuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_shichangchuqing"></use>
                </svg>
                <div class="name">icon_市场出清</div>
                <div class="code-name">#icon-icon_shichangchuqing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_fuzhufenxi"></use>
                </svg>
                <div class="name">icon_辅助分析</div>
                <div class="code-name">#icon-icon_fuzhufenxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_fangzhenshuju"></use>
                </svg>
                <div class="name">icon_仿真数据</div>
                <div class="code-name">#icon-icon_fangzhenshuju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_zhinengshiyan"></use>
                </svg>
                <div class="name">icon_智能试验</div>
                <div class="code-name">#icon-icon_zhinengshiyan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_qingjingbao"></use>
                </svg>
                <div class="name">icon_情景包</div>
                <div class="code-name">#icon-icon_qingjingbao</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
