.tableStyleClass {
    height: 100%;
    .el-table__empty-block{
        width: 100% !important;
    }
    .el-table {
        height: 100%;
        th.el-table__cell {
            color: #091e40;
            background: #eef1f6;
            padding: 5px 0 !important;
            position: relative;

            .cell {
                padding-right: 13px;
            }
        }

        td.el-table__cell,
        th.el-table__cell.is-leaf {
            border-bottom: 1px solid #d9d9d9;
        }

        .caret-wrapper {
            position: absolute;
            top: 10px;
        }
    }

    .el-table--border .el-table__cell,
    .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
        border-right: 1px solid #d9d9d9;
        color: #091e40;
    }

    .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
        background: #f8f9fc;
        color: #091e40;
    }

    .el-table--border,
    .el-table--group {
        border: 1px solid #d9d9d9;
    }

    .el-table--mini .el-table__cell {
        padding: 2px 0;
        position: relative;
    }
}
