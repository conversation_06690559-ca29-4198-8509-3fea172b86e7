interface NavGroup {
	title?: string;
	type: 'simple' | 'composite';
	items: {
		key: string;
		label: string;
		icon?: string;
		iconSize?: number;
		type?: 'page' | 'action';
		subItems?: {
			key: string;
			label: string;
			icon: string;
			iconSize?: number;
			hasDropdown?: boolean;
			type?: 'page' | 'action';
			disabled?: boolean;
			loading?: boolean;
			dropdownItems?: {
				key: string;
				label: string;
				icon?: string;
				iconSize?: number;
				type?: 'page' | 'action';
				disabled?: boolean;
			}[];
		}[];
	}[];
}
