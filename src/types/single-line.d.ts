// 坐标点接口
interface Point {
	x: number;
	y: number;
}

interface SingleLineNode {
	key: string;
	category: string;
	name: string;
	type: string;
	color: string;
	pos: [number, number];
	posX?: number;
	posY?: number;
	angle: number;
	width?: number;
	isTemplate?: boolean;
	[key: string]: any;
}

interface PaletteDroppedEvent {
	nodeData: SingleLineNode;
	position: go.Point;
}

interface SingleLineLink {
	key?: string; // 连线唯一标识
	from: string; // 起点节点ID
	to: string; // 终点节点ID
	fromPort?: string; // 起点端口ID
	toPort?: string; // 终点端口ID
	points?: go.Point[]; // 路径点
	voltage?: string; // 电压等级
	color?: string; // 线路颜色
	properties?: any; // 连线属性数据
	[key: string]: any; // 允许其他扩展属性
}

// 定义端口信息
interface PortInfo {
	id: string; // 端口ID
	alignment: go.Spot; // 端口位置
	fromSpot?: go.Spot; // 连出方向
	toSpot?: go.Spot; // 连入方向
	fromLinkable?: boolean; // 是否可以作为连出端口
	toLinkable?: boolean; // 是否可以作为连入端口
	fromMaxLinks?: number; // 最大可连出线数量
	toMaxLinks?: number; // 最大可连入线数量
}

interface IconInfo {
	path: string;
	width: number;
	height: number;
	name: string;
	type: string;
	strokeWidth: number;
	defaultColor: string;
	ports?: PortInfo[]; // 添加端口配置
}

interface IconsCollection {
	[key: string]: IconInfo;
}

// 圆环配置接口
interface CircleConfig {
	diameter: number;
	stroke: string;
	strokeWidth: number;
}

// 电压等级配置接口
interface VoltageConfig {
	name: string; // 电压等级名称
	key: string; // 搜索用的关键字
	color: string; // 主要颜色
	circles: CircleConfig[]; // 圆环配置
	lineWidth?: number; // 连接线宽度
}

// 平行线路配置
interface ParallelLineConfig {
	segmentFraction: number; // 线上元素位置比例
}

// 平行线路配置对象
interface ParallelLinesConfig {
	[lineCount: number]: {
		// 线路总数
		[lineIndex: number]: ParallelLineConfig; // 每条线的配置
	};
}

// 端口配置接口 - 扩展现有的PortInfo接口
interface PortConfig {
	id: string;
	alignment: go.Spot;
	fromSpot?: go.Spot;
	toSpot?: go.Spot;
	fromLinkable?: boolean;
	toLinkable?: boolean;
	fromMaxLinks?: number;
	toMaxLinks?: number;
}

// 组件图标接口 - 扩展现有的IconInfo接口
interface ComponentIcon {
	type: string;
	name: string;
	path: string;
	defaultColor: string;
	strokeWidth: number;
	width: number;
	height: number;
	ports?: PortConfig[];
}

// 右键菜单项接口
interface ContextMenuItem {
	text: string;
	icon?: string;
	action: (e: go.InputEvent, obj: go.GraphObject) => void;
	disabled?: (e: go.InputEvent, obj: go.GraphObject) => boolean;
	divider?: boolean;
}

// 节点模板配置接口
interface NodeTemplateConfig {
	type: string;
	template: go.Node;
	contextMenuItems?: ContextMenuItem[];
}

// 电压等级样式接口
interface VoltageStyle {
	color: string;
	strokeWidth: number;
}
